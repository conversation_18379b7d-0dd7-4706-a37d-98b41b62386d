<?php

return [

    'test'     => 'test-en',
    'Dashboard'     => 'Dashboard',
    'Overview'      => 'Overview',
    'Add_time'      => 'Add Time',
    'Workplaces'   => 'Workplaces',
    'Worker'  => 'Worker',
    'WORKER'  => 'Worker',
    'ADMIN'  => 'ADMIN',
    'reason'  => 'Reason',
    'Timesheet'      => 'Timesheet',
    'Information'   => 'Information',
    'Logout'        => 'Logout',
    'workplaces-list' => 'Workplaces List',
    'add-workplace' => 'Add Workplace',
    'company' => 'Company',
    'adresses' => 'Adresses',
    'action' => 'Action',
    'delete-selected' => 'Delete Selected',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'company-name' => 'Company Name',
    'enter-address' => 'Workplaces Address',
    'staff-number' => 'Staff Number',
    'name' => 'Name',
    'surname' => 'Surname',
    'work-time' => 'Work Time',
    'pause-time' => 'Pause Time',
    'last-login' => 'Last Login',
    'total' => 'Total',
    'action' => 'Action',
    'id' => 'ID',
    'add-worker' => 'Add Worker',
    'worker-list' => 'Worker List',
    'start-date' => 'Start Date',
    'end-date' => 'End Date',
    'save' => 'Save',
    'member' => 'Member',
    'supervisior' => 'Supervisior',
    'SUPERVISOR' => 'Supervisior',
    'administrator' => 'Administrator',
    'password' => 'Password',
    'add' => 'Add',
    'wo-worker' => 'Worker',
    'search' => 'Search',
    'workerplace' => 'Workplaces',
    'all' => 'All',
    'date' =>'Date',
    'start-time' => 'Start Time',
    'end-time' => 'End Time',
    'timesheet-list' => 'Timesheet List',
    'timesheet' => 'Timesheet',
    'missing-time' => 'Missing Time',
    'reason' => 'Information Worker ',
    'logout' => 'Logout',
    'time-sheet-system' => 'Time Sheet System',
    'wo-reason' => 'Information Worker',
    'select-date' => 'Select Date',
    'best-staff' => 'Best Staff',
    'best-workplaces' => 'Best Workplaces',
    'new-information' => 'New Information',
    'staff' => 'Staff',
    'Information-List' => 'Information List',
    'total_time'=>'Total Time',
    'best-worker' => 'Best Worker',
    'month'=>'Select Month ',
    'year'=>"Select Year",
    'January'=>"January",
    'February'=>"February",
    'March'=>"March",
    'April'=>"April",
    'May'=>"May",
    'June'=>"June",
    'July'=>"July",
    'August'=>"August",
    'September'=>"September",
    'October'=>"October",
    'November'=>"November",
    'December'=>"December",
    'mon'=>'Month',
    'ye'=>'Year',
    "disease"=>"Disease",
    "denotification"=>"Disease Notification",
    "name"=>"Worker",
    "of"=>"Of",
    "upto"=>"Up to and including",
    "submitted"=>"Submitted",
    "action"=>"Action",
    "holiday"=>"Holiday",
    "notification"=>"Holiday list",    
    "dns"=>"Disease, not submitted",
    "ds"=>"Disease, submitted",
    'shortByDate'=>'Sort by date',
    'shortByWorkplace'=>'Sort by workplaces',
    'jan'=>'January',
    'feb'=>'February',
    'mar'=>'March',
    'apr'=>'April',
    'may'=>'May',
    'jun'=>'June',
    'july'=>'July',
    'aug'=>'August',
    'sep'=>'September',
    'oct'=>'October',
    'nov'=>'November',
    'dec'=>'December',
    'allWorkers'=>'All Workers',
    'totalDays'=>'Total Days',
    'lock'=>'Lock',
    'unlock'=>'Unlock',
    'blockWorker'=>'Block Worker',
    'aresureBlock'=>'Are you sure want to block worker ?',
    'cancel'=>'Cancel',
    'block'=>'Block',
    
    'unblockWorker'=>'Unblock Worker',
    'aresureunBlock'=>'Are you sure want to unblock worker ?',
    'unblock'=>'Unblock',
    
    'office'=>'Office',
    'cleaner'=>'Building cleaning/cleaner',
    'glasss'=>'Construction cleaning/glass cleaning',
    'intern'=>'Intern',
    'position'=>'Position',
    
    'employment'=>'Employment',
    'minijob'=>'Mini-job',
    'part'=>'Part',
    'full'=>'Full',
    
    'workerData'=>'Worker Data',
    'workerNumber'=>'Worker number',
    'member'=>'Member',
    'contract'=>'Contract',
    'position'=>'Position',
    'pleaseSelect'=>'Please select...',
    'employment'=>'Employment',
    'startOfContract'=>'Start of contract',
    'endOfContract'=>'End of contract',
    'weekOfHours'=>'Weeks of hours',
    'hourlyWage'=>'Hourly wage',
    'totalHolidyas'=>'Holidays total days',
    'cancelContral'=>'Cancel to (date select)',
    'personalData'=>'Personal data',
    'gender'=>'Gender',
    'pleaseSelect'=>'Please select...',
    'firstName'=>'First name',
    'surName'=>'Surname',
    'dob'=>'Date of Birth',
    'placeOfBirth'=>'Place of Birth',
    'nationality'=>'Nationality',
    'pleaseSelect'=>'Please select...',
    'workpermit'=>'Work permit up',
    'resideancePermit'=>'Residence permit',
    'taxIdNumber'=>'Tax ID number',
    'socialSecurityNumber'=>'Social Security number',
    'email'=>'E-Mail Adresses',
    'password'=>'Password',
    'phoneNumber'=>'Phone number',
    'mobile'=>'Mobile',
    'addresses'=>'Adresses',
    'postCode'=>'Postcode / City',
    'paymentDetails'=>'Payment Details',
    'name'=>'Name',
    'bankName'=>'Bank name',
    'IBAN'=>'IBAN',
    'note'=>'Note',
    'fixedSalary'=>'Fixed salary',
    'create'=>'Create new worker and contract',
    'edit'=>'Edit worker details and contract',
    
    'dash_name'=>'Name',
    'dash_total_day'=>'Total days',
    'dash_taken'=>'Taken',
    'dash_remained'=>'Remained',
];
