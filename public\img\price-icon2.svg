<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 43.1 (39012) - http://www.bohemiancoding.com/sketch -->
    <title>Icon</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="1.72254642e-14%" y1="-1.11022302e-14%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FACC27" offset="0%"></stop>
            <stop stop-color="#F16911" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Source-Sans---White-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="P3---Pricing" transform="translate(-625.000000, -346.000000)">
            <g id="Plans" transform="translate(395.000000, 346.000000)">
                <g id="Plan-2" transform="translate(200.000000, 0.000000)">
                    <g id="Icon" transform="translate(30.000000, 0.000000)">
                        <rect id="Base" fill="url(#linearGradient-1)" x="0" y="0" width="40" height="40" rx="8"></rect>
                        <path d="M28,12 L12,12 L12,14 L28,14 L28,12 L28,12 Z M29,22 L29,20 L28,15 L12,15 L11,20 L11,22 L12,22 L12,28 L22,28 L22,22 L26,22 L26,28 L28,28 L28,22 L29,22 L29,22 Z M20,26 L14,26 L14,22 L20,22 L20,26 L20,26 Z" fill="#FFFFFF"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>