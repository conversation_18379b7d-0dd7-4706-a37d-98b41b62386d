<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 43.1 (39012) - http://www.bohemiancoding.com/sketch -->
    <title>Icon</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="101.999998%" y1="100.999999%" x2="6.24500451e-15%" y2="0%" id="linearGradient-1">
            <stop stop-color="#6758F3" offset="0%"></stop>
            <stop stop-color="#E061F5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Source-Sans---White-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="P3---Pricing" transform="translate(-1025.000000, -346.000000)">
            <g id="Plans" transform="translate(395.000000, 346.000000)">
                <g id="Plan-4" transform="translate(600.000000, 0.000000)">
                    <g id="Icon" transform="translate(30.000000, 0.000000)">
                        <rect id="Base" fill="url(#linearGradient-1)" x="0" y="0" width="40" height="40" rx="8"></rect>
                        <path d="M20,15 L20,11 L10,11 L10,29 L30,29 L30,15 L20,15 L20,15 Z M14,27 L12,27 L12,25 L14,25 L14,27 L14,27 Z M14,23 L12,23 L12,21 L14,21 L14,23 L14,23 Z M14,19 L12,19 L12,17 L14,17 L14,19 L14,19 Z M14,15 L12,15 L12,13 L14,13 L14,15 L14,15 Z M18,27 L16,27 L16,25 L18,25 L18,27 L18,27 Z M18,23 L16,23 L16,21 L18,21 L18,23 L18,23 Z M18,19 L16,19 L16,17 L18,17 L18,19 L18,19 Z M18,15 L16,15 L16,13 L18,13 L18,15 L18,15 Z M28,27 L20,27 L20,25 L22,25 L22,23 L20,23 L20,21 L22,21 L22,19 L20,19 L20,17 L28,17 L28,27 L28,27 Z M26,19 L24,19 L24,21 L26,21 L26,19 L26,19 Z M26,23 L24,23 L24,25 L26,25 L26,23 L26,23 Z" fill="#FFFFFF"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>