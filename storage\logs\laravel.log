[2025-06-10 14:08:58] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:08:58] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:08:59] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:08:59] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:00] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:00] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:01] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:03] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:03] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:03] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:05] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:05] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:06] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:06] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 18)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:06] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 18)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:07] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:08] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:08] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:09] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:10] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:10] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:11] local.ERROR: Symfony\Component\Debug\Exception\FatalThrowableError: Parse error: syntax error, unexpected 'Route' (T_STRING) in D:\xampp\htdocs\tms\routes\web.php:31
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(283): Illuminate\Routing\Router->loadRoutes('D:\\xampp\\htdocs...')
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(104): Illuminate\Routing\Router->group(Array, 'D:\\xampp\\htdocs...')
#2 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(56): Illuminate\Routing\RouteRegistrar->group('D:\\xampp\\htdocs...')
#3 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(40): App\Providers\RouteServiceProvider->mapWebRoutes()
#4 [internal function]: App\Providers\RouteServiceProvider->map()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(74): Illuminate\Container\Container->call(Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(33): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#11 D:\xampp\htdocs\tms\app\Providers\RouteServiceProvider.php(28): Illuminate\Foundation\Support\Providers\RouteServiceProvider->boot()
#12 [internal function]: App\Providers\RouteServiceProvider->boot()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(29): call_user_func_array(Array, Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(87): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(31): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Container\Container.php(539): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(788): Illuminate\Container\Container->call(Array)
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(771): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#19 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(App\Providers\RouteServiceProvider), 17)
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(772): array_walk(Array, Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(208): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(267): Illuminate\Foundation\Application->bootstrapWith(Array)
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(114): Illuminate\Foundation\Console\Kernel->bootstrap()
#25 Command line code(1): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 {main}  
[2025-06-10 14:09:12] local.ERROR: Symfony\Component\Console\Exception\RuntimeException: The "--json" option does not exist. in D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php:221
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(157): Symfony\Component\Console\Input\ArgvInput->addLongOption('json', NULL)
#1 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(80): Symfony\Component\Console\Input\ArgvInput->parseLongOption('--json')
#2 D:\xampp\htdocs\tms\vendor\symfony\console\Input\Input.php(55): Symfony\Component\Console\Input\ArgvInput->parse()
#3 D:\xampp\htdocs\tms\vendor\symfony\console\Command\Command.php(214): Symfony\Component\Console\Input\Input->bind(Object(Symfony\Component\Console\Input\InputDefinition))
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Console\Command.php(167): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#5 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(992): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#6 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(255): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Foundation\Console\RouteListCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#7 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(148): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(122): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#10 {main}  
[2025-06-10 14:09:13] local.ERROR: Symfony\Component\Console\Exception\RuntimeException: The "--json" option does not exist. in D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php:221
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(157): Symfony\Component\Console\Input\ArgvInput->addLongOption('json', NULL)
#1 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(80): Symfony\Component\Console\Input\ArgvInput->parseLongOption('--json')
#2 D:\xampp\htdocs\tms\vendor\symfony\console\Input\Input.php(55): Symfony\Component\Console\Input\ArgvInput->parse()
#3 D:\xampp\htdocs\tms\vendor\symfony\console\Command\Command.php(214): Symfony\Component\Console\Input\Input->bind(Object(Symfony\Component\Console\Input\InputDefinition))
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Console\Command.php(167): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#5 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(992): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#6 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(255): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Foundation\Console\RouteListCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#7 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(148): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(122): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#10 {main}  
[2025-06-10 14:09:13] local.ERROR: Symfony\Component\Console\Exception\RuntimeException: The "--json" option does not exist. in D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php:221
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(157): Symfony\Component\Console\Input\ArgvInput->addLongOption('json', NULL)
#1 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(80): Symfony\Component\Console\Input\ArgvInput->parseLongOption('--json')
#2 D:\xampp\htdocs\tms\vendor\symfony\console\Input\Input.php(55): Symfony\Component\Console\Input\ArgvInput->parse()
#3 D:\xampp\htdocs\tms\vendor\symfony\console\Command\Command.php(214): Symfony\Component\Console\Input\Input->bind(Object(Symfony\Component\Console\Input\InputDefinition))
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Console\Command.php(167): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#5 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(992): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#6 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(255): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Foundation\Console\RouteListCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#7 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(148): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(122): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#10 {main}  
[2025-06-10 14:09:18] local.ERROR: ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Unparenthesized...', 'D:\\xampp\\htdocs...', 86, Array)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include()
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#6 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#12 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#19 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#67 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#68 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#69 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 3)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#18 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#68 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 2)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#62 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#10 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 {main}  
[2025-06-10 14:09:33] local.ERROR: Symfony\Component\Console\Exception\RuntimeException: The "--json" option does not exist. in D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php:221
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(157): Symfony\Component\Console\Input\ArgvInput->addLongOption('json', NULL)
#1 D:\xampp\htdocs\tms\vendor\symfony\console\Input\ArgvInput.php(80): Symfony\Component\Console\Input\ArgvInput->parseLongOption('--json')
#2 D:\xampp\htdocs\tms\vendor\symfony\console\Input\Input.php(55): Symfony\Component\Console\Input\ArgvInput->parse()
#3 D:\xampp\htdocs\tms\vendor\symfony\console\Command\Command.php(214): Symfony\Component\Console\Input\Input->bind(Object(Symfony\Component\Console\Input\InputDefinition))
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Console\Command.php(167): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#5 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(992): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#6 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(255): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Foundation\Console\RouteListCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#7 D:\xampp\htdocs\tms\vendor\symfony\console\Application.php(148): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(122): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 D:\xampp\htdocs\tms\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#10 {main}  
[2025-06-10 14:09:44] local.ERROR: ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Unparenthesized...', 'D:\\xampp\\htdocs...', 86, Array)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include()
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#6 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#12 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#19 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#67 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#68 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#69 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 3)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#18 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#68 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 2)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\52f654568af11c1f00fef185555c02985ff972b0.php(156): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#62 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#10 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 {main}  
[2025-06-10 14:10:06] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Class 'Illuminate\Foundation\Auth\Admin' not found in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(571): include()
#1 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\xampp\\htdocs...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Admin')
#3 [internal function]: spl_autoload_call('App\\Admin')
#4 Command line code(1): class_exists('App\\Admin')
#5 [internal function]: {closure}('App\\Admin')
#6 Command line code(1): array_filter(Array, Object(Closure))
#7 {main}
  thrown in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 {main}  
[2025-06-10 14:10:07] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Call to undefined method Illuminate\Auth\Access\Gate::policies() in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 Command line code(1): Illuminate\Support\Facades\Facade::__callStatic('policies', Array)
#1 {main}
  thrown in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 {main}  
[2025-06-10 14:10:12] local.ERROR: ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Unparenthesized...', 'D:\\xampp\\htdocs...', 86, Array)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include()
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#6 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#12 D:\xampp\htdocs\tms\storage\framework\views\48ecc79504293b58542b4abd2d5cdcb437532792.php(745): Illuminate\View\View->render()
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#19 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#67 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#68 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#69 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 3)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\c057390177267144d12d4e87be5cf71c1c0eafcc.php(13): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\storage\framework\views\48ecc79504293b58542b4abd2d5cdcb437532792.php(745): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#18 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#65 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#66 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#68 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 2)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\storage\framework\views\48ecc79504293b58542b4abd2d5cdcb437532792.php(745): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(42): include('D:\\xampp\\htdocs...')
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#10 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#12 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#13 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#59 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#60 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#61 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#62 {main}

Next ErrorException: Unparenthesized `a ? b : c ? d : e` is deprecated. Use either `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)` (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) (View: D:\xampp\htdocs\tms\resources\views\layouts\include\leftpanel\admin-left-sidebar.blade.php) in D:\xampp\htdocs\tms\storage\framework\views\1f2cd4aeb80a107c4b7ddfe912cef63c04e0976f.php:86
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(44): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(59): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\xampp\\htdocs...', Array)
#2 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(137): Illuminate\View\Engines\CompilerEngine->get('D:\\xampp\\htdocs...', Array)
#3 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(120): Illuminate\View\View->getContents()
#4 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\View.php(85): Illuminate\View\View->renderContents()
#5 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Http\Response.php(38): Illuminate\View\View->render()
#6 D:\xampp\htdocs\tms\vendor\symfony\http-foundation\Response.php(206): Illuminate\Http\Response->setContent(Object(Illuminate\View\View))
#7 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(615): Symfony\Component\HttpFoundation\Response->__construct(Object(Illuminate\View\View))
#8 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(572): Illuminate\Routing\Router->prepareResponse(Object(Illuminate\Http\Request), Object(Illuminate\View\View))
#9 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#10 D:\xampp\htdocs\tms\app\Http\Controllers\Controller.php(45): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#11 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(131): App\Http\Controllers\Controller->App\Http\Controllers\{closure}(Object(Illuminate\Http\Request), Object(Closure))
#12 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 D:\xampp\htdocs\tms\app\Http\Middleware\Admin.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#14 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): App\Http\Middleware\Admin->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(65): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#20 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(59): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(574): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(533): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#38 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#40 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#44 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(148): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 D:\xampp\htdocs\tms\public\index.php(53): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 {main}  
[2025-06-10 14:11:06] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Call to undefined method Illuminate\Auth\Access\Gate::policies() in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 Command line code(1): Illuminate\Support\Facades\Facade::__callStatic('policies', Array)
#1 {main}
  thrown in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 {main}  
[2025-06-10 14:11:06] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Class 'Illuminate\Foundation\Auth\Admin' not found in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(571): include()
#1 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\xampp\\htdocs...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Admin')
#3 [internal function]: spl_autoload_call('App\\Admin')
#4 Command line code(1): class_exists('App\\Admin')
#5 [internal function]: {closure}('App\\Admin')
#6 Command line code(1): array_filter(Array, Object(Closure))
#7 {main}
  thrown in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 {main}  
[2025-06-10 14:12:06] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Call to undefined method Illuminate\Auth\Access\Gate::policies() in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 Command line code(1): Illuminate\Support\Facades\Facade::__callStatic('policies', Array)
#1 {main}
  thrown in D:\xampp\htdocs\tms\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php:221
Stack trace:
#0 {main}  
[2025-06-10 14:12:06] local.ERROR: Symfony\Component\Debug\Exception\FatalErrorException: Uncaught Error: Class 'Illuminate\Foundation\Auth\Admin' not found in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(571): include()
#1 D:\xampp\htdocs\tms\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\xampp\\htdocs...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Admin')
#3 [internal function]: spl_autoload_call('App\\Admin')
#4 Command line code(1): class_exists('App\\Admin')
#5 [internal function]: {closure}('App\\Admin')
#6 Command line code(1): array_filter(Array, Object(Closure))
#7 {main}
  thrown in D:\xampp\htdocs\tms\app\Admin.php:8
Stack trace:
#0 {main}  
