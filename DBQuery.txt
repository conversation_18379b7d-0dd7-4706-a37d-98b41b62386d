TRUNCATE TABLE `users`;
ALTER TABLE `users` CHANGE `type` `type` ENUM('ADMIN','CUSTOMER','USER','AGENT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USER' COMMENT '\'0\' => \'USER\', \'1\' => \'Customer\', \'2\' => \'Admin\'';
INSERT INTO `users` (`id`, `name`, `email`, `password`, `type`, `remember_token`, `created_at`, `updated_at`) VALUES (NULL, 'admin', '<EMAIL>', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', 'ADMIN', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', '2018-08-05 00:00:00', '2018-08-05 00:00:00');
INSERT INTO `users` (`id`, `name`, `email`, `password`, `type`, `remember_token`, `created_at`, `updated_at`) VALUES (NULL, 'user', '<EMAIL>', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', 'USER', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', '2018-08-05 00:00:00', '2018-08-05 00:00:00');
INSERT INTO `users` (`id`, `name`, `email`, `password`, `type`, `remember_token`, `created_at`, `updated_at`) VALUES (NULL, 'customer', '<EMAIL>', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', 'CUSTOMER', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', '2018-08-05 00:00:00', '2018-08-05 00:00:00');
INSERT INTO `users` (`id`, `name`, `email`, `password`, `type`, `remember_token`, `created_at`, `updated_at`) VALUES (NULL, 'agent', '<EMAIL>', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', 'AGENT', '$2y$12$SVnRH9z4fFbwGVAslC0umeId8nm6GeG2sitYuYn.cSAJ2REvv3z8G', '2018-08-05 00:00:00', '2018-08-05 00:00:00');
