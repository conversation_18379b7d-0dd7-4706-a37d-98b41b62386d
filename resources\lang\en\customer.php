<?php

return [
    'customerlist'      => 'Customer',
    'editcustomer'      => 'Edit Customer',
    'newcustomer'       => 'New Customer',
    'interested'        => 'Interested',
    'customernumber'    => 'Customer Number',
    'customername'      => 'Customer Name',
    'customertype'      => 'Customer Type',
    'customerdetails'   => 'Customer Details',
    'workplacedetail'   => 'Workplace Detail',
    'responsibleworker' => 'Responsible Worker',
    'editcustomer'      => 'Edit Customer',
    
    'companyname'       => 'Company Name',
    'address'           => 'Address',
    'plzstate'          => 'PLZ / State',
    'state'             => 'State',
    'telephone'         => 'Telephone',
    'fax'               => 'Fax',
    'email'             => 'Email',
    'web'               => 'Web',
    'taxnumber'         => 'Tax Number',
    'note'              => 'Note',
    'timeline'          => 'Timeline',
    'pleaseselect'      => 'Please Select',
    
    'contact'           => 'Contact',
    'gender'            => 'Gender',  
    'pleaseselect'      => 'Please select',  
    'male'              => 'Male',  
    'female'            => 'Female',
    'firstname'         => 'First Name',  
    'surname'           => 'Surname',
    
    'telephone'         => 'Telephone',
    'mobile'            => 'Mobile',
    'email'             => 'E-mail',
    'note'              => 'Note',
    
    'privacy'                           =>          'Privacy',
    'registerdate'                      =>          'Register Date',
    'howlongdosethesystemneedtosave'    =>          'How long dose the system need to save ?',
    'selectstimeperiod'                 =>          'Select time period',
    'month'                             =>          'Month',
    'year'                              =>          'Year',
    'lastupdate'                        =>          'Last Update',
    'rememberfordeletewithmail'         =>          'Remember for delete with mail.',
    'memoryforpurpose'                  =>          'Memory for purpose',
    
    'save'                              =>          'save',
    'addcustomer'=>'add-customer',
    'view'=>'View',
    'edit'=>'Edit',
    'delete'=>'Delete',
    'sixmonth'=>'6 Month',
    'oneyear'=>'1 Year',
    'twoyear'=>'2 Year',
    'threeyear'=>'3 Year',
    'fouryear'=>'4 Year',
    'fiveyear'=>'5 Year',
];  
?>