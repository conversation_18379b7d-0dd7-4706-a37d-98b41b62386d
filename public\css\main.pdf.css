 html {
     font-family: Source Sans Pro, sans-serif;
     font-size: 16px;
     line-height: 1.5;
     -webkit-text-size-adjust: 100%;
     -ms-text-size-adjust: 100%;
     text-size-adjust: 100%
 }
 
 body,
 html {
     height: 100%;
     min-height: 100%
 }
 
 body {
     margin: 0;
     background-color: #eff3f6
 }
 
 button,
 input,
 optgroup,
 select,
 textarea {
     font-family: Source Sans Pro, sans-serif
 }
 
 blockquote,
 body,
 dd,
 dl,
 fieldset,
 figure,
 h1,
 h2,
 h3,
 h4,
 h5,
 h6,
 hr,
 legend,
 ol,
 p,
 pre,
 ul {
     margin: 0;
     padding: 0
 }
 
 article,
 aside,
 figcaption,
 figure,
 footer,
 header,
 main,
 nav,
 section {
     display: block
 }
 
 button {
     cursor: pointer
 }
 
 ol,
 ul {
     margin: 0;
     padding: 0;
     list-style-type: none
 }
 
 li>ol,
 li>ul {
     margin-bottom: 0
 }
 
/* img {
     display: inline-block;
     max-width: 100%;
     height: auto;
     border-style: none
 }*/
 
 textarea {
     display: block;
     margin: 0;
     outline: 0;
     font-family: inherit;
     font-size: inherit;
     line-height: inherit;
     -webkit-appearance: none;
     -moz-appearance: none;
     appearance: none
 }
 
 h1,
 h2,
 h3,
 h4,
 h5,
 h6 {
     margin: 0 0 .5rem;
     color: #354052;
     font-weight: 500
 }
 
 h1 {
     font-size: 2.25rem
 }
 
 h2 {
     font-size: 1.75rem
 }
 
 h3 {
     font-size: 1.5rem
 }
 
 h4 {
     font-size: 1.25rem
 }
 
 h5 {
     font-size: 1.125rem
 }
 
 h6 {
     font-size: 1rem
 }
 
 p {
     font-size: .875rem
 }
 
 dl dd,
 dl dt,
 p,
 strong {
     color: #354052
 }
 
 a {
     transition: color .15s ease-in-out;
     background-color: transparent;
     text-decoration: none;
     cursor: pointer;
     -webkit-text-decoration-skip: objects;
     text-decoration-skip: objects
 }
 
 a,
 a:hover {
     color: #2ea1f8
 }
 
 a:hover {
     text-decoration: underline
 }
 
 code,
 kbd,
 pre,
 samp {
     font-family: monospace, monospace;
     font-size: 1em
 }
 
 mark {
     background-color: #ff0;
     color: #fff
 }
 
 small {
     font-size: 85%
 }
 
 blockquote {
     padding-left: 15px;
     border-left: 3px solid #e6eaee
 }
 
 @-ms-viewport {
     width: device-width
 }
 
 html {
     box-sizing: border-box;
     -ms-overflow-style: scrollbar
 }
 
 *,
 :after,
 :before {
     box-sizing: inherit
 }
 
 .container {
     width: 100%;
     padding-right: 15px;
     padding-left: 15px;
     margin-right: auto;
     margin-left: auto
 }
 
 @media (min-width:576px) {
     .container {
         max-width: 540px
     }
 }
 
 @media (min-width:768px) {
     .container {
         max-width: 720px
     }
 }
 
 @media (min-width:992px) {
     .container {
         max-width: 960px
     }
 }
 
 @media (min-width:1200px) {
     .container {
         max-width: 1140px
     }
 }
 
 .container-fluid {
     width: 100%;
     padding-right: 15px;
     padding-left: 15px;
     margin-right: auto;
     margin-left: auto
 }
 
 .row {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     margin-right: -15px;
     margin-left: -15px
 }
 
 .no-gutters {
     margin-right: 0;
     margin-left: 0
 }
 
 .no-gutters>.col,
 .no-gutters>[class*=col-] {
     padding-right: 0;
     padding-left: 0
 }
 
 .col,
 .col-1,
 .col-2,
 .col-3,
 .col-4,
 .col-5,
 .col-6,
 .col-7,
 .col-8,
 .col-9,
 .col-10,
 .col-11,
 .col-12,
 .col-auto,
 .col-lg,
 .col-lg-1,
 .col-lg-2,
 .col-lg-3,
 .col-lg-4,
 .col-lg-5,
 .col-lg-6,
 .col-lg-7,
 .col-lg-8,
 .col-lg-9,
 .col-lg-10,
 .col-lg-11,
 .col-lg-12,
 .col-lg-auto,
 .col-md,
 .col-md-1,
 .col-md-2,
 .col-md-3,
 .col-md-4,
 .col-md-5,
 .col-md-6,
 .col-md-7,
 .col-md-8,
 .col-md-9,
 .col-md-10,
 .col-md-11,
 .col-md-12,
 .col-md-auto,
 .col-sm,
 .col-sm-1,
 .col-sm-2,
 .col-sm-3,
 .col-sm-4,
 .col-sm-5,
 .col-sm-6,
 .col-sm-7,
 .col-sm-8,
 .col-sm-9,
 .col-sm-10,
 .col-sm-11,
 .col-sm-12,
 .col-sm-auto,
 .col-xl,
 .col-xl-1,
 .col-xl-2,
 .col-xl-3,
 .col-xl-4,
 .col-xl-5,
 .col-xl-6,
 .col-xl-7,
 .col-xl-8,
 .col-xl-9,
 .col-xl-10,
 .col-xl-11,
 .col-xl-12,
 .col-xl-auto {
     position: relative;
     width: 100%;
     min-height: 1px;
     padding-right: 15px;
     padding-left: 15px
 }
 
 .col {
     -ms-flex-preferred-size: 0;
     flex-basis: 0;
     -webkit-box-flex: 1;
     -ms-flex-positive: 1;
     flex-grow: 1;
     max-width: 100%
 }
 
 .col-auto {
     -ms-flex: 0 0 auto;
     flex: 0 0 auto;
     width: auto;
     max-width: none
 }
 
 .col-1,
 .col-auto {
     -webkit-box-flex: 0
 }
 
 .col-1 {
     -ms-flex: 0 0 8.333333%;
     flex: 0 0 8.333333%;
     max-width: 8.333333%
 }
 
 .col-2 {
     -ms-flex: 0 0 16.666667%;
     flex: 0 0 16.666667%;
     max-width: 16.666667%
 }
 
 .col-2,
 .col-3 {
     -webkit-box-flex: 0
 }
 
 .col-3 {
     -ms-flex: 0 0 25%;
     flex: 0 0 25%;
     max-width: 25%
 }
 
 .col-4 {
     -ms-flex: 0 0 33.333333%;
     flex: 0 0 33.333333%;
     max-width: 33.333333%
 }
 
 .col-4,
 .col-5 {
     -webkit-box-flex: 0
 }
 
 .col-5 {
     -ms-flex: 0 0 41.666667%;
     flex: 0 0 41.666667%;
     max-width: 41.666667%
 }
 
 .col-6 {
     -ms-flex: 0 0 50%;
     flex: 0 0 50%;
     max-width: 50%
 }
 
 .col-6,
 .col-7 {
     -webkit-box-flex: 0
 }
 
 .col-7 {
     -ms-flex: 0 0 58.333333%;
     flex: 0 0 58.333333%;
     max-width: 58.333333%
 }
 
 .col-8 {
     -ms-flex: 0 0 66.666667%;
     flex: 0 0 66.666667%;
     max-width: 66.666667%
 }
 
 .col-8,
 .col-9 {
     -webkit-box-flex: 0
 }
 
 .col-9 {
     -ms-flex: 0 0 75%;
     flex: 0 0 75%;
     max-width: 75%
 }
 
 .col-10 {
     -ms-flex: 0 0 83.333333%;
     flex: 0 0 83.333333%;
     max-width: 83.333333%
 }
 
 .col-10,
 .col-11 {
     -webkit-box-flex: 0
 }
 
 .col-11 {
     -ms-flex: 0 0 91.666667%;
     flex: 0 0 91.666667%;
     max-width: 91.666667%
 }
 
 .col-12 {
     -webkit-box-flex: 0;
     -ms-flex: 0 0 100%;
     flex: 0 0 100%;
     max-width: 100%
 }
 
 .order-first {
     -webkit-box-ordinal-group: 0;
     -ms-flex-order: -1;
     order: -1
 }
 
 .order-last {
     -webkit-box-ordinal-group: 14;
     -ms-flex-order: 13;
     order: 13
 }
 
 .order-0 {
     -webkit-box-ordinal-group: 1;
     -ms-flex-order: 0;
     order: 0
 }
 
 .order-1 {
     -webkit-box-ordinal-group: 2;
     -ms-flex-order: 1;
     order: 1
 }
 
 .order-2 {
     -webkit-box-ordinal-group: 3;
     -ms-flex-order: 2;
     order: 2
 }
 
 .order-3 {
     -webkit-box-ordinal-group: 4;
     -ms-flex-order: 3;
     order: 3
 }
 
 .order-4 {
     -webkit-box-ordinal-group: 5;
     -ms-flex-order: 4;
     order: 4
 }
 
 .order-5 {
     -webkit-box-ordinal-group: 6;
     -ms-flex-order: 5;
     order: 5
 }
 
 .order-6 {
     -webkit-box-ordinal-group: 7;
     -ms-flex-order: 6;
     order: 6
 }
 
 .order-7 {
     -webkit-box-ordinal-group: 8;
     -ms-flex-order: 7;
     order: 7
 }
 
 .order-8 {
     -webkit-box-ordinal-group: 9;
     -ms-flex-order: 8;
     order: 8
 }
 
 .order-9 {
     -webkit-box-ordinal-group: 10;
     -ms-flex-order: 9;
     order: 9
 }
 
 .order-10 {
     -webkit-box-ordinal-group: 11;
     -ms-flex-order: 10;
     order: 10
 }
 
 .order-11 {
     -webkit-box-ordinal-group: 12;
     -ms-flex-order: 11;
     order: 11
 }
 
 .order-12 {
     -webkit-box-ordinal-group: 13;
     -ms-flex-order: 12;
     order: 12
 }
 
 .offset-1 {
     margin-left: 8.333333%
 }
 
 .offset-2 {
     margin-left: 16.666667%
 }
 
 .offset-3 {
     margin-left: 25%
 }
 
 .offset-4 {
     margin-left: 33.333333%
 }
 
 .offset-5 {
     margin-left: 41.666667%
 }
 
 .offset-6 {
     margin-left: 50%
 }
 
 .offset-7 {
     margin-left: 58.333333%
 }
 
 .offset-8 {
     margin-left: 66.666667%
 }
 
 .offset-9 {
     margin-left: 75%
 }
 
 .offset-10 {
     margin-left: 83.333333%
 }
 
 .offset-11 {
     margin-left: 91.666667%
 }
 
 @media (min-width:576px) {
     .col-sm {
         -ms-flex-preferred-size: 0;
         flex-basis: 0;
         -webkit-box-flex: 1;
         -ms-flex-positive: 1;
         flex-grow: 1;
         max-width: 100%
     }
     .col-sm-auto {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 auto;
         flex: 0 0 auto;
         width: auto;
         max-width: none
     }
     .col-sm-1 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 8.333333%;
         flex: 0 0 8.333333%;
         max-width: 8.333333%
     }
     .col-sm-2 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 16.666667%;
         flex: 0 0 16.666667%;
         max-width: 16.666667%
     }
     .col-sm-3 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 25%;
         flex: 0 0 25%;
         max-width: 25%
     }
     .col-sm-4 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 33.333333%;
         flex: 0 0 33.333333%;
         max-width: 33.333333%
     }
     .col-sm-5 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 41.666667%;
         flex: 0 0 41.666667%;
         max-width: 41.666667%
     }
     .col-sm-6 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 50%;
         flex: 0 0 50%;
         max-width: 50%
     }
     .col-sm-7 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 58.333333%;
         flex: 0 0 58.333333%;
         max-width: 58.333333%
     }
     .col-sm-8 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 66.666667%;
         flex: 0 0 66.666667%;
         max-width: 66.666667%
     }
     .col-sm-9 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 75%;
         flex: 0 0 75%;
         max-width: 75%
     }
     .col-sm-10 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 83.333333%;
         flex: 0 0 83.333333%;
         max-width: 83.333333%
     }
     .col-sm-11 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 91.666667%;
         flex: 0 0 91.666667%;
         max-width: 91.666667%
     }
     .col-sm-12 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 100%;
         flex: 0 0 100%;
         max-width: 100%
     }
     .order-sm-first {
         -webkit-box-ordinal-group: 0;
         -ms-flex-order: -1;
         order: -1
     }
     .order-sm-last {
         -webkit-box-ordinal-group: 14;
         -ms-flex-order: 13;
         order: 13
     }
     .order-sm-0 {
         -webkit-box-ordinal-group: 1;
         -ms-flex-order: 0;
         order: 0
     }
     .order-sm-1 {
         -webkit-box-ordinal-group: 2;
         -ms-flex-order: 1;
         order: 1
     }
     .order-sm-2 {
         -webkit-box-ordinal-group: 3;
         -ms-flex-order: 2;
         order: 2
     }
     .order-sm-3 {
         -webkit-box-ordinal-group: 4;
         -ms-flex-order: 3;
         order: 3
     }
     .order-sm-4 {
         -webkit-box-ordinal-group: 5;
         -ms-flex-order: 4;
         order: 4
     }
     .order-sm-5 {
         -webkit-box-ordinal-group: 6;
         -ms-flex-order: 5;
         order: 5
     }
     .order-sm-6 {
         -webkit-box-ordinal-group: 7;
         -ms-flex-order: 6;
         order: 6
     }
     .order-sm-7 {
         -webkit-box-ordinal-group: 8;
         -ms-flex-order: 7;
         order: 7
     }
     .order-sm-8 {
         -webkit-box-ordinal-group: 9;
         -ms-flex-order: 8;
         order: 8
     }
     .order-sm-9 {
         -webkit-box-ordinal-group: 10;
         -ms-flex-order: 9;
         order: 9
     }
     .order-sm-10 {
         -webkit-box-ordinal-group: 11;
         -ms-flex-order: 10;
         order: 10
     }
     .order-sm-11 {
         -webkit-box-ordinal-group: 12;
         -ms-flex-order: 11;
         order: 11
     }
     .order-sm-12 {
         -webkit-box-ordinal-group: 13;
         -ms-flex-order: 12;
         order: 12
     }
     .offset-sm-0 {
         margin-left: 0
     }
     .offset-sm-1 {
         margin-left: 8.333333%
     }
     .offset-sm-2 {
         margin-left: 16.666667%
     }
     .offset-sm-3 {
         margin-left: 25%
     }
     .offset-sm-4 {
         margin-left: 33.333333%
     }
     .offset-sm-5 {
         margin-left: 41.666667%
     }
     .offset-sm-6 {
         margin-left: 50%
     }
     .offset-sm-7 {
         margin-left: 58.333333%
     }
     .offset-sm-8 {
         margin-left: 66.666667%
     }
     .offset-sm-9 {
         margin-left: 75%
     }
     .offset-sm-10 {
         margin-left: 83.333333%
     }
     .offset-sm-11 {
         margin-left: 91.666667%
     }
 }
 
 @media (min-width:768px) {
     .col-md {
         -ms-flex-preferred-size: 0;
         flex-basis: 0;
         -webkit-box-flex: 1;
         -ms-flex-positive: 1;
         flex-grow: 1;
         max-width: 100%
     }
     .col-md-auto {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 auto;
         flex: 0 0 auto;
         width: auto;
         max-width: none
     }
     .col-md-1 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 8.333333%;
         flex: 0 0 8.333333%;
         max-width: 8.333333%
     }
     .col-md-2 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 16.666667%;
         flex: 0 0 16.666667%;
         max-width: 16.666667%
     }
     .col-md-3 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 25%;
         flex: 0 0 25%;
         max-width: 25%
     }
     .col-md-4 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 33.333333%;
         flex: 0 0 33.333333%;
         max-width: 33.333333%
     }
     .col-md-5 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 41.666667%;
         flex: 0 0 41.666667%;
         max-width: 41.666667%
     }
     .col-md-6 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 50%;
         flex: 0 0 50%;
         max-width: 50%
     }
     .col-md-7 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 58.333333%;
         flex: 0 0 58.333333%;
         max-width: 58.333333%
     }
     .col-md-8 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 66.666667%;
         flex: 0 0 66.666667%;
         max-width: 66.666667%
     }
     .col-md-9 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 75%;
         flex: 0 0 75%;
         max-width: 75%
     }
     .col-md-10 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 83.333333%;
         flex: 0 0 83.333333%;
         max-width: 83.333333%
     }
     .col-md-11 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 91.666667%;
         flex: 0 0 91.666667%;
         max-width: 91.666667%
     }
     .col-md-12 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 100%;
         flex: 0 0 100%;
         max-width: 100%
     }
     .order-md-first {
         -webkit-box-ordinal-group: 0;
         -ms-flex-order: -1;
         order: -1
     }
     .order-md-last {
         -webkit-box-ordinal-group: 14;
         -ms-flex-order: 13;
         order: 13
     }
     .order-md-0 {
         -webkit-box-ordinal-group: 1;
         -ms-flex-order: 0;
         order: 0
     }
     .order-md-1 {
         -webkit-box-ordinal-group: 2;
         -ms-flex-order: 1;
         order: 1
     }
     .order-md-2 {
         -webkit-box-ordinal-group: 3;
         -ms-flex-order: 2;
         order: 2
     }
     .order-md-3 {
         -webkit-box-ordinal-group: 4;
         -ms-flex-order: 3;
         order: 3
     }
     .order-md-4 {
         -webkit-box-ordinal-group: 5;
         -ms-flex-order: 4;
         order: 4
     }
     .order-md-5 {
         -webkit-box-ordinal-group: 6;
         -ms-flex-order: 5;
         order: 5
     }
     .order-md-6 {
         -webkit-box-ordinal-group: 7;
         -ms-flex-order: 6;
         order: 6
     }
     .order-md-7 {
         -webkit-box-ordinal-group: 8;
         -ms-flex-order: 7;
         order: 7
     }
     .order-md-8 {
         -webkit-box-ordinal-group: 9;
         -ms-flex-order: 8;
         order: 8
     }
     .order-md-9 {
         -webkit-box-ordinal-group: 10;
         -ms-flex-order: 9;
         order: 9
     }
     .order-md-10 {
         -webkit-box-ordinal-group: 11;
         -ms-flex-order: 10;
         order: 10
     }
     .order-md-11 {
         -webkit-box-ordinal-group: 12;
         -ms-flex-order: 11;
         order: 11
     }
     .order-md-12 {
         -webkit-box-ordinal-group: 13;
         -ms-flex-order: 12;
         order: 12
     }
     .offset-md-0 {
         margin-left: 0
     }
     .offset-md-1 {
         margin-left: 8.333333%
     }
     .offset-md-2 {
         margin-left: 16.666667%
     }
     .offset-md-3 {
         margin-left: 25%
     }
     .offset-md-4 {
         margin-left: 33.333333%
     }
     .offset-md-5 {
         margin-left: 41.666667%
     }
     .offset-md-6 {
         margin-left: 50%
     }
     .offset-md-7 {
         margin-left: 58.333333%
     }
     .offset-md-8 {
         margin-left: 66.666667%
     }
     .offset-md-9 {
         margin-left: 75%
     }
     .offset-md-10 {
         margin-left: 83.333333%
     }
     .offset-md-11 {
         margin-left: 91.666667%
     }
 }
 
 @media (min-width:992px) {
     .col-lg {
         -ms-flex-preferred-size: 0;
         flex-basis: 0;
         -webkit-box-flex: 1;
         -ms-flex-positive: 1;
         flex-grow: 1;
         max-width: 100%
     }
     .col-lg-auto {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 auto;
         flex: 0 0 auto;
         width: auto;
         max-width: none
     }
     .col-lg-1 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 8.333333%;
         flex: 0 0 8.333333%;
         max-width: 8.333333%
     }
     .col-lg-2 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 16.666667%;
         flex: 0 0 16.666667%;
         max-width: 16.666667%
     }
     .col-lg-3 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 25%;
         flex: 0 0 25%;
         max-width: 25%
     }
     .col-lg-4 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 33.333333%;
         flex: 0 0 33.333333%;
         max-width: 33.333333%
     }
     .col-lg-5 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 41.666667%;
         flex: 0 0 41.666667%;
         max-width: 41.666667%
     }
     .col-lg-6 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 50%;
         flex: 0 0 50%;
         max-width: 50%
     }
     .col-lg-7 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 58.333333%;
         flex: 0 0 58.333333%;
         max-width: 58.333333%
     }
     .col-lg-8 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 66.666667%;
         flex: 0 0 66.666667%;
         max-width: 66.666667%
     }
     .col-lg-9 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 75%;
         flex: 0 0 75%;
         max-width: 75%
     }
     .col-lg-10 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 83.333333%;
         flex: 0 0 83.333333%;
         max-width: 83.333333%
     }
     .col-lg-11 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 91.666667%;
         flex: 0 0 91.666667%;
         max-width: 91.666667%
     }
     .col-lg-12 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 100%;
         flex: 0 0 100%;
         max-width: 100%
     }
     .order-lg-first {
         -webkit-box-ordinal-group: 0;
         -ms-flex-order: -1;
         order: -1
     }
     .order-lg-last {
         -webkit-box-ordinal-group: 14;
         -ms-flex-order: 13;
         order: 13
     }
     .order-lg-0 {
         -webkit-box-ordinal-group: 1;
         -ms-flex-order: 0;
         order: 0
     }
     .order-lg-1 {
         -webkit-box-ordinal-group: 2;
         -ms-flex-order: 1;
         order: 1
     }
     .order-lg-2 {
         -webkit-box-ordinal-group: 3;
         -ms-flex-order: 2;
         order: 2
     }
     .order-lg-3 {
         -webkit-box-ordinal-group: 4;
         -ms-flex-order: 3;
         order: 3
     }
     .order-lg-4 {
         -webkit-box-ordinal-group: 5;
         -ms-flex-order: 4;
         order: 4
     }
     .order-lg-5 {
         -webkit-box-ordinal-group: 6;
         -ms-flex-order: 5;
         order: 5
     }
     .order-lg-6 {
         -webkit-box-ordinal-group: 7;
         -ms-flex-order: 6;
         order: 6
     }
     .order-lg-7 {
         -webkit-box-ordinal-group: 8;
         -ms-flex-order: 7;
         order: 7
     }
     .order-lg-8 {
         -webkit-box-ordinal-group: 9;
         -ms-flex-order: 8;
         order: 8
     }
     .order-lg-9 {
         -webkit-box-ordinal-group: 10;
         -ms-flex-order: 9;
         order: 9
     }
     .order-lg-10 {
         -webkit-box-ordinal-group: 11;
         -ms-flex-order: 10;
         order: 10
     }
     .order-lg-11 {
         -webkit-box-ordinal-group: 12;
         -ms-flex-order: 11;
         order: 11
     }
     .order-lg-12 {
         -webkit-box-ordinal-group: 13;
         -ms-flex-order: 12;
         order: 12
     }
     .offset-lg-0 {
         margin-left: 0
     }
     .offset-lg-1 {
         margin-left: 8.333333%
     }
     .offset-lg-2 {
         margin-left: 16.666667%
     }
     .offset-lg-3 {
         margin-left: 25%
     }
     .offset-lg-4 {
         margin-left: 33.333333%
     }
     .offset-lg-5 {
         margin-left: 41.666667%
     }
     .offset-lg-6 {
         margin-left: 50%
     }
     .offset-lg-7 {
         margin-left: 58.333333%
     }
     .offset-lg-8 {
         margin-left: 66.666667%
     }
     .offset-lg-9 {
         margin-left: 75%
     }
     .offset-lg-10 {
         margin-left: 83.333333%
     }
     .offset-lg-11 {
         margin-left: 91.666667%
     }
 }
 
 @media (min-width:1200px) {
     .col-xl {
         -ms-flex-preferred-size: 0;
         flex-basis: 0;
         -webkit-box-flex: 1;
         -ms-flex-positive: 1;
         flex-grow: 1;
         max-width: 100%
     }
     .col-xl-auto {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 auto;
         flex: 0 0 auto;
         width: auto;
         max-width: none
     }
     .col-xl-1 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 8.333333%;
         flex: 0 0 8.333333%;
         max-width: 8.333333%
     }
     .col-xl-2 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 16.666667%;
         flex: 0 0 16.666667%;
         max-width: 16.666667%
     }
     .col-xl-3 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 25%;
         flex: 0 0 25%;
         max-width: 25%
     }
     .col-xl-4 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 33.333333%;
         flex: 0 0 33.333333%;
         max-width: 33.333333%
     }
     .col-xl-5 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 41.666667%;
         flex: 0 0 41.666667%;
         max-width: 41.666667%
     }
     .col-xl-6 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 50%;
         flex: 0 0 50%;
         max-width: 50%
     }
     .col-xl-7 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 58.333333%;
         flex: 0 0 58.333333%;
         max-width: 58.333333%
     }
     .col-xl-8 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 66.666667%;
         flex: 0 0 66.666667%;
         max-width: 66.666667%
     }
     .col-xl-9 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 75%;
         flex: 0 0 75%;
         max-width: 75%
     }
     .col-xl-10 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 83.333333%;
         flex: 0 0 83.333333%;
         max-width: 83.333333%
     }
     .col-xl-11 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 91.666667%;
         flex: 0 0 91.666667%;
         max-width: 91.666667%
     }
     .col-xl-12 {
         -webkit-box-flex: 0;
         -ms-flex: 0 0 100%;
         flex: 0 0 100%;
         max-width: 100%
     }
     .order-xl-first {
         -webkit-box-ordinal-group: 0;
         -ms-flex-order: -1;
         order: -1
     }
     .order-xl-last {
         -webkit-box-ordinal-group: 14;
         -ms-flex-order: 13;
         order: 13
     }
     .order-xl-0 {
         -webkit-box-ordinal-group: 1;
         -ms-flex-order: 0;
         order: 0
     }
     .order-xl-1 {
         -webkit-box-ordinal-group: 2;
         -ms-flex-order: 1;
         order: 1
     }
     .order-xl-2 {
         -webkit-box-ordinal-group: 3;
         -ms-flex-order: 2;
         order: 2
     }
     .order-xl-3 {
         -webkit-box-ordinal-group: 4;
         -ms-flex-order: 3;
         order: 3
     }
     .order-xl-4 {
         -webkit-box-ordinal-group: 5;
         -ms-flex-order: 4;
         order: 4
     }
     .order-xl-5 {
         -webkit-box-ordinal-group: 6;
         -ms-flex-order: 5;
         order: 5
     }
     .order-xl-6 {
         -webkit-box-ordinal-group: 7;
         -ms-flex-order: 6;
         order: 6
     }
     .order-xl-7 {
         -webkit-box-ordinal-group: 8;
         -ms-flex-order: 7;
         order: 7
     }
     .order-xl-8 {
         -webkit-box-ordinal-group: 9;
         -ms-flex-order: 8;
         order: 8
     }
     .order-xl-9 {
         -webkit-box-ordinal-group: 10;
         -ms-flex-order: 9;
         order: 9
     }
     .order-xl-10 {
         -webkit-box-ordinal-group: 11;
         -ms-flex-order: 10;
         order: 10
     }
     .order-xl-11 {
         -webkit-box-ordinal-group: 12;
         -ms-flex-order: 11;
         order: 11
     }
     .order-xl-12 {
         -webkit-box-ordinal-group: 13;
         -ms-flex-order: 12;
         order: 12
     }
     .offset-xl-0 {
         margin-left: 0
     }
     .offset-xl-1 {
         margin-left: 8.333333%
     }
     .offset-xl-2 {
         margin-left: 16.666667%
     }
     .offset-xl-3 {
         margin-left: 25%
     }
     .offset-xl-4 {
         margin-left: 33.333333%
     }
     .offset-xl-5 {
         margin-left: 41.666667%
     }
     .offset-xl-6 {
         margin-left: 50%
     }
     .offset-xl-7 {
         margin-left: 58.333333%
     }
     .offset-xl-8 {
         margin-left: 66.666667%
     }
     .offset-xl-9 {
         margin-left: 75%
     }
     .offset-xl-10 {
         margin-left: 83.333333%
     }
     .offset-xl-11 {
         margin-left: 91.666667%
     }
 }
 
 .d-none {
     display: none!important
 }
 
 .d-inline {
     display: inline!important
 }
 
 .d-inline-block {
     display: inline-block!important
 }
 
 .d-block {
     display: block!important
 }
 
 .d-table {
     display: table!important
 }
 
 .d-table-row {
     display: table-row!important
 }
 
 .d-table-cell {
     display: table-cell!important
 }
 
 .d-flex {
     display: -webkit-box!important;
     display: -ms-flexbox!important;
     display: flex!important
 }
 
 .d-inline-flex {
     display: -webkit-inline-box!important;
     display: -ms-inline-flexbox!important;
     display: inline-flex!important
 }
 
 @media (min-width:576px) {
     .d-sm-none {
         display: none!important
     }
     .d-sm-inline {
         display: inline!important
     }
     .d-sm-inline-block {
         display: inline-block!important
     }
     .d-sm-block {
         display: block!important
     }
     .d-sm-table {
         display: table!important
     }
     .d-sm-table-row {
         display: table-row!important
     }
     .d-sm-table-cell {
         display: table-cell!important
     }
     .d-sm-flex {
         display: -webkit-box!important;
         display: -ms-flexbox!important;
         display: flex!important
     }
     .d-sm-inline-flex {
         display: -webkit-inline-box!important;
         display: -ms-inline-flexbox!important;
         display: inline-flex!important
     }
 }
 
 @media (min-width:768px) {
     .d-md-none {
         display: none!important
     }
     .d-md-inline {
         display: inline!important
     }
     .d-md-inline-block {
         display: inline-block!important
     }
     .d-md-block {
         display: block!important
     }
     .d-md-table {
         display: table!important
     }
     .d-md-table-row {
         display: table-row!important
     }
     .d-md-table-cell {
         display: table-cell!important
     }
     .d-md-flex {
         display: -webkit-box!important;
         display: -ms-flexbox!important;
         display: flex!important
     }
     .d-md-inline-flex {
         display: -webkit-inline-box!important;
         display: -ms-inline-flexbox!important;
         display: inline-flex!important
     }
 }
 
 @media (min-width:992px) {
     .d-lg-none {
         display: none!important
     }
     .d-lg-inline {
         display: inline!important
     }
     .d-lg-inline-block {
         display: inline-block!important
     }
     .d-lg-block {
         display: block!important
     }
     .d-lg-table {
         display: table!important
     }
     .d-lg-table-row {
         display: table-row!important
     }
     .d-lg-table-cell {
         display: table-cell!important
     }
     .d-lg-flex {
         display: -webkit-box!important;
         display: -ms-flexbox!important;
         display: flex!important
     }
     .d-lg-inline-flex {
         display: -webkit-inline-box!important;
         display: -ms-inline-flexbox!important;
         display: inline-flex!important
     }
 }
 
 @media (min-width:1200px) {
     .d-xl-none {
         display: none!important
     }
     .d-xl-inline {
         display: inline!important
     }
     .d-xl-inline-block {
         display: inline-block!important
     }
     .d-xl-block {
         display: block!important
     }
     .d-xl-table {
         display: table!important
     }
     .d-xl-table-row {
         display: table-row!important
     }
     .d-xl-table-cell {
         display: table-cell!important
     }
     .d-xl-flex {
         display: -webkit-box!important;
         display: -ms-flexbox!important;
         display: flex!important
     }
     .d-xl-inline-flex {
         display: -webkit-inline-box!important;
         display: -ms-inline-flexbox!important;
         display: inline-flex!important
     }
 }
 
 @media print {
     .d-print-none {
         display: none!important
     }
     .d-print-inline {
         display: inline!important
     }
     .d-print-inline-block {
         display: inline-block!important
     }
     .d-print-block {
         display: block!important
     }
     .d-print-table {
         display: table!important
     }
     .d-print-table-row {
         display: table-row!important
     }
     .d-print-table-cell {
         display: table-cell!important
     }
     .d-print-flex {
         display: -webkit-box!important;
         display: -ms-flexbox!important;
         display: flex!important
     }
     .d-print-inline-flex {
         display: -webkit-inline-box!important;
         display: -ms-inline-flexbox!important;
         display: inline-flex!important
     }
 }
 
 .flex-row {
     -webkit-box-orient: horizontal!important;
     -ms-flex-direction: row!important;
     flex-direction: row!important
 }
 
 .flex-column,
 .flex-row {
     -webkit-box-direction: normal!important
 }
 
 .flex-column {
     -webkit-box-orient: vertical!important;
     -ms-flex-direction: column!important;
     flex-direction: column!important
 }
 
 .flex-row-reverse {
     -webkit-box-orient: horizontal!important;
     -ms-flex-direction: row-reverse!important;
     flex-direction: row-reverse!important
 }
 
 .flex-column-reverse,
 .flex-row-reverse {
     -webkit-box-direction: reverse!important
 }
 
 .flex-column-reverse {
     -webkit-box-orient: vertical!important;
     -ms-flex-direction: column-reverse!important;
     flex-direction: column-reverse!important
 }
 
 .flex-wrap {
     -ms-flex-wrap: wrap!important;
     flex-wrap: wrap!important
 }
 
 .flex-nowrap {
     -ms-flex-wrap: nowrap!important;
     flex-wrap: nowrap!important
 }
 
 .flex-wrap-reverse {
     -ms-flex-wrap: wrap-reverse!important;
     flex-wrap: wrap-reverse!important
 }
 
 .justify-content-start {
     -webkit-box-pack: start!important;
     -ms-flex-pack: start!important;
     justify-content: flex-start!important
 }
 
 .justify-content-end {
     -webkit-box-pack: end!important;
     -ms-flex-pack: end!important;
     justify-content: flex-end!important
 }
 
 .justify-content-center {
     -webkit-box-pack: center!important;
     -ms-flex-pack: center!important;
     justify-content: center!important
 }
 
 .justify-content-between {
     -webkit-box-pack: justify!important;
     -ms-flex-pack: justify!important;
     justify-content: space-between!important
 }
 
 .justify-content-around {
     -ms-flex-pack: distribute!important;
     justify-content: space-around!important
 }
 
 .align-items-start {
     -webkit-box-align: start!important;
     -ms-flex-align: start!important;
     align-items: flex-start!important
 }
 
 .align-items-end {
     -webkit-box-align: end!important;
     -ms-flex-align: end!important;
     align-items: flex-end!important
 }
 
 .align-items-center {
     -webkit-box-align: center!important;
     -ms-flex-align: center!important;
     align-items: center!important
 }
 
 .align-items-baseline {
     -webkit-box-align: baseline!important;
     -ms-flex-align: baseline!important;
     align-items: baseline!important
 }
 
 .align-items-stretch {
     -webkit-box-align: stretch!important;
     -ms-flex-align: stretch!important;
     align-items: stretch!important
 }
 
 .align-content-start {
     -ms-flex-line-pack: start!important;
     align-content: flex-start!important
 }
 
 .align-content-end {
     -ms-flex-line-pack: end!important;
     align-content: flex-end!important
 }
 
 .align-content-center {
     -ms-flex-line-pack: center!important;
     align-content: center!important
 }
 
 .align-content-between {
     -ms-flex-line-pack: justify!important;
     align-content: space-between!important
 }
 
 .align-content-around {
     -ms-flex-line-pack: distribute!important;
     align-content: space-around!important
 }
 
 .align-content-stretch {
     -ms-flex-line-pack: stretch!important;
     align-content: stretch!important
 }
 
 .align-self-auto {
     -ms-flex-item-align: auto!important;
     align-self: auto!important
 }
 
 .align-self-start {
     -ms-flex-item-align: start!important;
     align-self: flex-start!important
 }
 
 .align-self-end {
     -ms-flex-item-align: end!important;
     align-self: flex-end!important
 }
 
 .align-self-center {
     -ms-flex-item-align: center!important;
     align-self: center!important
 }
 
 .align-self-baseline {
     -ms-flex-item-align: baseline!important;
     align-self: baseline!important
 }
 
 .align-self-stretch {
     -ms-flex-item-align: stretch!important;
     align-self: stretch!important
 }
 
 @media (min-width:576px) {
     .flex-sm-row {
         -webkit-box-orient: horizontal!important;
         -ms-flex-direction: row!important;
         flex-direction: row!important
     }
     .flex-sm-column,
     .flex-sm-row {
         -webkit-box-direction: normal!important
     }
     .flex-sm-column {
         -webkit-box-orient: vertical!important;
         -ms-flex-direction: column!important;
         flex-direction: column!important
     }
     .flex-sm-row-reverse {
         -webkit-box-orient: horizontal!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: row-reverse!important;
         flex-direction: row-reverse!important
     }
     .flex-sm-column-reverse {
         -webkit-box-orient: vertical!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: column-reverse!important;
         flex-direction: column-reverse!important
     }
     .flex-sm-wrap {
         -ms-flex-wrap: wrap!important;
         flex-wrap: wrap!important
     }
     .flex-sm-nowrap {
         -ms-flex-wrap: nowrap!important;
         flex-wrap: nowrap!important
     }
     .flex-sm-wrap-reverse {
         -ms-flex-wrap: wrap-reverse!important;
         flex-wrap: wrap-reverse!important
     }
     .justify-content-sm-start {
         -webkit-box-pack: start!important;
         -ms-flex-pack: start!important;
         justify-content: flex-start!important
     }
     .justify-content-sm-end {
         -webkit-box-pack: end!important;
         -ms-flex-pack: end!important;
         justify-content: flex-end!important
     }
     .justify-content-sm-center {
         -webkit-box-pack: center!important;
         -ms-flex-pack: center!important;
         justify-content: center!important
     }
     .justify-content-sm-between {
         -webkit-box-pack: justify!important;
         -ms-flex-pack: justify!important;
         justify-content: space-between!important
     }
     .justify-content-sm-around {
         -ms-flex-pack: distribute!important;
         justify-content: space-around!important
     }
     .align-items-sm-start {
         -webkit-box-align: start!important;
         -ms-flex-align: start!important;
         align-items: flex-start!important
     }
     .align-items-sm-end {
         -webkit-box-align: end!important;
         -ms-flex-align: end!important;
         align-items: flex-end!important
     }
     .align-items-sm-center {
         -webkit-box-align: center!important;
         -ms-flex-align: center!important;
         align-items: center!important
     }
     .align-items-sm-baseline {
         -webkit-box-align: baseline!important;
         -ms-flex-align: baseline!important;
         align-items: baseline!important
     }
     .align-items-sm-stretch {
         -webkit-box-align: stretch!important;
         -ms-flex-align: stretch!important;
         align-items: stretch!important
     }
     .align-content-sm-start {
         -ms-flex-line-pack: start!important;
         align-content: flex-start!important
     }
     .align-content-sm-end {
         -ms-flex-line-pack: end!important;
         align-content: flex-end!important
     }
     .align-content-sm-center {
         -ms-flex-line-pack: center!important;
         align-content: center!important
     }
     .align-content-sm-between {
         -ms-flex-line-pack: justify!important;
         align-content: space-between!important
     }
     .align-content-sm-around {
         -ms-flex-line-pack: distribute!important;
         align-content: space-around!important
     }
     .align-content-sm-stretch {
         -ms-flex-line-pack: stretch!important;
         align-content: stretch!important
     }
     .align-self-sm-auto {
         -ms-flex-item-align: auto!important;
         align-self: auto!important
     }
     .align-self-sm-start {
         -ms-flex-item-align: start!important;
         align-self: flex-start!important
     }
     .align-self-sm-end {
         -ms-flex-item-align: end!important;
         align-self: flex-end!important
     }
     .align-self-sm-center {
         -ms-flex-item-align: center!important;
         align-self: center!important
     }
     .align-self-sm-baseline {
         -ms-flex-item-align: baseline!important;
         align-self: baseline!important
     }
     .align-self-sm-stretch {
         -ms-flex-item-align: stretch!important;
         align-self: stretch!important
     }
 }
 
 @media (min-width:768px) {
     .flex-md-row {
         -webkit-box-orient: horizontal!important;
         -ms-flex-direction: row!important;
         flex-direction: row!important
     }
     .flex-md-column,
     .flex-md-row {
         -webkit-box-direction: normal!important
     }
     .flex-md-column {
         -webkit-box-orient: vertical!important;
         -ms-flex-direction: column!important;
         flex-direction: column!important
     }
     .flex-md-row-reverse {
         -webkit-box-orient: horizontal!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: row-reverse!important;
         flex-direction: row-reverse!important
     }
     .flex-md-column-reverse {
         -webkit-box-orient: vertical!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: column-reverse!important;
         flex-direction: column-reverse!important
     }
     .flex-md-wrap {
         -ms-flex-wrap: wrap!important;
         flex-wrap: wrap!important
     }
     .flex-md-nowrap {
         -ms-flex-wrap: nowrap!important;
         flex-wrap: nowrap!important
     }
     .flex-md-wrap-reverse {
         -ms-flex-wrap: wrap-reverse!important;
         flex-wrap: wrap-reverse!important
     }
     .justify-content-md-start {
         -webkit-box-pack: start!important;
         -ms-flex-pack: start!important;
         justify-content: flex-start!important
     }
     .justify-content-md-end {
         -webkit-box-pack: end!important;
         -ms-flex-pack: end!important;
         justify-content: flex-end!important
     }
     .justify-content-md-center {
         -webkit-box-pack: center!important;
         -ms-flex-pack: center!important;
         justify-content: center!important
     }
     .justify-content-md-between {
         -webkit-box-pack: justify!important;
         -ms-flex-pack: justify!important;
         justify-content: space-between!important
     }
     .justify-content-md-around {
         -ms-flex-pack: distribute!important;
         justify-content: space-around!important
     }
     .align-items-md-start {
         -webkit-box-align: start!important;
         -ms-flex-align: start!important;
         align-items: flex-start!important
     }
     .align-items-md-end {
         -webkit-box-align: end!important;
         -ms-flex-align: end!important;
         align-items: flex-end!important
     }
     .align-items-md-center {
         -webkit-box-align: center!important;
         -ms-flex-align: center!important;
         align-items: center!important
     }
     .align-items-md-baseline {
         -webkit-box-align: baseline!important;
         -ms-flex-align: baseline!important;
         align-items: baseline!important
     }
     .align-items-md-stretch {
         -webkit-box-align: stretch!important;
         -ms-flex-align: stretch!important;
         align-items: stretch!important
     }
     .align-content-md-start {
         -ms-flex-line-pack: start!important;
         align-content: flex-start!important
     }
     .align-content-md-end {
         -ms-flex-line-pack: end!important;
         align-content: flex-end!important
     }
     .align-content-md-center {
         -ms-flex-line-pack: center!important;
         align-content: center!important
     }
     .align-content-md-between {
         -ms-flex-line-pack: justify!important;
         align-content: space-between!important
     }
     .align-content-md-around {
         -ms-flex-line-pack: distribute!important;
         align-content: space-around!important
     }
     .align-content-md-stretch {
         -ms-flex-line-pack: stretch!important;
         align-content: stretch!important
     }
     .align-self-md-auto {
         -ms-flex-item-align: auto!important;
         align-self: auto!important
     }
     .align-self-md-start {
         -ms-flex-item-align: start!important;
         align-self: flex-start!important
     }
     .align-self-md-end {
         -ms-flex-item-align: end!important;
         align-self: flex-end!important
     }
     .align-self-md-center {
         -ms-flex-item-align: center!important;
         align-self: center!important
     }
     .align-self-md-baseline {
         -ms-flex-item-align: baseline!important;
         align-self: baseline!important
     }
     .align-self-md-stretch {
         -ms-flex-item-align: stretch!important;
         align-self: stretch!important
     }
 }
 
 @media (min-width:992px) {
     .flex-lg-row {
         -webkit-box-orient: horizontal!important;
         -ms-flex-direction: row!important;
         flex-direction: row!important
     }
     .flex-lg-column,
     .flex-lg-row {
         -webkit-box-direction: normal!important
     }
     .flex-lg-column {
         -webkit-box-orient: vertical!important;
         -ms-flex-direction: column!important;
         flex-direction: column!important
     }
     .flex-lg-row-reverse {
         -webkit-box-orient: horizontal!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: row-reverse!important;
         flex-direction: row-reverse!important
     }
     .flex-lg-column-reverse {
         -webkit-box-orient: vertical!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: column-reverse!important;
         flex-direction: column-reverse!important
     }
     .flex-lg-wrap {
         -ms-flex-wrap: wrap!important;
         flex-wrap: wrap!important
     }
     .flex-lg-nowrap {
         -ms-flex-wrap: nowrap!important;
         flex-wrap: nowrap!important
     }
     .flex-lg-wrap-reverse {
         -ms-flex-wrap: wrap-reverse!important;
         flex-wrap: wrap-reverse!important
     }
     .justify-content-lg-start {
         -webkit-box-pack: start!important;
         -ms-flex-pack: start!important;
         justify-content: flex-start!important
     }
     .justify-content-lg-end {
         -webkit-box-pack: end!important;
         -ms-flex-pack: end!important;
         justify-content: flex-end!important
     }
     .justify-content-lg-center {
         -webkit-box-pack: center!important;
         -ms-flex-pack: center!important;
         justify-content: center!important
     }
     .justify-content-lg-between {
         -webkit-box-pack: justify!important;
         -ms-flex-pack: justify!important;
         justify-content: space-between!important
     }
     .justify-content-lg-around {
         -ms-flex-pack: distribute!important;
         justify-content: space-around!important
     }
     .align-items-lg-start {
         -webkit-box-align: start!important;
         -ms-flex-align: start!important;
         align-items: flex-start!important
     }
     .align-items-lg-end {
         -webkit-box-align: end!important;
         -ms-flex-align: end!important;
         align-items: flex-end!important
     }
     .align-items-lg-center {
         -webkit-box-align: center!important;
         -ms-flex-align: center!important;
         align-items: center!important
     }
     .align-items-lg-baseline {
         -webkit-box-align: baseline!important;
         -ms-flex-align: baseline!important;
         align-items: baseline!important
     }
     .align-items-lg-stretch {
         -webkit-box-align: stretch!important;
         -ms-flex-align: stretch!important;
         align-items: stretch!important
     }
     .align-content-lg-start {
         -ms-flex-line-pack: start!important;
         align-content: flex-start!important
     }
     .align-content-lg-end {
         -ms-flex-line-pack: end!important;
         align-content: flex-end!important
     }
     .align-content-lg-center {
         -ms-flex-line-pack: center!important;
         align-content: center!important
     }
     .align-content-lg-between {
         -ms-flex-line-pack: justify!important;
         align-content: space-between!important
     }
     .align-content-lg-around {
         -ms-flex-line-pack: distribute!important;
         align-content: space-around!important
     }
     .align-content-lg-stretch {
         -ms-flex-line-pack: stretch!important;
         align-content: stretch!important
     }
     .align-self-lg-auto {
         -ms-flex-item-align: auto!important;
         align-self: auto!important
     }
     .align-self-lg-start {
         -ms-flex-item-align: start!important;
         align-self: flex-start!important
     }
     .align-self-lg-end {
         -ms-flex-item-align: end!important;
         align-self: flex-end!important
     }
     .align-self-lg-center {
         -ms-flex-item-align: center!important;
         align-self: center!important
     }
     .align-self-lg-baseline {
         -ms-flex-item-align: baseline!important;
         align-self: baseline!important
     }
     .align-self-lg-stretch {
         -ms-flex-item-align: stretch!important;
         align-self: stretch!important
     }
 }
 
 @media (min-width:1200px) {
     .flex-xl-row {
         -webkit-box-orient: horizontal!important;
         -ms-flex-direction: row!important;
         flex-direction: row!important
     }
     .flex-xl-column,
     .flex-xl-row {
         -webkit-box-direction: normal!important
     }
     .flex-xl-column {
         -webkit-box-orient: vertical!important;
         -ms-flex-direction: column!important;
         flex-direction: column!important
     }
     .flex-xl-row-reverse {
         -webkit-box-orient: horizontal!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: row-reverse!important;
         flex-direction: row-reverse!important
     }
     .flex-xl-column-reverse {
         -webkit-box-orient: vertical!important;
         -webkit-box-direction: reverse!important;
         -ms-flex-direction: column-reverse!important;
         flex-direction: column-reverse!important
     }
     .flex-xl-wrap {
         -ms-flex-wrap: wrap!important;
         flex-wrap: wrap!important
     }
     .flex-xl-nowrap {
         -ms-flex-wrap: nowrap!important;
         flex-wrap: nowrap!important
     }
     .flex-xl-wrap-reverse {
         -ms-flex-wrap: wrap-reverse!important;
         flex-wrap: wrap-reverse!important
     }
     .justify-content-xl-start {
         -webkit-box-pack: start!important;
         -ms-flex-pack: start!important;
         justify-content: flex-start!important
     }
     .justify-content-xl-end {
         -webkit-box-pack: end!important;
         -ms-flex-pack: end!important;
         justify-content: flex-end!important
     }
     .justify-content-xl-center {
         -webkit-box-pack: center!important;
         -ms-flex-pack: center!important;
         justify-content: center!important
     }
     .justify-content-xl-between {
         -webkit-box-pack: justify!important;
         -ms-flex-pack: justify!important;
         justify-content: space-between!important
     }
     .justify-content-xl-around {
         -ms-flex-pack: distribute!important;
         justify-content: space-around!important
     }
     .align-items-xl-start {
         -webkit-box-align: start!important;
         -ms-flex-align: start!important;
         align-items: flex-start!important
     }
     .align-items-xl-end {
         -webkit-box-align: end!important;
         -ms-flex-align: end!important;
         align-items: flex-end!important
     }
     .align-items-xl-center {
         -webkit-box-align: center!important;
         -ms-flex-align: center!important;
         align-items: center!important
     }
     .align-items-xl-baseline {
         -webkit-box-align: baseline!important;
         -ms-flex-align: baseline!important;
         align-items: baseline!important
     }
     .align-items-xl-stretch {
         -webkit-box-align: stretch!important;
         -ms-flex-align: stretch!important;
         align-items: stretch!important
     }
     .align-content-xl-start {
         -ms-flex-line-pack: start!important;
         align-content: flex-start!important
     }
     .align-content-xl-end {
         -ms-flex-line-pack: end!important;
         align-content: flex-end!important
     }
     .align-content-xl-center {
         -ms-flex-line-pack: center!important;
         align-content: center!important
     }
     .align-content-xl-between {
         -ms-flex-line-pack: justify!important;
         align-content: space-between!important
     }
     .align-content-xl-around {
         -ms-flex-line-pack: distribute!important;
         align-content: space-around!important
     }
     .align-content-xl-stretch {
         -ms-flex-line-pack: stretch!important;
         align-content: stretch!important
     }
     .align-self-xl-auto {
         -ms-flex-item-align: auto!important;
         align-self: auto!important
     }
     .align-self-xl-start {
         -ms-flex-item-align: start!important;
         align-self: flex-start!important
     }
     .align-self-xl-end {
         -ms-flex-item-align: end!important;
         align-self: flex-end!important
     }
     .align-self-xl-center {
         -ms-flex-item-align: center!important;
         align-self: center!important
     }
     .align-self-xl-baseline {
         -ms-flex-item-align: baseline!important;
         align-self: baseline!important
     }
     .align-self-xl-stretch {
         -ms-flex-item-align: stretch!important;
         align-self: stretch!important
     }
 }
 
 @font-face {
     font-family: FontAwesome;
     src: url(../fonts/fontawesome-webfont.eot?v=4.7.0);
     src: url(../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0) format("embedded-opentype"), url(../fonts/fontawesome-webfont.woff2?v=4.7.0) format("woff2"), url(../fonts/fontawesome-webfont.woff?v=4.7.0) format("woff"), url(../fonts/fontawesome-webfont.ttf?v=4.7.0) format("truetype"), url(../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular) format("svg");
     font-weight: 400;
     font-style: normal
 }
 
 .fa {
     display: inline-block;
     font: normal normal normal 14px/1 FontAwesome;
     font-size: inherit;
     text-rendering: auto;
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale
 }
 
 .fa-lg {
     font-size: 1.33333333em;
     line-height: .75em;
     vertical-align: -15%
 }
 
 .fa-2x {
     font-size: 2em
 }
 
 .fa-3x {
     font-size: 3em
 }
 
 .fa-4x {
     font-size: 4em
 }
 
 .fa-5x {
     font-size: 5em
 }
 
 .fa-fw {
     width: 1.28571429em;
     text-align: center
 }
 
 .fa-ul {
     padding-left: 0;
     margin-left: 2.14285714em;
     list-style-type: none
 }
 
 .fa-ul>li {
     position: relative
 }
 
 .fa-li {
     position: absolute;
     left: -2.14285714em;
     width: 2.14285714em;
     top: .14285714em;
     text-align: center
 }
 
 .fa-li.fa-lg {
     left: -1.85714286em
 }
 
 .fa-border {
     padding: .2em .25em .15em;
     border: .08em solid #eee;
     border-radius: .1em
 }
 
 .fa-pull-left {
     float: left
 }
 
 .fa-pull-right {
     float: right
 }
 
 .fa.fa-pull-left {
     margin-right: .3em
 }
 
 .fa.fa-pull-right {
     margin-left: .3em
 }
 
 .pull-right {
     float: right
 }
 
 .pull-left {
     float: left
 }
 
 .fa.pull-left {
     margin-right: .3em
 }
 
 .fa.pull-right {
     margin-left: .3em
 }
 
 .fa-spin {
     -webkit-animation: a 2s infinite linear;
     animation: a 2s infinite linear
 }
 
 .fa-pulse {
     -webkit-animation: a 1s infinite steps(8);
     animation: a 1s infinite steps(8)
 }
 
 @-webkit-keyframes a {
     0% {
         -webkit-transform: rotate(0deg);
         transform: rotate(0deg)
     }
     to {
         -webkit-transform: rotate(359deg);
         transform: rotate(359deg)
     }
 }
 
 @keyframes a {
     0% {
         -webkit-transform: rotate(0deg);
         transform: rotate(0deg)
     }
     to {
         -webkit-transform: rotate(359deg);
         transform: rotate(359deg)
     }
 }
 
 .fa-rotate-90 {
     -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
     -webkit-transform: rotate(90deg);
     transform: rotate(90deg)
 }
 
 .fa-rotate-180 {
     -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
     -webkit-transform: rotate(180deg);
     transform: rotate(180deg)
 }
 
 .fa-rotate-270 {
     -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
     -webkit-transform: rotate(270deg);
     transform: rotate(270deg)
 }
 
 .fa-flip-horizontal {
     -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
     -webkit-transform: scaleX(-1);
     transform: scaleX(-1)
 }
 
 .fa-flip-vertical {
     -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
     -webkit-transform: scaleY(-1);
     transform: scaleY(-1)
 }
 
 :root .fa-flip-horizontal,
 :root .fa-flip-vertical,
 :root .fa-rotate-90,
 :root .fa-rotate-180,
 :root .fa-rotate-270 {
     -webkit-filter: none;
     filter: none
 }
 
 .fa-stack {
     position: relative;
     display: inline-block;
     width: 2em;
     height: 2em;
     line-height: 2em;
     vertical-align: middle
 }
 
 .fa-stack-1x,
 .fa-stack-2x {
     position: absolute;
     left: 0;
     width: 100%;
     text-align: center
 }
 
 .fa-stack-1x {
     line-height: inherit
 }
 
 .fa-stack-2x {
     font-size: 2em
 }
 
 .fa-inverse {
     color: #fff
 }
 
 .fa-glass:before {
     content: "\f000"
 }
 
 .fa-music:before {
     content: "\f001"
 }
 
 .fa-search:before {
     content: "\f002"
 }
 
 .fa-envelope-o:before {
     content: "\f003"
 }
 
 .fa-heart:before {
     content: "\f004"
 }
 
 .fa-star:before {
     content: "\f005"
 }
 
 .fa-star-o:before {
     content: "\f006"
 }
 
 .fa-user:before {
     content: "\f007"
 }
 
 .fa-film:before {
     content: "\f008"
 }
 
 .fa-th-large:before {
     content: "\f009"
 }
 
 .fa-th:before {
     content: "\f00a"
 }
 
 .fa-th-list:before {
     content: "\f00b"
 }
 
 .fa-check:before {
     content: "\f00c"
 }
 
 .fa-close:before,
 .fa-remove:before,
 .fa-times:before {
     content: "\f00d"
 }
 
 .fa-search-plus:before {
     content: "\f00e"
 }
 
 .fa-search-minus:before {
     content: "\f010"
 }
 
 .fa-power-off:before {
     content: "\f011"
 }
 
 .fa-signal:before {
     content: "\f012"
 }
 
 .fa-cog:before,
 .fa-gear:before {
     content: "\f013"
 }
 
 .fa-trash-o:before {
     content: "\f014"
 }
 
 .fa-home:before {
     content: "\f015"
 }
 
 .fa-file-o:before {
     content: "\f016"
 }
 
 .fa-clock-o:before {
     content: "\f017"
 }
 
 .fa-road:before {
     content: "\f018"
 }
 
 .fa-download:before {
     content: "\f019"
 }
 
 .fa-arrow-circle-o-down:before {
     content: "\f01a"
 }
 
 .fa-arrow-circle-o-up:before {
     content: "\f01b"
 }
 
 .fa-inbox:before {
     content: "\f01c"
 }
 
 .fa-play-circle-o:before {
     content: "\f01d"
 }
 
 .fa-repeat:before,
 .fa-rotate-right:before {
     content: "\f01e"
 }
 
 .fa-refresh:before {
     content: "\f021"
 }
 
 .fa-list-alt:before {
     content: "\f022"
 }
 
 .fa-lock:before {
     content: "\f023"
 }
 
 .fa-flag:before {
     content: "\f024"
 }
 
 .fa-headphones:before {
     content: "\f025"
 }
 
 .fa-volume-off:before {
     content: "\f026"
 }
 
 .fa-volume-down:before {
     content: "\f027"
 }
 
 .fa-volume-up:before {
     content: "\f028"
 }
 
 .fa-qrcode:before {
     content: "\f029"
 }
 
 .fa-barcode:before {
     content: "\f02a"
 }
 
 .fa-tag:before {
     content: "\f02b"
 }
 
 .fa-tags:before {
     content: "\f02c"
 }
 
 .fa-book:before {
     content: "\f02d"
 }
 
 .fa-bookmark:before {
     content: "\f02e"
 }
 
 .fa-print:before {
     content: "\f02f"
 }
 
 .fa-camera:before {
     content: "\f030"
 }
 
 .fa-font:before {
     content: "\f031"
 }
 
 .fa-bold:before {
     content: "\f032"
 }
 
 .fa-italic:before {
     content: "\f033"
 }
 
 .fa-text-height:before {
     content: "\f034"
 }
 
 .fa-text-width:before {
     content: "\f035"
 }
 
 .fa-align-left:before {
     content: "\f036"
 }
 
 .fa-align-center:before {
     content: "\f037"
 }
 
 .fa-align-right:before {
     content: "\f038"
 }
 
 .fa-align-justify:before {
     content: "\f039"
 }
 
 .fa-list:before {
     content: "\f03a"
 }
 
 .fa-dedent:before,
 .fa-outdent:before {
     content: "\f03b"
 }
 
 .fa-indent:before {
     content: "\f03c"
 }
 
 .fa-video-camera:before {
     content: "\f03d"
 }
 
 .fa-image:before,
 .fa-photo:before,
 .fa-picture-o:before {
     content: "\f03e"
 }
 
 .fa-pencil:before {
     content: "\f040"
 }
 
 .fa-map-marker:before {
     content: "\f041"
 }
 
 .fa-adjust:before {
     content: "\f042"
 }
 
 .fa-tint:before {
     content: "\f043"
 }
 
 .fa-edit:before,
 .fa-pencil-square-o:before {
     content: "\f044"
 }
 
 .fa-share-square-o:before {
     content: "\f045"
 }
 
 .fa-check-square-o:before {
     content: "\f046"
 }
 
 .fa-arrows:before {
     content: "\f047"
 }
 
 .fa-step-backward:before {
     content: "\f048"
 }
 
 .fa-fast-backward:before {
     content: "\f049"
 }
 
 .fa-backward:before {
     content: "\f04a"
 }
 
 .fa-play:before {
     content: "\f04b"
 }
 
 .fa-pause:before {
     content: "\f04c"
 }
 
 .fa-stop:before {
     content: "\f04d"
 }
 
 .fa-forward:before {
     content: "\f04e"
 }
 
 .fa-fast-forward:before {
     content: "\f050"
 }
 
 .fa-step-forward:before {
     content: "\f051"
 }
 
 .fa-eject:before {
     content: "\f052"
 }
 
 .fa-chevron-left:before {
     content: "\f053"
 }
 
 .fa-chevron-right:before {
     content: "\f054"
 }
 
 .fa-plus-circle:before {
     content: "\f055"
 }
 
 .fa-minus-circle:before {
     content: "\f056"
 }
 
 .fa-times-circle:before {
     content: "\f057"
 }
 
 .fa-check-circle:before {
     content: "\f058"
 }
 
 .fa-question-circle:before {
     content: "\f059"
 }
 
 .fa-info-circle:before {
     content: "\f05a"
 }
 
 .fa-crosshairs:before {
     content: "\f05b"
 }
 
 .fa-times-circle-o:before {
     content: "\f05c"
 }
 
 .fa-check-circle-o:before {
     content: "\f05d"
 }
 
 .fa-ban:before {
     content: "\f05e"
 }
 
 .fa-arrow-left:before {
     content: "\f060"
 }
 
 .fa-arrow-right:before {
     content: "\f061"
 }
 
 .fa-arrow-up:before {
     content: "\f062"
 }
 
 .fa-arrow-down:before {
     content: "\f063"
 }
 
 .fa-mail-forward:before,
 .fa-share:before {
     content: "\f064"
 }
 
 .fa-expand:before {
     content: "\f065"
 }
 
 .fa-compress:before {
     content: "\f066"
 }
 
 .fa-plus:before {
     content: "\f067"
 }
 
 .fa-minus:before {
     content: "\f068"
 }
 
 .fa-asterisk:before {
     content: "\f069"
 }
 
 .fa-exclamation-circle:before {
     content: "\f06a"
 }
 
 .fa-gift:before {
     content: "\f06b"
 }
 
 .fa-leaf:before {
     content: "\f06c"
 }
 
 .fa-fire:before {
     content: "\f06d"
 }
 
 .fa-eye:before {
     content: "\f06e"
 }
 
 .fa-eye-slash:before {
     content: "\f070"
 }
 
 .fa-exclamation-triangle:before,
 .fa-warning:before {
     content: "\f071"
 }
 
 .fa-plane:before {
     content: "\f072"
 }
 
 .fa-calendar:before {
     content: "\f073"
 }
 
 .fa-random:before {
     content: "\f074"
 }
 
 .fa-comment:before {
     content: "\f075"
 }
 
 .fa-magnet:before {
     content: "\f076"
 }
 
 .fa-chevron-up:before {
     content: "\f077"
 }
 
 .fa-chevron-down:before {
     content: "\f078"
 }
 
 .fa-retweet:before {
     content: "\f079"
 }
 
 .fa-shopping-cart:before {
     content: "\f07a"
 }
 
 .fa-folder:before {
     content: "\f07b"
 }
 
 .fa-folder-open:before {
     content: "\f07c"
 }
 
 .fa-arrows-v:before {
     content: "\f07d"
 }
 
 .fa-arrows-h:before {
     content: "\f07e"
 }
 
 .fa-bar-chart-o:before,
 .fa-bar-chart:before {
     content: "\f080"
 }
 
 .fa-twitter-square:before {
     content: "\f081"
 }
 
 .fa-facebook-square:before {
     content: "\f082"
 }
 
 .fa-camera-retro:before {
     content: "\f083"
 }
 
 .fa-key:before {
     content: "\f084"
 }
 
 .fa-cogs:before,
 .fa-gears:before {
     content: "\f085"
 }
 
 .fa-comments:before {
     content: "\f086"
 }
 
 .fa-thumbs-o-up:before {
     content: "\f087"
 }
 
 .fa-thumbs-o-down:before {
     content: "\f088"
 }
 
 .fa-star-half:before {
     content: "\f089"
 }
 
 .fa-heart-o:before {
     content: "\f08a"
 }
 
 .fa-sign-out:before {
     content: "\f08b"
 }
 
 .fa-linkedin-square:before {
     content: "\f08c"
 }
 
 .fa-thumb-tack:before {
     content: "\f08d"
 }
 
 .fa-external-link:before {
     content: "\f08e"
 }
 
 .fa-sign-in:before {
     content: "\f090"
 }
 
 .fa-trophy:before {
     content: "\f091"
 }
 
 .fa-github-square:before {
     content: "\f092"
 }
 
 .fa-upload:before {
     content: "\f093"
 }
 
 .fa-lemon-o:before {
     content: "\f094"
 }
 
 .fa-phone:before {
     content: "\f095"
 }
 
 .fa-square-o:before {
     content: "\f096"
 }
 
 .fa-bookmark-o:before {
     content: "\f097"
 }
 
 .fa-phone-square:before {
     content: "\f098"
 }
 
 .fa-twitter:before {
     content: "\f099"
 }
 
 .fa-facebook-f:before,
 .fa-facebook:before {
     content: "\f09a"
 }
 
 .fa-github:before {
     content: "\f09b"
 }
 
 .fa-unlock:before {
     content: "\f09c"
 }
 
 .fa-credit-card:before {
     content: "\f09d"
 }
 
 .fa-feed:before,
 .fa-rss:before {
     content: "\f09e"
 }
 
 .fa-hdd-o:before {
     content: "\f0a0"
 }
 
 .fa-bullhorn:before {
     content: "\f0a1"
 }
 
 .fa-bell:before {
     content: "\f0f3"
 }
 
 .fa-certificate:before {
     content: "\f0a3"
 }
 
 .fa-hand-o-right:before {
     content: "\f0a4"
 }
 
 .fa-hand-o-left:before {
     content: "\f0a5"
 }
 
 .fa-hand-o-up:before {
     content: "\f0a6"
 }
 
 .fa-hand-o-down:before {
     content: "\f0a7"
 }
 
 .fa-arrow-circle-left:before {
     content: "\f0a8"
 }
 
 .fa-arrow-circle-right:before {
     content: "\f0a9"
 }
 
 .fa-arrow-circle-up:before {
     content: "\f0aa"
 }
 
 .fa-arrow-circle-down:before {
     content: "\f0ab"
 }
 
 .fa-globe:before {
     content: "\f0ac"
 }
 
 .fa-wrench:before {
     content: "\f0ad"
 }
 
 .fa-tasks:before {
     content: "\f0ae"
 }
 
 .fa-filter:before {
     content: "\f0b0"
 }
 
 .fa-briefcase:before {
     content: "\f0b1"
 }
 
 .fa-arrows-alt:before {
     content: "\f0b2"
 }
 
 .fa-group:before,
 .fa-users:before {
     content: "\f0c0"
 }
 
 .fa-chain:before,
 .fa-link:before {
     content: "\f0c1"
 }
 
 .fa-cloud:before {
     content: "\f0c2"
 }
 
 .fa-flask:before {
     content: "\f0c3"
 }
 
 .fa-cut:before,
 .fa-scissors:before {
     content: "\f0c4"
 }
 
 .fa-copy:before,
 .fa-files-o:before {
     content: "\f0c5"
 }
 
 .fa-paperclip:before {
     content: "\f0c6"
 }
 
 .fa-floppy-o:before,
 .fa-save:before {
     content: "\f0c7"
 }
 
 .fa-square:before {
     content: "\f0c8"
 }
 
 .fa-bars:before,
 .fa-navicon:before,
 .fa-reorder:before {
     content: "\f0c9"
 }
 
 .fa-list-ul:before {
     content: "\f0ca"
 }
 
 .fa-list-ol:before {
     content: "\f0cb"
 }
 
 .fa-strikethrough:before {
     content: "\f0cc"
 }
 
 .fa-underline:before {
     content: "\f0cd"
 }
 
 .fa-table:before {
     content: "\f0ce"
 }
 
 .fa-magic:before {
     content: "\f0d0"
 }
 
 .fa-truck:before {
     content: "\f0d1"
 }
 
 .fa-pinterest:before {
     content: "\f0d2"
 }
 
 .fa-pinterest-square:before {
     content: "\f0d3"
 }
 
 .fa-google-plus-square:before {
     content: "\f0d4"
 }
 
 .fa-google-plus:before {
     content: "\f0d5"
 }
 
 .fa-money:before {
     content: "\f0d6"
 }
 
 .fa-caret-down:before {
     content: "\f0d7"
 }
 
 .fa-caret-up:before {
     content: "\f0d8"
 }
 
 .fa-caret-left:before {
     content: "\f0d9"
 }
 
 .fa-caret-right:before {
     content: "\f0da"
 }
 
 .fa-columns:before {
     content: "\f0db"
 }
 
 .fa-sort:before,
 .fa-unsorted:before {
     content: "\f0dc"
 }
 
 .fa-sort-desc:before,
 .fa-sort-down:before {
     content: "\f0dd"
 }
 
 .fa-sort-asc:before,
 .fa-sort-up:before {
     content: "\f0de"
 }
 
 .fa-envelope:before {
     content: "\f0e0"
 }
 
 .fa-linkedin:before {
     content: "\f0e1"
 }
 
 .fa-rotate-left:before,
 .fa-undo:before {
     content: "\f0e2"
 }
 
 .fa-gavel:before,
 .fa-legal:before {
     content: "\f0e3"
 }
 
 .fa-dashboard:before,
 .fa-tachometer:before {
     content: "\f0e4"
 }
 
 .fa-comment-o:before {
     content: "\f0e5"
 }
 
 .fa-comments-o:before {
     content: "\f0e6"
 }
 
 .fa-bolt:before,
 .fa-flash:before {
     content: "\f0e7"
 }
 
 .fa-sitemap:before {
     content: "\f0e8"
 }
 
 .fa-umbrella:before {
     content: "\f0e9"
 }
 
 .fa-clipboard:before,
 .fa-paste:before {
     content: "\f0ea"
 }
 
 .fa-lightbulb-o:before {
     content: "\f0eb"
 }
 
 .fa-exchange:before {
     content: "\f0ec"
 }
 
 .fa-cloud-download:before {
     content: "\f0ed"
 }
 
 .fa-cloud-upload:before {
     content: "\f0ee"
 }
 
 .fa-user-md:before {
     content: "\f0f0"
 }
 
 .fa-stethoscope:before {
     content: "\f0f1"
 }
 
 .fa-suitcase:before {
     content: "\f0f2"
 }
 
 .fa-bell-o:before {
     content: "\f0a2"
 }
 
 .fa-coffee:before {
     content: "\f0f4"
 }
 
 .fa-cutlery:before {
     content: "\f0f5"
 }
 
 .fa-file-text-o:before {
     content: "\f0f6"
 }
 
 .fa-building-o:before {
     content: "\f0f7"
 }
 
 .fa-hospital-o:before {
     content: "\f0f8"
 }
 
 .fa-ambulance:before {
     content: "\f0f9"
 }
 
 .fa-medkit:before {
     content: "\f0fa"
 }
 
 .fa-fighter-jet:before {
     content: "\f0fb"
 }
 
 .fa-beer:before {
     content: "\f0fc"
 }
 
 .fa-h-square:before {
     content: "\f0fd"
 }
 
 .fa-plus-square:before {
     content: "\f0fe"
 }
 
 .fa-angle-double-left:before {
     content: "\f100"
 }
 
 .fa-angle-double-right:before {
     content: "\f101"
 }
 
 .fa-angle-double-up:before {
     content: "\f102"
 }
 
 .fa-angle-double-down:before {
     content: "\f103"
 }
 
 .fa-angle-left:before {
     content: "\f104"
 }
 
 .fa-angle-right:before {
     content: "\f105"
 }
 
 .fa-angle-up:before {
     content: "\f106"
 }
 
 .fa-angle-down:before {
     content: "\f107"
 }
 
 .fa-desktop:before {
     content: "\f108"
 }
 
 .fa-laptop:before {
     content: "\f109"
 }
 
 .fa-tablet:before {
     content: "\f10a"
 }
 
 .fa-mobile-phone:before,
 .fa-mobile:before {
     content: "\f10b"
 }
 
 .fa-circle-o:before {
     content: "\f10c"
 }
 
 .fa-quote-left:before {
     content: "\f10d"
 }
 
 .fa-quote-right:before {
     content: "\f10e"
 }
 
 .fa-spinner:before {
     content: "\f110"
 }
 
 .fa-circle:before {
     content: "\f111"
 }
 
 .fa-mail-reply:before,
 .fa-reply:before {
     content: "\f112"
 }
 
 .fa-github-alt:before {
     content: "\f113"
 }
 
 .fa-folder-o:before {
     content: "\f114"
 }
 
 .fa-folder-open-o:before {
     content: "\f115"
 }
 
 .fa-smile-o:before {
     content: "\f118"
 }
 
 .fa-frown-o:before {
     content: "\f119"
 }
 
 .fa-meh-o:before {
     content: "\f11a"
 }
 
 .fa-gamepad:before {
     content: "\f11b"
 }
 
 .fa-keyboard-o:before {
     content: "\f11c"
 }
 
 .fa-flag-o:before {
     content: "\f11d"
 }
 
 .fa-flag-checkered:before {
     content: "\f11e"
 }
 
 .fa-terminal:before {
     content: "\f120"
 }
 
 .fa-code:before {
     content: "\f121"
 }
 
 .fa-mail-reply-all:before,
 .fa-reply-all:before {
     content: "\f122"
 }
 
 .fa-star-half-empty:before,
 .fa-star-half-full:before,
 .fa-star-half-o:before {
     content: "\f123"
 }
 
 .fa-location-arrow:before {
     content: "\f124"
 }
 
 .fa-crop:before {
     content: "\f125"
 }
 
 .fa-code-fork:before {
     content: "\f126"
 }
 
 .fa-chain-broken:before,
 .fa-unlink:before {
     content: "\f127"
 }
 
 .fa-question:before {
     content: "\f128"
 }
 
 .fa-info:before {
     content: "\f129"
 }
 
 .fa-exclamation:before {
     content: "\f12a"
 }
 
 .fa-superscript:before {
     content: "\f12b"
 }
 
 .fa-subscript:before {
     content: "\f12c"
 }
 
 .fa-eraser:before {
     content: "\f12d"
 }
 
 .fa-puzzle-piece:before {
     content: "\f12e"
 }
 
 .fa-microphone:before {
     content: "\f130"
 }
 
 .fa-microphone-slash:before {
     content: "\f131"
 }
 
 .fa-shield:before {
     content: "\f132"
 }
 
 .fa-calendar-o:before {
     content: "\f133"
 }
 
 .fa-fire-extinguisher:before {
     content: "\f134"
 }
 
 .fa-rocket:before {
     content: "\f135"
 }
 
 .fa-maxcdn:before {
     content: "\f136"
 }
 
 .fa-chevron-circle-left:before {
     content: "\f137"
 }
 
 .fa-chevron-circle-right:before {
     content: "\f138"
 }
 
 .fa-chevron-circle-up:before {
     content: "\f139"
 }
 
 .fa-chevron-circle-down:before {
     content: "\f13a"
 }
 
 .fa-html5:before {
     content: "\f13b"
 }
 
 .fa-css3:before {
     content: "\f13c"
 }
 
 .fa-anchor:before {
     content: "\f13d"
 }
 
 .fa-unlock-alt:before {
     content: "\f13e"
 }
 
 .fa-bullseye:before {
     content: "\f140"
 }
 
 .fa-ellipsis-h:before {
     content: "\f141"
 }
 
 .fa-ellipsis-v:before {
     content: "\f142"
 }
 
 .fa-rss-square:before {
     content: "\f143"
 }
 
 .fa-play-circle:before {
     content: "\f144"
 }
 
 .fa-ticket:before {
     content: "\f145"
 }
 
 .fa-minus-square:before {
     content: "\f146"
 }
 
 .fa-minus-square-o:before {
     content: "\f147"
 }
 
 .fa-level-up:before {
     content: "\f148"
 }
 
 .fa-level-down:before {
     content: "\f149"
 }
 
 .fa-check-square:before {
     content: "\f14a"
 }
 
 .fa-pencil-square:before {
     content: "\f14b"
 }
 
 .fa-external-link-square:before {
     content: "\f14c"
 }
 
 .fa-share-square:before {
     content: "\f14d"
 }
 
 .fa-compass:before {
     content: "\f14e"
 }
 
 .fa-caret-square-o-down:before,
 .fa-toggle-down:before {
     content: "\f150"
 }
 
 .fa-caret-square-o-up:before,
 .fa-toggle-up:before {
     content: "\f151"
 }
 
 .fa-caret-square-o-right:before,
 .fa-toggle-right:before {
     content: "\f152"
 }
 
 .fa-eur:before,
 .fa-euro:before {
     content: "\f153"
 }
 
 .fa-gbp:before {
     content: "\f154"
 }
 
 .fa-dollar:before,
 .fa-usd:before {
     content: "\f155"
 }
 
 .fa-inr:before,
 .fa-rupee:before {
     content: "\f156"
 }
 
 .fa-cny:before,
 .fa-jpy:before,
 .fa-rmb:before,
 .fa-yen:before {
     content: "\f157"
 }
 
 .fa-rouble:before,
 .fa-rub:before,
 .fa-ruble:before {
     content: "\f158"
 }
 
 .fa-krw:before,
 .fa-won:before {
     content: "\f159"
 }
 
 .fa-bitcoin:before,
 .fa-btc:before {
     content: "\f15a"
 }
 
 .fa-file:before {
     content: "\f15b"
 }
 
 .fa-file-text:before {
     content: "\f15c"
 }
 
 .fa-sort-alpha-asc:before {
     content: "\f15d"
 }
 
 .fa-sort-alpha-desc:before {
     content: "\f15e"
 }
 
 .fa-sort-amount-asc:before {
     content: "\f160"
 }
 
 .fa-sort-amount-desc:before {
     content: "\f161"
 }
 
 .fa-sort-numeric-asc:before {
     content: "\f162"
 }
 
 .fa-sort-numeric-desc:before {
     content: "\f163"
 }
 
 .fa-thumbs-up:before {
     content: "\f164"
 }
 
 .fa-thumbs-down:before {
     content: "\f165"
 }
 
 .fa-youtube-square:before {
     content: "\f166"
 }
 
 .fa-youtube:before {
     content: "\f167"
 }
 
 .fa-xing:before {
     content: "\f168"
 }
 
 .fa-xing-square:before {
     content: "\f169"
 }
 
 .fa-youtube-play:before {
     content: "\f16a"
 }
 
 .fa-dropbox:before {
     content: "\f16b"
 }
 
 .fa-stack-overflow:before {
     content: "\f16c"
 }
 
 .fa-instagram:before {
     content: "\f16d"
 }
 
 .fa-flickr:before {
     content: "\f16e"
 }
 
 .fa-adn:before {
     content: "\f170"
 }
 
 .fa-bitbucket:before {
     content: "\f171"
 }
 
 .fa-bitbucket-square:before {
     content: "\f172"
 }
 
 .fa-tumblr:before {
     content: "\f173"
 }
 
 .fa-tumblr-square:before {
     content: "\f174"
 }
 
 .fa-long-arrow-down:before {
     content: "\f175"
 }
 
 .fa-long-arrow-up:before {
     content: "\f176"
 }
 
 .fa-long-arrow-left:before {
     content: "\f177"
 }
 
 .fa-long-arrow-right:before {
     content: "\f178"
 }
 
 .fa-apple:before {
     content: "\f179"
 }
 
 .fa-windows:before {
     content: "\f17a"
 }
 
 .fa-android:before {
     content: "\f17b"
 }
 
 .fa-linux:before {
     content: "\f17c"
 }
 
 .fa-dribbble:before {
     content: "\f17d"
 }
 
 .fa-skype:before {
     content: "\f17e"
 }
 
 .fa-foursquare:before {
     content: "\f180"
 }
 
 .fa-trello:before {
     content: "\f181"
 }
 
 .fa-female:before {
     content: "\f182"
 }
 
 .fa-male:before {
     content: "\f183"
 }
 
 .fa-gittip:before,
 .fa-gratipay:before {
     content: "\f184"
 }
 
 .fa-sun-o:before {
     content: "\f185"
 }
 
 .fa-moon-o:before {
     content: "\f186"
 }
 
 .fa-archive:before {
     content: "\f187"
 }
 
 .fa-bug:before {
     content: "\f188"
 }
 
 .fa-vk:before {
     content: "\f189"
 }
 
 .fa-weibo:before {
     content: "\f18a"
 }
 
 .fa-renren:before {
     content: "\f18b"
 }
 
 .fa-pagelines:before {
     content: "\f18c"
 }
 
 .fa-stack-exchange:before {
     content: "\f18d"
 }
 
 .fa-arrow-circle-o-right:before {
     content: "\f18e"
 }
 
 .fa-arrow-circle-o-left:before {
     content: "\f190"
 }
 
 .fa-caret-square-o-left:before,
 .fa-toggle-left:before {
     content: "\f191"
 }
 
 .fa-dot-circle-o:before {
     content: "\f192"
 }
 
 .fa-wheelchair:before {
     content: "\f193"
 }
 
 .fa-vimeo-square:before {
     content: "\f194"
 }
 
 .fa-try:before,
 .fa-turkish-lira:before {
     content: "\f195"
 }
 
 .fa-plus-square-o:before {
     content: "\f196"
 }
 
 .fa-space-shuttle:before {
     content: "\f197"
 }
 
 .fa-slack:before {
     content: "\f198"
 }
 
 .fa-envelope-square:before {
     content: "\f199"
 }
 
 .fa-wordpress:before {
     content: "\f19a"
 }
 
 .fa-openid:before {
     content: "\f19b"
 }
 
 .fa-bank:before,
 .fa-institution:before,
 .fa-university:before {
     content: "\f19c"
 }
 
 .fa-graduation-cap:before,
 .fa-mortar-board:before {
     content: "\f19d"
 }
 
 .fa-yahoo:before {
     content: "\f19e"
 }
 
 .fa-google:before {
     content: "\f1a0"
 }
 
 .fa-reddit:before {
     content: "\f1a1"
 }
 
 .fa-reddit-square:before {
     content: "\f1a2"
 }
 
 .fa-stumbleupon-circle:before {
     content: "\f1a3"
 }
 
 .fa-stumbleupon:before {
     content: "\f1a4"
 }
 
 .fa-delicious:before {
     content: "\f1a5"
 }
 
 .fa-digg:before {
     content: "\f1a6"
 }
 
 .fa-pied-piper-pp:before {
     content: "\f1a7"
 }
 
 .fa-pied-piper-alt:before {
     content: "\f1a8"
 }
 
 .fa-drupal:before {
     content: "\f1a9"
 }
 
 .fa-joomla:before {
     content: "\f1aa"
 }
 
 .fa-language:before {
     content: "\f1ab"
 }
 
 .fa-fax:before {
     content: "\f1ac"
 }
 
 .fa-building:before {
     content: "\f1ad"
 }
 
 .fa-child:before {
     content: "\f1ae"
 }
 
 .fa-paw:before {
     content: "\f1b0"
 }
 
 .fa-spoon:before {
     content: "\f1b1"
 }
 
 .fa-cube:before {
     content: "\f1b2"
 }
 
 .fa-cubes:before {
     content: "\f1b3"
 }
 
 .fa-behance:before {
     content: "\f1b4"
 }
 
 .fa-behance-square:before {
     content: "\f1b5"
 }
 
 .fa-steam:before {
     content: "\f1b6"
 }
 
 .fa-steam-square:before {
     content: "\f1b7"
 }
 
 .fa-recycle:before {
     content: "\f1b8"
 }
 
 .fa-automobile:before,
 .fa-car:before {
     content: "\f1b9"
 }
 
 .fa-cab:before,
 .fa-taxi:before {
     content: "\f1ba"
 }
 
 .fa-tree:before {
     content: "\f1bb"
 }
 
 .fa-spotify:before {
     content: "\f1bc"
 }
 
 .fa-deviantart:before {
     content: "\f1bd"
 }
 
 .fa-soundcloud:before {
     content: "\f1be"
 }
 
 .fa-database:before {
     content: "\f1c0"
 }
 
 .fa-file-pdf-o:before {
     content: "\f1c1"
 }
 
 .fa-file-word-o:before {
     content: "\f1c2"
 }
 
 .fa-file-excel-o:before {
     content: "\f1c3"
 }
 
 .fa-file-powerpoint-o:before {
     content: "\f1c4"
 }
 
 .fa-file-image-o:before,
 .fa-file-photo-o:before,
 .fa-file-picture-o:before {
     content: "\f1c5"
 }
 
 .fa-file-archive-o:before,
 .fa-file-zip-o:before {
     content: "\f1c6"
 }
 
 .fa-file-audio-o:before,
 .fa-file-sound-o:before {
     content: "\f1c7"
 }
 
 .fa-file-movie-o:before,
 .fa-file-video-o:before {
     content: "\f1c8"
 }
 
 .fa-file-code-o:before {
     content: "\f1c9"
 }
 
 .fa-vine:before {
     content: "\f1ca"
 }
 
 .fa-codepen:before {
     content: "\f1cb"
 }
 
 .fa-jsfiddle:before {
     content: "\f1cc"
 }
 
 .fa-life-bouy:before,
 .fa-life-buoy:before,
 .fa-life-ring:before,
 .fa-life-saver:before,
 .fa-support:before {
     content: "\f1cd"
 }
 
 .fa-circle-o-notch:before {
     content: "\f1ce"
 }
 
 .fa-ra:before,
 .fa-rebel:before,
 .fa-resistance:before {
     content: "\f1d0"
 }
 
 .fa-empire:before,
 .fa-ge:before {
     content: "\f1d1"
 }
 
 .fa-git-square:before {
     content: "\f1d2"
 }
 
 .fa-git:before {
     content: "\f1d3"
 }
 
 .fa-hacker-news:before,
 .fa-y-combinator-square:before,
 .fa-yc-square:before {
     content: "\f1d4"
 }
 
 .fa-tencent-weibo:before {
     content: "\f1d5"
 }
 
 .fa-qq:before {
     content: "\f1d6"
 }
 
 .fa-wechat:before,
 .fa-weixin:before {
     content: "\f1d7"
 }
 
 .fa-paper-plane:before,
 .fa-send:before {
     content: "\f1d8"
 }
 
 .fa-paper-plane-o:before,
 .fa-send-o:before {
     content: "\f1d9"
 }
 
 .fa-history:before {
     content: "\f1da"
 }
 
 .fa-circle-thin:before {
     content: "\f1db"
 }
 
 .fa-header:before {
     content: "\f1dc"
 }
 
 .fa-paragraph:before {
     content: "\f1dd"
 }
 
 .fa-sliders:before {
     content: "\f1de"
 }
 
 .fa-share-alt:before {
     content: "\f1e0"
 }
 
 .fa-share-alt-square:before {
     content: "\f1e1"
 }
 
 .fa-bomb:before {
     content: "\f1e2"
 }
 
 .fa-futbol-o:before,
 .fa-soccer-ball-o:before {
     content: "\f1e3"
 }
 
 .fa-tty:before {
     content: "\f1e4"
 }
 
 .fa-binoculars:before {
     content: "\f1e5"
 }
 
 .fa-plug:before {
     content: "\f1e6"
 }
 
 .fa-slideshare:before {
     content: "\f1e7"
 }
 
 .fa-twitch:before {
     content: "\f1e8"
 }
 
 .fa-yelp:before {
     content: "\f1e9"
 }
 
 .fa-newspaper-o:before {
     content: "\f1ea"
 }
 
 .fa-wifi:before {
     content: "\f1eb"
 }
 
 .fa-calculator:before {
     content: "\f1ec"
 }
 
 .fa-paypal:before {
     content: "\f1ed"
 }
 
 .fa-google-wallet:before {
     content: "\f1ee"
 }
 
 .fa-cc-visa:before {
     content: "\f1f0"
 }
 
 .fa-cc-mastercard:before {
     content: "\f1f1"
 }
 
 .fa-cc-discover:before {
     content: "\f1f2"
 }
 
 .fa-cc-amex:before {
     content: "\f1f3"
 }
 
 .fa-cc-paypal:before {
     content: "\f1f4"
 }
 
 .fa-cc-stripe:before {
     content: "\f1f5"
 }
 
 .fa-bell-slash:before {
     content: "\f1f6"
 }
 
 .fa-bell-slash-o:before {
     content: "\f1f7"
 }
 
 .fa-trash:before {
     content: "\f1f8"
 }
 
 .fa-copyright:before {
     content: "\f1f9"
 }
 
 .fa-at:before {
     content: "\f1fa"
 }
 
 .fa-eyedropper:before {
     content: "\f1fb"
 }
 
 .fa-paint-brush:before {
     content: "\f1fc"
 }
 
 .fa-birthday-cake:before {
     content: "\f1fd"
 }
 
 .fa-area-chart:before {
     content: "\f1fe"
 }
 
 .fa-pie-chart:before {
     content: "\f200"
 }
 
 .fa-line-chart:before {
     content: "\f201"
 }
 
 .fa-lastfm:before {
     content: "\f202"
 }
 
 .fa-lastfm-square:before {
     content: "\f203"
 }
 
 .fa-toggle-off:before {
     content: "\f204"
 }
 
 .fa-toggle-on:before {
     content: "\f205"
 }
 
 .fa-bicycle:before {
     content: "\f206"
 }
 
 .fa-bus:before {
     content: "\f207"
 }
 
 .fa-ioxhost:before {
     content: "\f208"
 }
 
 .fa-angellist:before {
     content: "\f209"
 }
 
 .fa-cc:before {
     content: "\f20a"
 }
 
 .fa-ils:before,
 .fa-shekel:before,
 .fa-sheqel:before {
     content: "\f20b"
 }
 
 .fa-meanpath:before {
     content: "\f20c"
 }
 
 .fa-buysellads:before {
     content: "\f20d"
 }
 
 .fa-connectdevelop:before {
     content: "\f20e"
 }
 
 .fa-dashcube:before {
     content: "\f210"
 }
 
 .fa-forumbee:before {
     content: "\f211"
 }
 
 .fa-leanpub:before {
     content: "\f212"
 }
 
 .fa-sellsy:before {
     content: "\f213"
 }
 
 .fa-shirtsinbulk:before {
     content: "\f214"
 }
 
 .fa-simplybuilt:before {
     content: "\f215"
 }
 
 .fa-skyatlas:before {
     content: "\f216"
 }
 
 .fa-cart-plus:before {
     content: "\f217"
 }
 
 .fa-cart-arrow-down:before {
     content: "\f218"
 }
 
 .fa-diamond:before {
     content: "\f219"
 }
 
 .fa-ship:before {
     content: "\f21a"
 }
 
 .fa-user-secret:before {
     content: "\f21b"
 }
 
 .fa-motorcycle:before {
     content: "\f21c"
 }
 
 .fa-street-view:before {
     content: "\f21d"
 }
 
 .fa-heartbeat:before {
     content: "\f21e"
 }
 
 .fa-venus:before {
     content: "\f221"
 }
 
 .fa-mars:before {
     content: "\f222"
 }
 
 .fa-mercury:before {
     content: "\f223"
 }
 
 .fa-intersex:before,
 .fa-transgender:before {
     content: "\f224"
 }
 
 .fa-transgender-alt:before {
     content: "\f225"
 }
 
 .fa-venus-double:before {
     content: "\f226"
 }
 
 .fa-mars-double:before {
     content: "\f227"
 }
 
 .fa-venus-mars:before {
     content: "\f228"
 }
 
 .fa-mars-stroke:before {
     content: "\f229"
 }
 
 .fa-mars-stroke-v:before {
     content: "\f22a"
 }
 
 .fa-mars-stroke-h:before {
     content: "\f22b"
 }
 
 .fa-neuter:before {
     content: "\f22c"
 }
 
 .fa-genderless:before {
     content: "\f22d"
 }
 
 .fa-facebook-official:before {
     content: "\f230"
 }
 
 .fa-pinterest-p:before {
     content: "\f231"
 }
 
 .fa-whatsapp:before {
     content: "\f232"
 }
 
 .fa-server:before {
     content: "\f233"
 }
 
 .fa-user-plus:before {
     content: "\f234"
 }
 
 .fa-user-times:before {
     content: "\f235"
 }
 
 .fa-bed:before,
 .fa-hotel:before {
     content: "\f236"
 }
 
 .fa-viacoin:before {
     content: "\f237"
 }
 
 .fa-train:before {
     content: "\f238"
 }
 
 .fa-subway:before {
     content: "\f239"
 }
 
 .fa-medium:before {
     content: "\f23a"
 }
 
 .fa-y-combinator:before,
 .fa-yc:before {
     content: "\f23b"
 }
 
 .fa-optin-monster:before {
     content: "\f23c"
 }
 
 .fa-opencart:before {
     content: "\f23d"
 }
 
 .fa-expeditedssl:before {
     content: "\f23e"
 }
 
 .fa-battery-4:before,
 .fa-battery-full:before,
 .fa-battery:before {
     content: "\f240"
 }
 
 .fa-battery-3:before,
 .fa-battery-three-quarters:before {
     content: "\f241"
 }
 
 .fa-battery-2:before,
 .fa-battery-half:before {
     content: "\f242"
 }
 
 .fa-battery-1:before,
 .fa-battery-quarter:before {
     content: "\f243"
 }
 
 .fa-battery-0:before,
 .fa-battery-empty:before {
     content: "\f244"
 }
 
 .fa-mouse-pointer:before {
     content: "\f245"
 }
 
 .fa-i-cursor:before {
     content: "\f246"
 }
 
 .fa-object-group:before {
     content: "\f247"
 }
 
 .fa-object-ungroup:before {
     content: "\f248"
 }
 
 .fa-sticky-note:before {
     content: "\f249"
 }
 
 .fa-sticky-note-o:before {
     content: "\f24a"
 }
 
 .fa-cc-jcb:before {
     content: "\f24b"
 }
 
 .fa-cc-diners-club:before {
     content: "\f24c"
 }
 
 .fa-clone:before {
     content: "\f24d"
 }
 
 .fa-balance-scale:before {
     content: "\f24e"
 }
 
 .fa-hourglass-o:before {
     content: "\f250"
 }
 
 .fa-hourglass-1:before,
 .fa-hourglass-start:before {
     content: "\f251"
 }
 
 .fa-hourglass-2:before,
 .fa-hourglass-half:before {
     content: "\f252"
 }
 
 .fa-hourglass-3:before,
 .fa-hourglass-end:before {
     content: "\f253"
 }
 
 .fa-hourglass:before {
     content: "\f254"
 }
 
 .fa-hand-grab-o:before,
 .fa-hand-rock-o:before {
     content: "\f255"
 }
 
 .fa-hand-paper-o:before,
 .fa-hand-stop-o:before {
     content: "\f256"
 }
 
 .fa-hand-scissors-o:before {
     content: "\f257"
 }
 
 .fa-hand-lizard-o:before {
     content: "\f258"
 }
 
 .fa-hand-spock-o:before {
     content: "\f259"
 }
 
 .fa-hand-pointer-o:before {
     content: "\f25a"
 }
 
 .fa-hand-peace-o:before {
     content: "\f25b"
 }
 
 .fa-trademark:before {
     content: "\f25c"
 }
 
 .fa-registered:before {
     content: "\f25d"
 }
 
 .fa-creative-commons:before {
     content: "\f25e"
 }
 
 .fa-gg:before {
     content: "\f260"
 }
 
 .fa-gg-circle:before {
     content: "\f261"
 }
 
 .fa-tripadvisor:before {
     content: "\f262"
 }
 
 .fa-odnoklassniki:before {
     content: "\f263"
 }
 
 .fa-odnoklassniki-square:before {
     content: "\f264"
 }
 
 .fa-get-pocket:before {
     content: "\f265"
 }
 
 .fa-wikipedia-w:before {
     content: "\f266"
 }
 
 .fa-safari:before {
     content: "\f267"
 }
 
 .fa-chrome:before {
     content: "\f268"
 }
 
 .fa-firefox:before {
     content: "\f269"
 }
 
 .fa-opera:before {
     content: "\f26a"
 }
 
 .fa-internet-explorer:before {
     content: "\f26b"
 }
 
 .fa-television:before,
 .fa-tv:before {
     content: "\f26c"
 }
 
 .fa-contao:before {
     content: "\f26d"
 }
 
 .fa-500px:before {
     content: "\f26e"
 }
 
 .fa-amazon:before {
     content: "\f270"
 }
 
 .fa-calendar-plus-o:before {
     content: "\f271"
 }
 
 .fa-calendar-minus-o:before {
     content: "\f272"
 }
 
 .fa-calendar-times-o:before {
     content: "\f273"
 }
 
 .fa-calendar-check-o:before {
     content: "\f274"
 }
 
 .fa-industry:before {
     content: "\f275"
 }
 
 .fa-map-pin:before {
     content: "\f276"
 }
 
 .fa-map-signs:before {
     content: "\f277"
 }
 
 .fa-map-o:before {
     content: "\f278"
 }
 
 .fa-map:before {
     content: "\f279"
 }
 
 .fa-commenting:before {
     content: "\f27a"
 }
 
 .fa-commenting-o:before {
     content: "\f27b"
 }
 
 .fa-houzz:before {
     content: "\f27c"
 }
 
 .fa-vimeo:before {
     content: "\f27d"
 }
 
 .fa-black-tie:before {
     content: "\f27e"
 }
 
 .fa-fonticons:before {
     content: "\f280"
 }
 
 .fa-reddit-alien:before {
     content: "\f281"
 }
 
 .fa-edge:before {
     content: "\f282"
 }
 
 .fa-credit-card-alt:before {
     content: "\f283"
 }
 
 .fa-codiepie:before {
     content: "\f284"
 }
 
 .fa-modx:before {
     content: "\f285"
 }
 
 .fa-fort-awesome:before {
     content: "\f286"
 }
 
 .fa-usb:before {
     content: "\f287"
 }
 
 .fa-product-hunt:before {
     content: "\f288"
 }
 
 .fa-mixcloud:before {
     content: "\f289"
 }
 
 .fa-scribd:before {
     content: "\f28a"
 }
 
 .fa-pause-circle:before {
     content: "\f28b"
 }
 
 .fa-pause-circle-o:before {
     content: "\f28c"
 }
 
 .fa-stop-circle:before {
     content: "\f28d"
 }
 
 .fa-stop-circle-o:before {
     content: "\f28e"
 }
 
 .fa-shopping-bag:before {
     content: "\f290"
 }
 
 .fa-shopping-basket:before {
     content: "\f291"
 }
 
 .fa-hashtag:before {
     content: "\f292"
 }
 
 .fa-bluetooth:before {
     content: "\f293"
 }
 
 .fa-bluetooth-b:before {
     content: "\f294"
 }
 
 .fa-percent:before {
     content: "\f295"
 }
 
 .fa-gitlab:before {
     content: "\f296"
 }
 
 .fa-wpbeginner:before {
     content: "\f297"
 }
 
 .fa-wpforms:before {
     content: "\f298"
 }
 
 .fa-envira:before {
     content: "\f299"
 }
 
 .fa-universal-access:before {
     content: "\f29a"
 }
 
 .fa-wheelchair-alt:before {
     content: "\f29b"
 }
 
 .fa-question-circle-o:before {
     content: "\f29c"
 }
 
 .fa-blind:before {
     content: "\f29d"
 }
 
 .fa-audio-description:before {
     content: "\f29e"
 }
 
 .fa-volume-control-phone:before {
     content: "\f2a0"
 }
 
 .fa-braille:before {
     content: "\f2a1"
 }
 
 .fa-assistive-listening-systems:before {
     content: "\f2a2"
 }
 
 .fa-american-sign-language-interpreting:before,
 .fa-asl-interpreting:before {
     content: "\f2a3"
 }
 
 .fa-deaf:before,
 .fa-deafness:before,
 .fa-hard-of-hearing:before {
     content: "\f2a4"
 }
 
 .fa-glide:before {
     content: "\f2a5"
 }
 
 .fa-glide-g:before {
     content: "\f2a6"
 }
 
 .fa-sign-language:before,
 .fa-signing:before {
     content: "\f2a7"
 }
 
 .fa-low-vision:before {
     content: "\f2a8"
 }
 
 .fa-viadeo:before {
     content: "\f2a9"
 }
 
 .fa-viadeo-square:before {
     content: "\f2aa"
 }
 
 .fa-snapchat:before {
     content: "\f2ab"
 }
 
 .fa-snapchat-ghost:before {
     content: "\f2ac"
 }
 
 .fa-snapchat-square:before {
     content: "\f2ad"
 }
 
 .fa-pied-piper:before {
     content: "\f2ae"
 }
 
 .fa-first-order:before {
     content: "\f2b0"
 }
 
 .fa-yoast:before {
     content: "\f2b1"
 }
 
 .fa-themeisle:before {
     content: "\f2b2"
 }
 
 .fa-google-plus-circle:before,
 .fa-google-plus-official:before {
     content: "\f2b3"
 }
 
 .fa-fa:before,
 .fa-font-awesome:before {
     content: "\f2b4"
 }
 
 .fa-handshake-o:before {
     content: "\f2b5"
 }
 
 .fa-envelope-open:before {
     content: "\f2b6"
 }
 
 .fa-envelope-open-o:before {
     content: "\f2b7"
 }
 
 .fa-linode:before {
     content: "\f2b8"
 }
 
 .fa-address-book:before {
     content: "\f2b9"
 }
 
 .fa-address-book-o:before {
     content: "\f2ba"
 }
 
 .fa-address-card:before,
 .fa-vcard:before {
     content: "\f2bb"
 }
 
 .fa-address-card-o:before,
 .fa-vcard-o:before {
     content: "\f2bc"
 }
 
 .fa-user-circle:before {
     content: "\f2bd"
 }
 
 .fa-user-circle-o:before {
     content: "\f2be"
 }
 
 .fa-user-o:before {
     content: "\f2c0"
 }
 
 .fa-id-badge:before {
     content: "\f2c1"
 }
 
 .fa-drivers-license:before,
 .fa-id-card:before {
     content: "\f2c2"
 }
 
 .fa-drivers-license-o:before,
 .fa-id-card-o:before {
     content: "\f2c3"
 }
 
 .fa-quora:before {
     content: "\f2c4"
 }
 
 .fa-free-code-camp:before {
     content: "\f2c5"
 }
 
 .fa-telegram:before {
     content: "\f2c6"
 }
 
 .fa-thermometer-4:before,
 .fa-thermometer-full:before,
 .fa-thermometer:before {
     content: "\f2c7"
 }
 
 .fa-thermometer-3:before,
 .fa-thermometer-three-quarters:before {
     content: "\f2c8"
 }
 
 .fa-thermometer-2:before,
 .fa-thermometer-half:before {
     content: "\f2c9"
 }
 
 .fa-thermometer-1:before,
 .fa-thermometer-quarter:before {
     content: "\f2ca"
 }
 
 .fa-thermometer-0:before,
 .fa-thermometer-empty:before {
     content: "\f2cb"
 }
 
 .fa-shower:before {
     content: "\f2cc"
 }
 
 .fa-bath:before,
 .fa-bathtub:before,
 .fa-s15:before {
     content: "\f2cd"
 }
 
 .fa-podcast:before {
     content: "\f2ce"
 }
 
 .fa-window-maximize:before {
     content: "\f2d0"
 }
 
 .fa-window-minimize:before {
     content: "\f2d1"
 }
 
 .fa-window-restore:before {
     content: "\f2d2"
 }
 
 .fa-times-rectangle:before,
 .fa-window-close:before {
     content: "\f2d3"
 }
 
 .fa-times-rectangle-o:before,
 .fa-window-close-o:before {
     content: "\f2d4"
 }
 
 .fa-bandcamp:before {
     content: "\f2d5"
 }
 
 .fa-grav:before {
     content: "\f2d6"
 }
 
 .fa-etsy:before {
     content: "\f2d7"
 }
 
 .fa-imdb:before {
     content: "\f2d8"
 }
 
 .fa-ravelry:before {
     content: "\f2d9"
 }
 
 .fa-eercast:before {
     content: "\f2da"
 }
 
 .fa-microchip:before {
     content: "\f2db"
 }
 
 .fa-snowflake-o:before {
     content: "\f2dc"
 }
 
 .fa-superpowers:before {
     content: "\f2dd"
 }
 
 .fa-wpexplorer:before {
     content: "\f2de"
 }
 
 .fa-meetup:before {
     content: "\f2e0"
 }
 
 .sr-only {
     position: absolute;
     width: 1px;
     height: 1px;
     padding: 0;
     margin: -1px;
     overflow: hidden;
     clip: rect(0, 0, 0, 0);
     border: 0
 }
 
 .sr-only-focusable:active,
 .sr-only-focusable:focus {
     position: static;
     width: auto;
     height: auto;
     margin: 0;
     overflow: visible;
     clip: auto
 }
 
 .fc {
     direction: ltr;
     text-align: left
 }
 
 .fc-rtl {
     text-align: right
 }
 
 body .fc {
     font-size: 1em
 }
 
 .fc-highlight {
     background: #bce8f1;
     opacity: .3
 }
 
 .fc-bgevent {
     background: #8fdf82;
     opacity: .3
 }
 
 .fc-nonbusiness {
     background: #d7d7d7
 }
 
 .fc button {
     box-sizing: border-box;
     margin: 0;
     height: 2.1em;
     padding: 0 .6em;
     font-size: 1em;
     white-space: nowrap;
     cursor: pointer
 }
 
 .fc button::-moz-focus-inner {
     margin: 0;
     padding: 0
 }
 
 .fc-state-default {
     border: 1px solid
 }
 
 .fc-state-default.fc-corner-left {
     border-top-left-radius: 4px;
     border-bottom-left-radius: 4px
 }
 
 .fc-state-default.fc-corner-right {
     border-top-right-radius: 4px;
     border-bottom-right-radius: 4px
 }
 
 .fc button .fc-icon {
     position: relative;
     top: -.05em;
     margin: 0 .2em;
     vertical-align: middle
 }
 
 .fc-state-default {
     background-color: #f5f5f5;
     background-image: linear-gradient(180deg, #fff, #e6e6e6);
     background-repeat: repeat-x;
     border-color: #e6e6e6 #e6e6e6 #bfbfbf;
     border-color: rgba(0, 0, 0, .1) rgba(0, 0, 0, .1) rgba(0, 0, 0, .25);
     color: #333;
     text-shadow: 0 1px 1px hsla(0, 0%, 100%, .75);
     box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .2), 0 1px 2px rgba(0, 0, 0, .05)
 }
 
 .fc-state-active,
 .fc-state-disabled,
 .fc-state-down,
 .fc-state-hover {
     color: #333;
     background-color: #e6e6e6
 }
 
 .fc-state-hover {
     color: #333;
     text-decoration: none;
     background-position: 0 -15px;
     transition: background-position .1s linear
 }
 
 .fc-state-active,
 .fc-state-down {
     background-color: #ccc;
     background-image: none;
     box-shadow: inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05)
 }
 
 .fc-state-disabled {
     cursor: default;
     background-image: none;
     opacity: .65;
     box-shadow: none
 }
 
 .fc-button-group {
     display: inline-block
 }
 
 .fc .fc-button-group>* {
     float: left;
     margin: 0 0 0 -1px
 }
 
 .fc .fc-button-group>:first-child {
     margin-left: 0
 }
 
 .fc-popover {
     position: absolute;
     box-shadow: 0 2px 6px rgba(0, 0, 0, .15)
 }
 
 .fc-popover .fc-header {
     padding: 2px 4px
 }
 
 .fc-popover .fc-header .fc-title {
     margin: 0 2px
 }
 
 .fc-popover .fc-header .fc-close {
     cursor: pointer
 }
 
 .fc-ltr .fc-popover .fc-header .fc-title,
 .fc-rtl .fc-popover .fc-header .fc-close {
     float: left
 }
 
 .fc-ltr .fc-popover .fc-header .fc-close,
 .fc-rtl .fc-popover .fc-header .fc-title {
     float: right
 }
 
 .fc-divider {
     border-style: solid;
     border-width: 1px
 }
 
 hr.fc-divider {
     height: 0;
     margin: 0;
     padding: 0 0 2px;
     border-width: 1px 0
 }
 
 .fc-clear {
     clear: both
 }
 
 .fc-bg,
 .fc-bgevent-skeleton,
 .fc-helper-skeleton,
 .fc-highlight-skeleton {
     position: absolute;
     top: 0;
     left: 0;
     right: 0
 }
 
 .fc-bg {
     bottom: 0
 }
 
 .fc-bg table {
     height: 100%
 }
 
 .fc table {
     width: 100%;
     box-sizing: border-box;
     table-layout: fixed;
     border-collapse: collapse;
     border-spacing: 0;
     font-size: 1em
 }
 
 .fc th {
     text-align: center
 }
 
 .fc td,
 .fc th {
     border-style: solid;
     border-width: 1px;
     padding: 0;
     vertical-align: top
 }
 
 .fc td.fc-today {
     border-style: double
 }
 
 a[data-goto] {
     cursor: pointer
 }
 
 a[data-goto]:hover {
     text-decoration: underline
 }
 
 .fc .fc-row {
     border-style: solid;
     border-width: 0
 }
 
 .fc-row table {
     border-left: 0 hidden transparent;
     border-right: 0 hidden transparent;
     border-bottom: 0 hidden transparent
 }
 
 .fc-row:first-child table {
     border-top: 0 hidden transparent
 }
 
 .fc-row {
     position: relative
 }
 
 .fc-row .fc-bg {
     z-index: 1
 }
 
 .fc-row .fc-bgevent-skeleton,
 .fc-row .fc-highlight-skeleton {
     bottom: 0
 }
 
 .fc-row .fc-bgevent-skeleton table,
 .fc-row .fc-highlight-skeleton table {
     height: 100%
 }
 
 .fc-row .fc-bgevent-skeleton td,
 .fc-row .fc-highlight-skeleton td {
     border-color: transparent
 }
 
 .fc-row .fc-bgevent-skeleton {
     z-index: 2
 }
 
 .fc-row .fc-highlight-skeleton {
     z-index: 3
 }
 
 .fc-row .fc-content-skeleton {
     position: relative;
     z-index: 4;
     padding-bottom: 2px
 }
 
 .fc-row .fc-helper-skeleton {
     z-index: 5
 }
 
 .fc .fc-row .fc-content-skeleton table,
 .fc .fc-row .fc-content-skeleton td,
 .fc .fc-row .fc-helper-skeleton td {
     background: none;
     border-color: transparent
 }
 
 .fc-row .fc-content-skeleton td,
 .fc-row .fc-helper-skeleton td {
     border-bottom: 0
 }
 
 .fc-row .fc-content-skeleton tbody td,
 .fc-row .fc-helper-skeleton tbody td {
     border-top: 0
 }
 
 .fc-scroller {
     -webkit-overflow-scrolling: touch
 }
 
 .fc-scroller>.fc-day-grid,
 .fc-scroller>.fc-time-grid {
     position: relative;
     width: 100%
 }
 
 .fc-event {
     position: relative;
     display: block;
     font-size: .85em;
     line-height: 1.3;
     border-radius: 3px;
     border: 1px solid #3a87ad
 }
 
 .fc-event,
 .fc-event-dot {
     background-color: #3a87ad
 }
 
 .fc-event,
 .fc-event:hover {
     color: #fff;
     text-decoration: none
 }
 
 .fc-event.fc-draggable,
 .fc-event[href] {
     cursor: pointer
 }
 
 .fc-not-allowed,
 .fc-not-allowed .fc-event {
     cursor: not-allowed
 }
 
 .fc-event .fc-bg {
     z-index: 1;
     background: #fff;
     opacity: .25
 }
 
 .fc-event .fc-content {
     position: relative;
     z-index: 2
 }
 
 .fc-event .fc-resizer {
     position: absolute;
     z-index: 4;
     display: none
 }
 
 .fc-event.fc-allow-mouse-resize .fc-resizer,
 .fc-event.fc-selected .fc-resizer {
     display: block
 }
 
 .fc-event.fc-selected .fc-resizer:before {
     content: "";
     position: absolute;
     z-index: 9999;
     top: 50%;
     left: 50%;
     width: 40px;
     height: 40px;
     margin-left: -20px;
     margin-top: -20px
 }
 
 .fc-event.fc-selected {
     z-index: 9999!important;
     box-shadow: 0 2px 5px rgba(0, 0, 0, .2)
 }
 
 .fc-event.fc-selected.fc-dragging {
     box-shadow: 0 2px 7px rgba(0, 0, 0, .3)
 }
 
 .fc-h-event.fc-selected:before {
     content: "";
     position: absolute;
     z-index: 3;
     top: -10px;
     bottom: -10px;
     left: 0;
     right: 0
 }
 
 .fc-ltr .fc-h-event.fc-not-start,
 .fc-rtl .fc-h-event.fc-not-end {
     margin-left: 0;
     border-left-width: 0;
     padding-left: 1px;
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .fc-ltr .fc-h-event.fc-not-end,
 .fc-rtl .fc-h-event.fc-not-start {
     margin-right: 0;
     border-right-width: 0;
     padding-right: 1px;
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .fc-ltr .fc-h-event .fc-start-resizer,
 .fc-rtl .fc-h-event .fc-end-resizer {
     cursor: w-resize;
     left: -1px
 }
 
 .fc-ltr .fc-h-event .fc-end-resizer,
 .fc-rtl .fc-h-event .fc-start-resizer {
     cursor: e-resize;
     right: -1px
 }
 
 .fc-h-event.fc-allow-mouse-resize .fc-resizer {
     width: 7px;
     top: -1px;
     bottom: -1px
 }
 
 .fc-h-event.fc-selected .fc-resizer {
     border-radius: 4px;
     border-width: 1px;
     width: 6px;
     height: 6px;
     border-style: solid;
     border-color: inherit;
     background: #fff;
     top: 50%;
     margin-top: -4px
 }
 
 .fc-ltr .fc-h-event.fc-selected .fc-start-resizer,
 .fc-rtl .fc-h-event.fc-selected .fc-end-resizer {
     margin-left: -4px
 }
 
 .fc-ltr .fc-h-event.fc-selected .fc-end-resizer,
 .fc-rtl .fc-h-event.fc-selected .fc-start-resizer {
     margin-right: -4px
 }
 
 .fc-day-grid-event {
     margin: 1px 2px 0;
     padding: 0 1px
 }
 
 tr:first-child>td>.fc-day-grid-event {
     margin-top: 2px
 }
 
 .fc-day-grid-event.fc-selected:after {
     content: "";
     position: absolute;
     z-index: 1;
     top: -1px;
     right: -1px;
     bottom: -1px;
     left: -1px;
     background: #000;
     opacity: .25
 }
 
 .fc-day-grid-event .fc-content {
     white-space: nowrap;
     overflow: hidden
 }
 
 .fc-day-grid-event .fc-time {
     font-weight: 700
 }
 
 .fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer,
 .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer {
     margin-left: -2px
 }
 
 .fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer,
 .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer {
     margin-right: -2px
 }
 
 a.fc-more {
     margin: 1px 3px;
     font-size: .85em;
     cursor: pointer;
     text-decoration: none
 }
 
 a.fc-more:hover {
     text-decoration: underline
 }
 
 .fc-limited {
     display: none
 }
 
 .fc-day-grid .fc-row {
     z-index: 1
 }
 
 .fc-more-popover {
     z-index: 2;
     width: 220px
 }
 
 .fc-more-popover .fc-event-container {
     padding: 10px
 }
 
 .fc-now-indicator {
     position: absolute;
     border: 0 solid red
 }
 
 .fc-unselectable {
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     -webkit-touch-callout: none;
     -webkit-tap-highlight-color: transparent
 }
 
 .fc-unthemed .fc-content,
 .fc-unthemed .fc-divider,
 .fc-unthemed .fc-list-heading td,
 .fc-unthemed .fc-list-view,
 .fc-unthemed .fc-popover,
 .fc-unthemed .fc-row,
 .fc-unthemed tbody,
 .fc-unthemed td,
 .fc-unthemed th,
 .fc-unthemed thead {
     border-color: #ddd
 }
 
 .fc-unthemed .fc-popover {
     background-color: #fff
 }
 
 .fc-unthemed .fc-divider,
 .fc-unthemed .fc-list-heading td,
 .fc-unthemed .fc-popover .fc-header {
     background: #eee
 }
 
 .fc-unthemed .fc-popover .fc-header .fc-close {
     color: #666
 }
 
 .fc-unthemed td.fc-today {
     background: #fcf8e3
 }
 
 .fc-unthemed .fc-disabled-day {
     background: #d7d7d7;
     opacity: .3
 }
 
 .fc-icon {
     display: inline-block;
     height: 1em;
     line-height: 1em;
     font-size: 1em;
     text-align: center;
     overflow: hidden;
     font-family: Courier New, Courier, monospace;
     -webkit-touch-callout: none;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none
 }
 
 .fc-icon:after {
     position: relative
 }
 
 .fc-icon-left-single-arrow:after {
     content: "\2039";
     font-weight: 700;
     font-size: 200%;
     top: -7%
 }
 
 .fc-icon-right-single-arrow:after {
     content: "\203A";
     font-weight: 700;
     font-size: 200%;
     top: -7%
 }
 
 .fc-icon-left-double-arrow:after {
     content: "\AB";
     font-size: 160%;
     top: -7%
 }
 
 .fc-icon-right-double-arrow:after {
     content: "\BB";
     font-size: 160%;
     top: -7%
 }
 
 .fc-icon-left-triangle:after {
     content: "\25C4";
     font-size: 125%;
     top: 3%
 }
 
 .fc-icon-right-triangle:after {
     content: "\25BA";
     font-size: 125%;
     top: 3%
 }
 
 .fc-icon-down-triangle:after {
     content: "\25BC";
     font-size: 125%;
     top: 2%
 }
 
 .fc-icon-x:after {
     content: "\D7";
     font-size: 200%;
     top: 6%
 }
 
 .fc-unthemed .fc-popover {
     border-width: 1px;
     border-style: solid
 }
 
 .fc-unthemed .fc-popover .fc-header .fc-close {
     font-size: .9em;
     margin-top: 2px
 }
 
 .fc-unthemed .fc-list-item:hover td {
     background-color: #f5f5f5
 }
 
 .ui-widget .fc-disabled-day {
     background-image: none
 }
 
 .fc-popover>.ui-widget-header+.ui-widget-content {
     border-top: 0
 }
 
 .ui-widget .fc-event {
     color: #fff;
     text-decoration: none;
     font-weight: 400
 }
 
 .ui-widget td.fc-axis {
     font-weight: 400
 }
 
 .fc-time-grid .fc-slats .ui-widget-content {
     background: none
 }
 
 .fc.fc-bootstrap3 a {
     text-decoration: none
 }
 
 .fc.fc-bootstrap3 a[data-goto]:hover {
     text-decoration: underline
 }
 
 .fc-bootstrap3 hr.fc-divider {
     border-color: inherit
 }
 
 .fc-bootstrap3 .fc-today.alert {
     border-radius: 0
 }
 
 .fc-bootstrap3 .fc-popover .panel-body {
     padding: 0
 }
 
 .fc-bootstrap3 .fc-time-grid .fc-slats table {
     background: none
 }
 
 .fc-toolbar {
     text-align: center
 }
 
 .fc-toolbar.fc-header-toolbar {
     margin-bottom: 1em
 }
 
 .fc-toolbar.fc-footer-toolbar {
     margin-top: 1em
 }
 
 .fc-toolbar .fc-left {
     float: left
 }
 
 .fc-toolbar .fc-right {
     float: right
 }
 
 .fc-toolbar .fc-center {
     display: inline-block
 }
 
 .fc .fc-toolbar>*>* {
     float: left;
     margin-left: .75em
 }
 
 .fc .fc-toolbar>*>:first-child {
     margin-left: 0
 }
 
 .fc-toolbar h2 {
     margin: 0
 }
 
 .fc-toolbar button {
     position: relative
 }
 
 .fc-toolbar .fc-state-hover,
 .fc-toolbar .ui-state-hover {
     z-index: 2
 }
 
 .fc-toolbar .fc-state-down {
     z-index: 3
 }
 
 .fc-toolbar .fc-state-active,
 .fc-toolbar .ui-state-active {
     z-index: 4
 }
 
 .fc-toolbar button:focus {
     z-index: 5
 }
 
 .fc-view-container *,
 .fc-view-container:after,
 .fc-view-container:before {
     box-sizing: content-box
 }
 
 .fc-view,
 .fc-view>table {
     position: relative;
     z-index: 1
 }
 
 .fc-basicDay-view .fc-content-skeleton,
 .fc-basicWeek-view .fc-content-skeleton {
     padding-bottom: 1em
 }
 
 .fc-basic-view .fc-body .fc-row {
     min-height: 4em
 }
 
 .fc-row.fc-rigid {
     overflow: hidden
 }
 
 .fc-row.fc-rigid .fc-content-skeleton {
     position: absolute;
     top: 0;
     left: 0;
     right: 0
 }
 
 .fc-day-top.fc-other-month {
     opacity: .3
 }
 
 .fc-basic-view .fc-day-number,
 .fc-basic-view .fc-week-number {
     padding: 2px
 }
 
 .fc-basic-view th.fc-day-number,
 .fc-basic-view th.fc-week-number {
     padding: 0 2px
 }
 
 .fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
     float: right
 }
 
 .fc-rtl .fc-basic-view .fc-day-top .fc-day-number {
     float: left
 }
 
 .fc-ltr .fc-basic-view .fc-day-top .fc-week-number {
     float: left;
     border-radius: 0 0 3px 0
 }
 
 .fc-rtl .fc-basic-view .fc-day-top .fc-week-number {
     float: right;
     border-radius: 0 0 0 3px
 }
 
 .fc-basic-view .fc-day-top .fc-week-number {
     min-width: 1.5em;
     text-align: center;
     background-color: #f2f2f2;
     color: gray
 }
 
 .fc-basic-view td.fc-week-number {
     text-align: center
 }
 
 .fc-basic-view td.fc-week-number>* {
     display: inline-block;
     min-width: 1.25em
 }
 
 .fc-agenda-view .fc-day-grid {
     position: relative;
     z-index: 2
 }
 
 .fc-agenda-view .fc-day-grid .fc-row {
     min-height: 3em
 }
 
 .fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
     padding-bottom: 1em
 }
 
 .fc .fc-axis {
     vertical-align: middle;
     padding: 0 4px;
     white-space: nowrap
 }
 
 .fc-ltr .fc-axis {
     text-align: right
 }
 
 .fc-rtl .fc-axis {
     text-align: left
 }
 
 .fc-time-grid,
 .fc-time-grid-container {
     position: relative;
     z-index: 1
 }
 
 .fc-time-grid {
     min-height: 100%
 }
 
 .fc-time-grid table {
     border: 0 hidden transparent
 }
 
 .fc-time-grid>.fc-bg {
     z-index: 1
 }
 
 .fc-time-grid .fc-slats,
 .fc-time-grid>hr {
     position: relative;
     z-index: 2
 }
 
 .fc-time-grid .fc-content-col {
     position: relative
 }
 
 .fc-time-grid .fc-content-skeleton {
     position: absolute;
     z-index: 3;
     top: 0;
     left: 0;
     right: 0
 }
 
 .fc-time-grid .fc-business-container {
     position: relative;
     z-index: 1
 }
 
 .fc-time-grid .fc-bgevent-container {
     position: relative;
     z-index: 2
 }
 
 .fc-time-grid .fc-highlight-container {
     z-index: 3
 }
 
 .fc-time-grid .fc-event-container {
     position: relative;
     z-index: 4
 }
 
 .fc-time-grid .fc-now-indicator-line {
     z-index: 5
 }
 
 .fc-time-grid .fc-helper-container {
     position: relative;
     z-index: 6
 }
 
 .fc-time-grid .fc-slats td {
     height: 1.5em;
     border-bottom: 0
 }
 
 .fc-time-grid .fc-slats .fc-minor td {
     border-top-style: dotted
 }
 
 .fc-time-grid .fc-highlight-container {
     position: relative
 }
 
 .fc-time-grid .fc-highlight {
     position: absolute;
     left: 0;
     right: 0
 }
 
 .fc-ltr .fc-time-grid .fc-event-container {
     margin: 0 2.5% 0 2px
 }
 
 .fc-rtl .fc-time-grid .fc-event-container {
     margin: 0 2px 0 2.5%
 }
 
 .fc-time-grid .fc-bgevent,
 .fc-time-grid .fc-event {
     position: absolute;
     z-index: 1
 }
 
 .fc-time-grid .fc-bgevent {
     left: 0;
     right: 0
 }
 
 .fc-v-event.fc-not-start {
     border-top-width: 0;
     padding-top: 1px;
     border-top-left-radius: 0;
     border-top-right-radius: 0
 }
 
 .fc-v-event.fc-not-end {
     border-bottom-width: 0;
     padding-bottom: 1px;
     border-bottom-left-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .fc-time-grid-event {
     overflow: hidden
 }
 
 .fc-time-grid-event.fc-selected {
     overflow: visible
 }
 
 .fc-time-grid-event.fc-selected .fc-bg {
     display: none
 }
 
 .fc-time-grid-event .fc-content {
     overflow: hidden
 }
 
 .fc-time-grid-event .fc-time,
 .fc-time-grid-event .fc-title {
     padding: 0 1px
 }
 
 .fc-time-grid-event .fc-time {
     font-size: .85em;
     white-space: nowrap
 }
 
 .fc-time-grid-event.fc-short .fc-content {
     white-space: nowrap
 }
 
 .fc-time-grid-event.fc-short .fc-time,
 .fc-time-grid-event.fc-short .fc-title {
     display: inline-block;
     vertical-align: top
 }
 
 .fc-time-grid-event.fc-short .fc-time span {
     display: none
 }
 
 .fc-time-grid-event.fc-short .fc-time:before {
     content: attr(data-start)
 }
 
 .fc-time-grid-event.fc-short .fc-time:after {
     content: "\A0-\A0"
 }
 
 .fc-time-grid-event.fc-short .fc-title {
     font-size: .85em;
     padding: 0
 }
 
 .fc-time-grid-event.fc-allow-mouse-resize .fc-resizer {
     left: 0;
     right: 0;
     bottom: 0;
     height: 8px;
     overflow: hidden;
     line-height: 8px;
     font-size: 11px;
     font-family: monospace;
     text-align: center;
     cursor: s-resize
 }
 
 .fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after {
     content: "="
 }
 
 .fc-time-grid-event.fc-selected .fc-resizer {
     border-radius: 5px;
     border-width: 1px;
     width: 8px;
     height: 8px;
     border-style: solid;
     border-color: inherit;
     background: #fff;
     left: 50%;
     margin-left: -5px;
     bottom: -5px
 }
 
 .fc-time-grid .fc-now-indicator-line {
     border-top-width: 1px;
     left: 0;
     right: 0
 }
 
 .fc-time-grid .fc-now-indicator-arrow {
     margin-top: -5px
 }
 
 .fc-ltr .fc-time-grid .fc-now-indicator-arrow {
     left: 0;
     border-width: 5px 0 5px 6px;
     border-top-color: transparent;
     border-bottom-color: transparent
 }
 
 .fc-rtl .fc-time-grid .fc-now-indicator-arrow {
     right: 0;
     border-width: 5px 6px 5px 0;
     border-top-color: transparent;
     border-bottom-color: transparent
 }
 
 .fc-event-dot {
     display: inline-block;
     width: 10px;
     height: 10px;
     border-radius: 5px
 }
 
 .fc-rtl .fc-list-view {
     direction: rtl
 }
 
 .fc-list-view {
     border-width: 1px;
     border-style: solid
 }
 
 .fc .fc-list-table {
     table-layout: auto
 }
 
 .fc-list-table td {
     border-width: 1px 0 0;
     padding: 8px 14px
 }
 
 .fc-list-table tr:first-child td {
     border-top-width: 0
 }
 
 .fc-list-heading {
     border-bottom-width: 1px
 }
 
 .fc-list-heading td {
     font-weight: 700
 }
 
 .fc-ltr .fc-list-heading-main {
     float: left
 }
 
 .fc-ltr .fc-list-heading-alt,
 .fc-rtl .fc-list-heading-main {
     float: right
 }
 
 .fc-rtl .fc-list-heading-alt {
     float: left
 }
 
 .fc-list-item.fc-has-url {
     cursor: pointer
 }
 
 .fc-list-item-marker,
 .fc-list-item-time {
     white-space: nowrap;
     width: 1px
 }
 
 .fc-ltr .fc-list-item-marker {
     padding-right: 0
 }
 
 .fc-rtl .fc-list-item-marker {
     padding-left: 0
 }
 
 .fc-list-item-title a {
     text-decoration: none;
     color: inherit
 }
 
 .fc-list-item-title a[href]:hover {
     text-decoration: underline
 }
 
 .fc-list-empty-wrap2 {
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0
 }
 
 .fc-list-empty-wrap1 {
     width: 100%;
     height: 100%;
     display: table
 }
 
 .fc-list-empty {
     display: table-cell;
     vertical-align: middle;
     text-align: center
 }
 
 .fc-unthemed .fc-list-empty {
     background-color: #eee
 }
 
 .fc-view-container {
     background-color: #fff
 }
 
 .fc-header {
     height: 50px;
     border-top-left-radius: 4px;
     border-top-right-radius: 4px;
     line-height: 50px;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .fc-header-title {
     margin: 0 0 0 30px;
     color: #fff;
     font-size: 1rem
 }
 
 .fc-toolbar.fc-header-toolbar {
     position: relative;
     height: 50px;
     line-height: 50px;
     padding: 0 7px;
     margin: 0;
     background-color: #222c3c
 }
 
 .fc-right .fc-button-group {
     position: relative;
     top: -43px;
     right: 10px;
     height: 36px;
     border: 1px solid #1a83d2;
     border-radius: 4px
 }
 
 .fc-agendaDay-button,
 .fc-agendaWeek-button,
 .fc-button,
 .fc-state-default {
     padding: 0 20px!important;
     height: 34px!important;
     background-color: #1e8fe4;
     box-shadow: none;
     border-color: transparent;
     color: #f0f3f8;
     font-size: .875rem!important;
     text-shadow: none;
     background-image: none;
     border: 0;
     outline: none
 }
 
 .fc-agendaDay-button:first-letter,
 .fc-agendaWeek-button:first-letter,
 .fc-month-button:first-letter {
     text-transform: uppercase
 }
 
 .fc-toolbar .fc-center {
     display: block;
     width: 100px;
     margin: 0 auto
 }
 
 .fc-toolbar .fc-center h2 {
     color: #fff;
     font-size: .875rem
 }
 
 .fc-left .fc-button-group .fc-button {
     margin: 0
 }
 
 .fc-state-default.fc-next-button,
 .fc-state-default.fc-prev-button {
     border-radius: 100%!important
 }
 
 .fc-next-button,
 .fc-prev-button {
     position: absolute!important;
     top: 11px;
     padding: 0!important;
     height: 28px!important;
     width: 28px;
     background-color: #313c4d;
     box-shadow: none;
     color: #ced0da;
     font-size: 8px!important;
     text-shadow: none;
     background-image: none;
     border: 0
 }
 
 .fc-prev-button {
     left: 30px
 }
 
 .fc-next-button {
     right: 30px
 }
 
 .fc th {
     padding: 12px 0;
     border: 0;
     color: #adafb2;
     text-transform: uppercase;
     font-size: .75rem;
     font-weight: 600
 }
 
 .fc-day-number {
     padding: 15px 15px 0 0!important;
     color: #354052;
     font-size: 1rem;
     font-weight: 600
 }
 
 .fc-unthemed td {
     border-color: #e6eaee
 }
 
 .fc-unthemed td.fc-today {
     background: none
 }
 
 .fc-today .fc-day-number {
     background-color: red;
     padding: 0!important;
     margin: 10px 10px 8px 0;
     width: 28px;
     height: 28px;
     line-height: 28px;
     border-radius: 100%;
     text-align: center;
     color: #fff
 }
 
 .fc-event {
     margin: 2px 0 0;
     font-size: .875rem;
     font-weight: 600;
     background-color: rgba(27, 185, 52, .1);
     border: 0;
     border-radius: 0;
     border-left: 2px solid #e6eaee
 }
 
 .fc-event,
 .fc-event:hover {
     color: #354052
 }
 
 .fc-event .fc-content {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding: 8px 8px 8px 12px
 }
 
 .fc-title {
     -webkit-box-ordinal-group: 2;
     -ms-flex-order: 1;
     order: 1
 }
 
 .fc-time {
     -webkit-box-ordinal-group: 3;
     -ms-flex-order: 2;
     order: 2;
     text-transform: uppercase;
     color: #354052;
     font-size: .75rem;
     font-weight: 400!important
 }
 
 .fc-event--blue {
     background-color: #269af3;
     background-color: rgba(38, 154, 243, .1);
     border-color: #269af3
 }
 
 .fc-event--orange {
     background-color: #ff7800;
     background-color: rgba(255, 120, 0, .1);
     border-color: #ff7800
 }
 
 .fc-event--green {
     background-color: #1bb934;
     background-color: rgba(27, 185, 52, .1);
     border-color: #1bb934
 }
 
 .gu-mirror {
     position: fixed!important;
     margin: 0!important;
     z-index: 9999!important;
     opacity: .8;
     -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
     filter: alpha(opacity=80)
 }
 
 .gu-hide {
     display: none!important
 }
 
 .gu-unselectable {
     -webkit-user-select: none!important;
     -moz-user-select: none!important;
     -ms-user-select: none!important;
     user-select: none!important
 }
 
 .gu-transit {
     opacity: .2;
     -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
     filter: alpha(opacity=20)
 }
 
 .select2-container {
     box-sizing: border-box;
     display: inline-block;
     margin: 0;
     position: relative;
     vertical-align: middle
 }
 
 .select2-container .select2-selection--single {
     box-sizing: border-box;
     cursor: pointer;
     display: block;
     height: 28px;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     -webkit-user-select: none
 }
 
 .select2-container .select2-selection--single .select2-selection__rendered {
     display: block;
     padding-left: 8px;
     padding-right: 20px;
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap
 }
 
 .select2-container .select2-selection--single .select2-selection__clear {
     position: relative
 }
 
 .select2-container[dir=rtl] .select2-selection--single .select2-selection__rendered {
     padding-right: 8px;
     padding-left: 20px
 }
 
 .select2-container .select2-selection--multiple {
     box-sizing: border-box;
     cursor: pointer;
     display: block;
     min-height: 32px;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     -webkit-user-select: none
 }
 
 .select2-container .select2-selection--multiple .select2-selection__rendered {
     display: inline-block;
     overflow: hidden;
     padding-left: 8px;
     text-overflow: ellipsis;
     white-space: nowrap
 }
 
 .select2-container .select2-search--inline {
     float: left
 }
 
 .select2-container .select2-search--inline .select2-search__field {
     box-sizing: border-box;
     border: none;
     font-size: 100%;
     margin-top: 5px;
     padding: 0
 }
 
 .select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
     -webkit-appearance: none
 }
 
 .select2-dropdown {
     border: 1px solid #aaa;
     box-sizing: border-box;
     display: block;
     position: absolute;
     left: -100000px;
     width: 100%;
     z-index: 1051
 }
 
 .select2-results {
     display: block
 }
 
 .select2-results__options {
     list-style: none;
     margin: 0;
     padding: 0
 }
 
 .select2-results__option {
     padding: 6px;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     -webkit-user-select: none
 }
 
 .select2-results__option[aria-selected] {
     cursor: pointer
 }
 
 .select2-container--open .select2-dropdown {
     left: 0
 }
 
 .select2-container--open .select2-dropdown--above {
     border-bottom: none;
     border-bottom-left-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .select2-container--open .select2-dropdown--below {
     border-top: none;
     border-top-left-radius: 0;
     border-top-right-radius: 0
 }
 
 .select2-search--dropdown {
     display: block;
     padding: 4px
 }
 
 .select2-search--dropdown .select2-search__field {
     padding: 4px;
     width: 100%;
     box-sizing: border-box
 }
 
 .select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
     -webkit-appearance: none
 }
 
 .select2-search--dropdown.select2-search--hide {
     display: none
 }
 
 .select2-close-mask {
     border: 0;
     margin: 0;
     padding: 0;
     display: block;
     position: fixed;
     left: 0;
     top: 0;
     min-height: 100%;
     min-width: 100%;
     height: auto;
     width: auto;
     opacity: 0;
     z-index: 99;
     background-color: #fff;
     filter: alpha(opacity=0)
 }
 
 .select2-hidden-accessible {
     border: 0!important;
     clip: rect(0 0 0 0)!important;
     -webkit-clip-path: inset(50%)!important;
     clip-path: inset(50%)!important;
     height: 1px!important;
     overflow: hidden!important;
     padding: 0!important;
     position: absolute!important;
     width: 1px!important;
     white-space: nowrap!important
 }
 
 .select2-container--default .select2-selection--single {
     background-color: #fff;
     border: 1px solid #aaa
 }
 
 .select2-container--default .select2-selection--single .select2-selection__rendered {
     color: #444;
     line-height: 28px
 }
 
 .select2-container--default .select2-selection--single .select2-selection__clear {
     cursor: pointer;
     float: right;
     font-weight: 700
 }
 
 .select2-container--default .select2-selection--single .select2-selection__placeholder {
     color: #999
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow b {
     border-color: #888 transparent transparent;
     border-style: solid;
     border-width: 5px 4px 0;
     height: 0;
     left: 50%;
     margin-left: -4px;
     margin-top: -2px;
     position: absolute;
     top: 50%;
     width: 0
 }
 
 .select2-container--default[dir=rtl] .select2-selection--single .select2-selection__clear {
     float: left
 }
 
 .select2-container--default[dir=rtl] .select2-selection--single .select2-selection__arrow {
     left: 1px;
     right: auto
 }
 
 .select2-container--default.select2-container--disabled .select2-selection--single {
     background-color: #eee;
     cursor: default
 }
 
 .select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
     display: none
 }
 
 .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
     border-color: transparent transparent #888;
     border-width: 0 4px 5px
 }
 
 .select2-container--default .select2-selection--multiple {
     border: 1px solid #aaa;
     border-radius: 4px;
     cursor: text
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__rendered {
     box-sizing: border-box;
     list-style: none;
     margin: 0;
     padding: 0 5px;
     width: 100%
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
     list-style: none
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__placeholder {
     color: #999;
     margin-top: 5px;
     float: left
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__clear {
     cursor: pointer;
     float: right;
     font-weight: 700;
     margin-top: 5px;
     margin-right: 10px
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice {
     background-color: #e4e4e4;
     border: 1px solid #aaa;
     border-radius: 4px;
     cursor: default;
     float: left;
     margin-right: 5px;
     margin-top: 5px;
     padding: 0 5px
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
     color: #999;
     cursor: pointer;
     display: inline-block;
     font-weight: 700;
     margin-right: 2px
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
     color: #333
 }
 
 .select2-container--default[dir=rtl] .select2-selection--multiple .select2-search--inline,
 .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice,
 .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__placeholder {
     float: right
 }
 
 .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice {
     margin-left: 5px;
     margin-right: auto
 }
 
 .select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice__remove {
     margin-left: 2px;
     margin-right: auto
 }
 
 .select2-container--default.select2-container--focus .select2-selection--multiple {
     border: 1px solid #000;
     outline: 0
 }
 
 .select2-container--default.select2-container--disabled .select2-selection--multiple {
     background-color: #eee;
     cursor: default
 }
 
 .select2-container--default.select2-container--disabled .select2-selection__choice__remove {
     display: none
 }
 
 .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple,
 .select2-container--default.select2-container--open.select2-container--above .select2-selection--single {
     border-top-left-radius: 0;
     border-top-right-radius: 0
 }
 
 .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
 .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
     border-bottom-left-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .select2-container--default .select2-search--dropdown .select2-search__field {
     border: 1px solid #aaa
 }
 
 .select2-container--default .select2-search--inline .select2-search__field {
     background: transparent;
     border: none;
     outline: 0;
     box-shadow: none;
     -webkit-appearance: textfield
 }
 
 .select2-container--default .select2-results>.select2-results__options {
     max-height: 200px;
     overflow-y: auto
 }
 
 .select2-container--default .select2-results__option[role=group] {
     padding: 0
 }
 
 .select2-container--default .select2-results__option[aria-disabled=true] {
     color: #999
 }
 
 .select2-container--default .select2-results__option[aria-selected=true] {
     background-color: #ddd
 }
 
 .select2-container--default .select2-results__option .select2-results__option {
     padding-left: 1em
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__group {
     padding-left: 0
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
     margin-left: -1em;
     padding-left: 2em
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
     margin-left: -2em;
     padding-left: 3em
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
     margin-left: -3em;
     padding-left: 4em
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
     margin-left: -4em;
     padding-left: 5em
 }
 
 .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
     margin-left: -5em;
     padding-left: 6em
 }
 
 .select2-container--default .select2-results__option--highlighted[aria-selected] {
     background-color: #5897fb;
     color: #fff
 }
 
 .select2-container--default .select2-results__group {
     cursor: default;
     display: block;
     padding: 6px
 }
 
 .select2-container--classic .select2-selection--single {
     background-color: #f7f7f7;
     border: 1px solid #aaa;
     border-radius: 4px;
     outline: 0;
     background-image: linear-gradient(180deg, #fff 50%, #eee);
     background-repeat: repeat-x;
     filter: progid: DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFFFF", endColorstr="#FFEEEEEE", GradientType=0)
 }
 
 .select2-container--classic .select2-selection--single:focus {
     border: 1px solid #5897fb
 }
 
 .select2-container--classic .select2-selection--single .select2-selection__rendered {
     color: #444;
     line-height: 28px
 }
 
 .select2-container--classic .select2-selection--single .select2-selection__clear {
     cursor: pointer;
     float: right;
     font-weight: 700;
     margin-right: 10px
 }
 
 .select2-container--classic .select2-selection--single .select2-selection__placeholder {
     color: #999
 }
 
 .select2-container--classic .select2-selection--single .select2-selection__arrow {
     background-color: #ddd;
     border: none;
     border-left: 1px solid #aaa;
     border-top-right-radius: 4px;
     border-bottom-right-radius: 4px;
     height: 26px;
     position: absolute;
     top: 1px;
     right: 1px;
     width: 20px;
     background-image: linear-gradient(180deg, #eee 50%, #ccc);
     background-repeat: repeat-x;
     filter: progid: DXImageTransform.Microsoft.gradient(startColorstr="#FFEEEEEE", endColorstr="#FFCCCCCC", GradientType=0)
 }
 
 .select2-container--classic .select2-selection--single .select2-selection__arrow b {
     border-color: #888 transparent transparent;
     border-style: solid;
     border-width: 5px 4px 0;
     height: 0;
     left: 50%;
     margin-left: -4px;
     margin-top: -2px;
     position: absolute;
     top: 50%;
     width: 0
 }
 
 .select2-container--classic[dir=rtl] .select2-selection--single .select2-selection__clear {
     float: left
 }
 
 .select2-container--classic[dir=rtl] .select2-selection--single .select2-selection__arrow {
     border: none;
     border-right: 1px solid #aaa;
     border-radius: 0;
     border-top-left-radius: 4px;
     border-bottom-left-radius: 4px;
     left: 1px;
     right: auto
 }
 
 .select2-container--classic.select2-container--open .select2-selection--single {
     border: 1px solid #5897fb
 }
 
 .select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
     background: transparent;
     border: none
 }
 
 .select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
     border-color: transparent transparent #888;
     border-width: 0 4px 5px
 }
 
 .select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
     border-top: none;
     border-top-left-radius: 0;
     border-top-right-radius: 0;
     background-image: linear-gradient(180deg, #fff 0, #eee 50%);
     background-repeat: repeat-x;
     filter: progid: DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFFFF", endColorstr="#FFEEEEEE", GradientType=0)
 }
 
 .select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
     border-bottom: none;
     border-bottom-left-radius: 0;
     border-bottom-right-radius: 0;
     background-image: linear-gradient(180deg, #eee 50%, #fff);
     background-repeat: repeat-x;
     filter: progid: DXImageTransform.Microsoft.gradient(startColorstr="#FFEEEEEE", endColorstr="#FFFFFFFF", GradientType=0)
 }
 
 .select2-container--classic .select2-selection--multiple {
     background-color: #fff;
     border: 1px solid #aaa;
     border-radius: 4px;
     cursor: text;
     outline: 0
 }
 
 .select2-container--classic .select2-selection--multiple:focus {
     border: 1px solid #5897fb
 }
 
 .select2-container--classic .select2-selection--multiple .select2-selection__rendered {
     list-style: none;
     margin: 0;
     padding: 0 5px
 }
 
 .select2-container--classic .select2-selection--multiple .select2-selection__clear {
     display: none
 }
 
 .select2-container--classic .select2-selection--multiple .select2-selection__choice {
     background-color: #e4e4e4;
     border: 1px solid #aaa;
     border-radius: 4px;
     cursor: default;
     float: left;
     margin-right: 5px;
     margin-top: 5px;
     padding: 0 5px
 }
 
 .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
     color: #888;
     cursor: pointer;
     display: inline-block;
     font-weight: 700;
     margin-right: 2px
 }
 
 .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
     color: #555
 }
 
 .select2-container--classic[dir=rtl] .select2-selection--multiple .select2-selection__choice {
     float: right;
     margin-left: 5px;
     margin-right: auto
 }
 
 .select2-container--classic[dir=rtl] .select2-selection--multiple .select2-selection__choice__remove {
     margin-left: 2px;
     margin-right: auto
 }
 
 .select2-container--classic.select2-container--open .select2-selection--multiple {
     border: 1px solid #5897fb
 }
 
 .select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
     border-top: none;
     border-top-left-radius: 0;
     border-top-right-radius: 0
 }
 
 .select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
     border-bottom: none;
     border-bottom-left-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .select2-container--classic .select2-search--dropdown .select2-search__field {
     border: 1px solid #aaa;
     outline: 0
 }
 
 .select2-container--classic .select2-search--inline .select2-search__field {
     outline: 0;
     box-shadow: none
 }
 
 .select2-container--classic .select2-dropdown {
     background-color: #fff;
     border: 1px solid transparent
 }
 
 .select2-container--classic .select2-dropdown--above {
     border-bottom: none
 }
 
 .select2-container--classic .select2-dropdown--below {
     border-top: none
 }
 
 .select2-container--classic .select2-results>.select2-results__options {
     max-height: 200px;
     overflow-y: auto
 }
 
 .select2-container--classic .select2-results__option[role=group] {
     padding: 0
 }
 
 .select2-container--classic .select2-results__option[aria-disabled=true] {
     color: grey
 }
 
 .select2-container--classic .select2-results__option--highlighted[aria-selected] {
     background-color: #3875d7;
     color: #fff
 }
 
 .select2-container--classic .select2-results__group {
     cursor: default;
     display: block;
     padding: 6px
 }
 
 .select2-container--classic.select2-container--open .select2-dropdown {
     border-color: #5897fb
 }
 
 .select2-container--default .select2-selection--single {
     background: #fff;
     background: linear-gradient(180deg, #fff, #f2f4f7);
     height: 39px;
     border: 1px solid #dfe3e9;
     border-radius: 4px;
     color: rgba(53, 64, 82, .5);
     font-size: .875rem;
     font-weight: 500;
     outline: 0
 }
 
 .select2-container--default .select2-selection--multiple[aria-expanded=true],
 .select2-container--default .select2-selection--single[aria-expanded=true] {
     outline: none;
     border: 1px solid #2ea1f8;
     border-bottom-left-radius: 4px!important;
     border-bottom-right-radius: 4px!important
 }
 
 .select2-container--default .select2-selection--single .select2-selection__rendered {
     line-height: 39px;
     padding-right: .9375rem;
     padding-left: .9375rem;
     color: rgba(53, 64, 82, .5)
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow {
     height: 39px
 }
 
 .select2-dropdown,
 .select2-results {
     border-radius: 4px;
     border: 1px solid #e6eaee
 }
 
 .select2-dropdown {
     margin: 5px 0;
     background-color: #fff
 }
 
 .select2-container--open .select2-dropdown--below,
 .select2-dropdown {
     border-top-right-radius: 4px;
     border-top-left-radius: 4px
 }
 
 .select2-container--default .select2-results>.select2-results__options {
     border-radius: 4px
 }
 
 .select2-results__option {
     padding: .625rem .9375rem;
     font-size: .875rem;
     color: rgba(53, 64, 82, .5)
 }
 
 .select2-results__option+.select2-results__option {
     border-top: 1px solid #e6eaee
 }
 
 .select2-container--default .select2-results__option--highlighted[aria-selected],
 .select2-container--default .select2-results__option[aria-selected=true] {
     background-color: #f1f4f8;
     color: #2ea1f8
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow {
     height: 26px;
     position: absolute;
     top: 1px;
     right: 1px;
     width: 20px
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow:after {
     display: inline-block;
     position: absolute;
     top: 0;
     right: 0;
     height: 100%;
     margin-left: 1.25rem;
     padding: .5rem 8px;
     border-color: inherit;
     color: inherit;
     font-family: FontAwesome;
     content: "\f107"
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow b {
     display: none
 }
 
 .select2-search--dropdown {
     position: relative;
     padding: 5px
 }
 
 .select2-search--dropdown:after {
     display: inline-block;
     position: absolute;
     top: 0;
     right: 0;
     height: 100%;
     margin-left: 1.25rem;
     padding: 10px 18px;
     border-color: inherit;
     color: #ced0da;
     font-family: FontAwesome;
     content: "\f002"
 }
 
 .select2-container--default .select2-search--dropdown .select2-search__field {
     border: 1px solid #e6eaee;
     border-radius: 4px;
     padding: 8px 15px;
     outline: none;
     color: #354052;
     font-size: 14px
 }
 
 .select2-container--default .select2-selection--multiple {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding-bottom: 3px;
     background-color: #fff;
     border-color: #dfe3e9
 }
 
 .select2-container--default.select2-container--focus .select2-selection--multiple {
     border: 1px solid #2ea2f8
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__rendered {
     padding: 0 3px
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     position: relative;
     margin-right: 3px;
     margin-top: 3px;
     padding: 4px 30px 4px 12px;
     background-color: #f1f4f8;
     border-color: #d5dce6;
     color: #354052
 }
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
     position: absolute;
     right: 10px;
     color: #c7d2e0;
     font-size: 18px
 }
 
 .select2-container--default.select2-container--disabled .select2-selection--single {
     border-color: #dfe3e9;
     background: #eaeef1;
     background-color: #eaeef1;
     color: rgba(53, 64, 82, .7)
 }
 
 .select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered {
     color: rgba(53, 64, 82, .7)
 }
 
 @-webkit-keyframes b {
     0% {
         opacity: 0;
         -webkit-transform: translateY(40px);
         transform: translateY(40px)
     }
     30%,
     70% {
         opacity: 1;
         -webkit-transform: translateY(0);
         transform: translateY(0)
     }
     to {
         opacity: 0;
         -webkit-transform: translateY(-40px);
         transform: translateY(-40px)
     }
 }
 
 @keyframes b {
     0% {
         opacity: 0;
         -webkit-transform: translateY(40px);
         transform: translateY(40px)
     }
     30%,
     70% {
         opacity: 1;
         -webkit-transform: translateY(0);
         transform: translateY(0)
     }
     to {
         opacity: 0;
         -webkit-transform: translateY(-40px);
         transform: translateY(-40px)
     }
 }
 
 @-webkit-keyframes c {
     0% {
         opacity: 0;
         -webkit-transform: translateY(40px);
         transform: translateY(40px)
     }
     30% {
         opacity: 1;
         -webkit-transform: translateY(0);
         transform: translateY(0)
     }
 }
 
 @keyframes c {
     0% {
         opacity: 0;
         -webkit-transform: translateY(40px);
         transform: translateY(40px)
     }
     30% {
         opacity: 1;
         -webkit-transform: translateY(0);
         transform: translateY(0)
     }
 }
 
 @-webkit-keyframes d {
     0% {
         -webkit-transform: scale(1);
         transform: scale(1)
     }
     10% {
         -webkit-transform: scale(1.1);
         transform: scale(1.1)
     }
     20% {
         -webkit-transform: scale(1);
         transform: scale(1)
     }
 }
 
 @keyframes d {
     0% {
         -webkit-transform: scale(1);
         transform: scale(1)
     }
     10% {
         -webkit-transform: scale(1.1);
         transform: scale(1.1)
     }
     20% {
         -webkit-transform: scale(1);
         transform: scale(1)
     }
 }
 
 .dropzone,
 .dropzone * {
     box-sizing: border-box
 }
 
 .dropzone {
     min-height: 150px;
     border: 2px solid rgba(0, 0, 0, .3);
     background: #fff;
     padding: 20px
 }
 
 .dropzone.dz-clickable {
     cursor: pointer
 }
 
 .dropzone.dz-clickable * {
     cursor: default
 }
 
 .dropzone.dz-clickable .dz-message,
 .dropzone.dz-clickable .dz-message * {
     cursor: pointer
 }
 
 .dropzone.dz-started .dz-message {
     display: none
 }
 
 .dropzone.dz-drag-hover {
     border-style: solid
 }
 
 .dropzone.dz-drag-hover .dz-message {
     opacity: .5
 }
 
 .dropzone .dz-message {
     text-align: center;
     margin: 2em 0
 }
 
 .dropzone .dz-preview {
     position: relative;
     display: inline-block;
     vertical-align: top;
     margin: 16px;
     min-height: 100px
 }
 
 .dropzone .dz-preview:hover {
     z-index: 1000
 }
 
 .dropzone .dz-preview.dz-file-preview .dz-image {
     border-radius: 20px;
     background: #999;
     background: linear-gradient(180deg, #eee, #ddd)
 }
 
 .dropzone .dz-preview.dz-file-preview .dz-details {
     opacity: 1
 }
 
 .dropzone .dz-preview.dz-image-preview {
     background: #fff
 }
 
 .dropzone .dz-preview.dz-image-preview .dz-details {
     transition: opacity .2s linear
 }
 
 .dropzone .dz-preview .dz-remove {
     font-size: 14px;
     text-align: center;
     display: block;
     cursor: pointer;
     border: none
 }
 
 .dropzone .dz-preview .dz-remove:hover {
     text-decoration: underline
 }
 
 .dropzone .dz-preview:hover .dz-details {
     opacity: 1
 }
 
 .dropzone .dz-preview .dz-details {
     z-index: 20;
     position: absolute;
     top: 0;
     left: 0;
     opacity: 0;
     font-size: 13px;
     min-width: 100%;
     max-width: 100%;
     padding: 2em 1em;
     text-align: center;
     color: rgba(0, 0, 0, .9);
     line-height: 150%
 }
 
 .dropzone .dz-preview .dz-details .dz-size {
     margin-bottom: 1em;
     font-size: 16px
 }
 
 .dropzone .dz-preview .dz-details .dz-filename {
     white-space: nowrap
 }
 
 .dropzone .dz-preview .dz-details .dz-filename:hover span {
     border: 1px solid hsla(0, 0%, 78%, .8);
     background-color: hsla(0, 0%, 100%, .8)
 }
 
 .dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
     overflow: hidden;
     text-overflow: ellipsis
 }
 
 .dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
     border: 1px solid transparent
 }
 
 .dropzone .dz-preview .dz-details .dz-filename span,
 .dropzone .dz-preview .dz-details .dz-size span {
     background-color: hsla(0, 0%, 100%, .4);
     padding: 0 .4em;
     border-radius: 3px
 }
 
 .dropzone .dz-preview:hover .dz-image img {
     -webkit-transform: scale(1.05);
     transform: scale(1.05);
     -webkit-filter: blur(8px);
     filter: blur(8px)
 }
 
 .dropzone .dz-preview .dz-image {
     border-radius: 20px;
     overflow: hidden;
     width: 120px;
     height: 120px;
     position: relative;
     display: block;
     z-index: 10
 }
 
 .dropzone .dz-preview .dz-image img {
     display: block
 }
 
 .dropzone .dz-preview.dz-success .dz-success-mark {
     -webkit-animation: b 3s cubic-bezier(.77, 0, .175, 1);
     animation: b 3s cubic-bezier(.77, 0, .175, 1)
 }
 
 .dropzone .dz-preview.dz-error .dz-error-mark {
     opacity: 1;
     -webkit-animation: c 3s cubic-bezier(.77, 0, .175, 1);
     animation: c 3s cubic-bezier(.77, 0, .175, 1)
 }
 
 .dropzone .dz-preview .dz-error-mark,
 .dropzone .dz-preview .dz-success-mark {
     pointer-events: none;
     opacity: 0;
     z-index: 500;
     position: absolute;
     display: block;
     top: 50%;
     left: 50%;
     margin-left: -27px;
     margin-top: -27px
 }
 
 .dropzone .dz-preview .dz-error-mark svg,
 .dropzone .dz-preview .dz-success-mark svg {
     display: block;
     width: 54px;
     height: 54px
 }
 
 .dropzone .dz-preview.dz-processing .dz-progress {
     opacity: 1;
     transition: all .2s linear
 }
 
 .dropzone .dz-preview.dz-complete .dz-progress {
     opacity: 0;
     transition: opacity .4s ease-in
 }
 
 .dropzone .dz-preview:not(.dz-processing) .dz-progress {
     -webkit-animation: d 6s ease infinite;
     animation: d 6s ease infinite
 }
 
 .dropzone .dz-preview .dz-progress {
     opacity: 1;
     z-index: 1000;
     pointer-events: none;
     position: absolute;
     height: 16px;
     left: 50%;
     top: 50%;
     margin-top: -8px;
     width: 80px;
     margin-left: -40px;
     background: hsla(0, 0%, 100%, .9);
     -webkit-transform: scale(1);
     border-radius: 8px;
     overflow: hidden
 }
 
 .dropzone .dz-preview .dz-progress .dz-upload {
     background: #333;
     background: linear-gradient(180deg, #666, #444);
     position: absolute;
     top: 0;
     left: 0;
     bottom: 0;
     width: 0;
     transition: width .3s ease-in-out
 }
 
 .dropzone .dz-preview.dz-error .dz-error-message {
     display: block
 }
 
 .dropzone .dz-preview.dz-error:hover .dz-error-message {
     opacity: 1;
     pointer-events: auto
 }
 
 .dropzone .dz-preview .dz-error-message {
     pointer-events: none;
     z-index: 1000;
     position: absolute;
     display: block;
     display: none;
     opacity: 0;
     transition: opacity .3s ease;
     border-radius: 8px;
     font-size: 13px;
     top: 130px;
     left: -10px;
     width: 140px;
     background: #be2626;
     background: linear-gradient(180deg, #be2626, #a92222);
     padding: .5em 1.2em;
     color: #fff
 }
 
 .dropzone .dz-preview .dz-error-message:after {
     content: "";
     position: absolute;
     top: -6px;
     left: 64px;
     width: 0;
     height: 0;
     border-left: 6px solid transparent;
     border-right: 6px solid transparent;
     border-bottom: 6px solid #be2626
 }
 
 .dropzone {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     background-color: #fff;
     color: #7f8fa4;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     padding: 5px;
     transition: border-color .3s
 }
 
 .dropzone:hover {
     border-color: #2ea1f8
 }
 
 .dropzone .dz-message {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-orient: vertical;
     -webkit-box-direction: normal;
     -ms-flex-flow: column;
     flex-flow: column;
     position: absolute;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     top: 0;
     right: 0;
     bottom: 0;
     left: 0;
     width: calc(100% - 10px);
     height: calc(100% - 10px);
     margin: auto;
     border-radius: 4px;
     border: 2px dashed rgba(206, 208, 218, .5)
 }
 
 .dz-icon {
     display: block;
     margin-bottom: 10px;
     font-size: 30px;
     color: #e2e3e9
 }
 
 .dataTable .sorting,
 .dataTable .sorting_asc,
 .dataTable .sorting_desc {
     position: relative;
     cursor: pointer
 }
 
 .dataTable .sorting:focus,
 .dataTable .sorting_asc:focus,
 .dataTable .sorting_desc:focus {
     outline: none
 }
 
 .dataTables_filter {
     margin-bottom: .9375rem;
     color: #354052
 }
 
 .dataTables_filter input {
     display: inline-block;
     width: 100%;
     max-width: 230px;
     margin-left: .625rem;
     padding: .59375rem .9375rem;
     transition: all .3s;
     border: 1px solid #dfe3e9;
     border-radius: 4px;
     background-color: #fff;
     color: #354052;
     font-size: .875rem;
     font-weight: 400;
     resize: none
 }
 
 .dataTables_filter input:focus {
     border-color: #2ea1f8;
     outline: none
 }
 
 .dataTable thead .sorting:after,
 .dataTable thead .sorting:before,
 .dataTable thead .sorting_asc:after,
 .dataTable thead .sorting_asc:before,
 .dataTable thead .sorting_desc:after,
 .dataTable thead .sorting_desc:before {
     position: absolute;
     display: inline-block;
     content: "\f0d8";
     color: #7f8fa4;
     font-family: FontAwesome
 }
 
 .dataTable thead .sorting:before,
 .dataTable thead .sorting_asc:before,
 .dataTable thead .sorting_desc:before {
     right: 10px;
     top: 25%
 }
 
 .dataTable thead .sorting:after,
 .dataTable thead .sorting_asc:after,
 .dataTable thead .sorting_desc:after {
     right: 10px;
     bottom: 25%;
     content: "\f0d7"
 }
 
 .dataTable thead.c-table__head--slim .sorting:before,
 .dataTable thead.c-table__head--slim .sorting_asc:before,
 .dataTable thead.c-table__head--slim .sorting_desc:before {
     right: 10px;
     top: 6px
 }
 
 .dataTable thead.c-table__head--slim .sorting:after,
 .dataTable thead.c-table__head--slim .sorting_asc:after,
 .dataTable thead.c-table__head--slim .sorting_desc:after {
     right: 10px;
     bottom: 6px;
     content: "\f0d7"
 }
 
 .dataTable thead .sorting:last-child:after,
 .dataTable thead .sorting:last-child:before,
 .dataTable thead .sorting_asc:last-child:after,
 .dataTable thead .sorting_asc:last-child:before,
 .dataTable thead .sorting_desc:last-child:after,
 .dataTable thead .sorting_desc:last-child:before {
     right: 20px
 }
 
 .dataTable thead .sorting_asc:after,
 .dataTable thead .sorting_desc:before {
     opacity: .5
 }
 
 .dataTables_info {
     font-size: .875rem;
     margin-top: .9375rem;
     float: left;
     color: #7f8fa4
 }
 
 .dataTables_empty {
     padding: 1.875rem;
     color: #354052
 }
 
 .dataTables_paginate {
     margin-top: .9375rem;
     float: right
 }
 
 .dataTables_paginate,
 .dataTables_paginate span {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex
 }
 
 .dataTables_paginate .paginate_button {
     display: block;
     height: 2.1875rem;
     padding: 0 16px;
     border: 1px solid #e6eaee;
     background-color: #fff;
     color: rgba(53, 64, 82, .5);
     font-size: .875rem;
     font-weight: 600;
     line-height: 2.1875rem;
     text-align: center
 }
 
 .dataTables_paginate .paginate_button:hover {
     color: #354052;
     text-decoration: none
 }
 
 .dataTables_paginate .paginate_button.current {
     color: #354052
 }
 
 .dataTables_paginate .paginate_button+.paginate_button {
     border-left: 0
 }
 
 .dataTables_paginate .paginate_button.previous {
     border-radius: 4px;
     border-right: 0;
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .dataTables_paginate .paginate_button.next {
     border-radius: 4px;
     border-left: 0;
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .datepicker-container {
     background-color: #fff;
     direction: ltr;
     font-size: 12px;
     left: 0;
     line-height: 30px;
     position: fixed;
     top: 0;
     -ms-touch-action: none;
     touch-action: none;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     width: 210px;
     z-index: -1;
     -webkit-tap-highlight-color: transparent;
     -webkit-touch-callout: none
 }
 
 .datepicker-container:after,
 .datepicker-container:before {
     border: 5px solid transparent;
     content: " ";
     display: block;
     height: 0;
     position: absolute;
     width: 0
 }
 
 .datepicker-dropdown {
     border: 1px solid #ccc;
     box-shadow: 0 3px 6px #ccc;
     box-sizing: content-box;
     position: absolute;
     z-index: 1
 }
 
 .datepicker-inline {
     position: static
 }
 
 .datepicker-top-left,
 .datepicker-top-right {
     border-top-color: #39f
 }
 
 .datepicker-top-left:after,
 .datepicker-top-left:before,
 .datepicker-top-right:after,
 .datepicker-top-right:before {
     border-top: 0;
     left: 10px;
     top: -5px
 }
 
 .datepicker-top-left:before,
 .datepicker-top-right:before {
     border-bottom-color: #39f
 }
 
 .datepicker-top-left:after,
 .datepicker-top-right:after {
     border-bottom-color: #fff;
     top: -4px
 }
 
 .datepicker-bottom-left,
 .datepicker-bottom-right {
     border-bottom-color: #39f
 }
 
 .datepicker-bottom-left:after,
 .datepicker-bottom-left:before,
 .datepicker-bottom-right:after,
 .datepicker-bottom-right:before {
     border-bottom: 0;
     bottom: -5px;
     left: 10px
 }
 
 .datepicker-bottom-left:before,
 .datepicker-bottom-right:before {
     border-top-color: #39f
 }
 
 .datepicker-bottom-left:after,
 .datepicker-bottom-right:after {
     border-top-color: #fff;
     bottom: -4px
 }
 
 .datepicker-bottom-right:after,
 .datepicker-bottom-right:before,
 .datepicker-top-right:after,
 .datepicker-top-right:before {
     left: auto;
     right: 10px
 }
 
 .datepicker-panel>ul {
     margin: 0;
     padding: 0;
     width: 102%
 }
 
 .datepicker-panel>ul:after,
 .datepicker-panel>ul:before {
     content: " ";
     display: table
 }
 
 .datepicker-panel>ul:after {
     clear: both
 }
 
 .datepicker-panel>ul>li {
     background-color: #fff;
     cursor: pointer;
     float: left;
     height: 30px;
     list-style: none;
     margin: 0;
     padding: 0;
     text-align: center;
     width: 30px
 }
 
 .datepicker-panel>ul>li:hover {
     background-color: #e5f2ff
 }
 
 .datepicker-panel>ul>li.muted,
 .datepicker-panel>ul>li.muted:hover {
     color: #999
 }
 
 .datepicker-panel>ul>li.highlighted {
     background-color: #e5f2ff
 }
 
 .datepicker-panel>ul>li.highlighted:hover {
     background-color: #cce5ff
 }
 
 .datepicker-panel>ul>li.picked,
 .datepicker-panel>ul>li.picked:hover {
     color: #39f
 }
 
 .datepicker-panel>ul>li.disabled,
 .datepicker-panel>ul>li.disabled:hover {
     background-color: #fff;
     color: #ccc;
     cursor: default
 }
 
 .datepicker-panel>ul>li.disabled.highlighted,
 .datepicker-panel>ul>li.disabled:hover.highlighted {
     background-color: #e5f2ff
 }
 
 .datepicker-panel>ul>li[data-view="month next"],
 .datepicker-panel>ul>li[data-view="month prev"],
 .datepicker-panel>ul>li[data-view="year next"],
 .datepicker-panel>ul>li[data-view="year prev"],
 .datepicker-panel>ul>li[data-view="years next"],
 .datepicker-panel>ul>li[data-view="years prev"],
 .datepicker-panel>ul>li[data-view=next] {
     font-size: 18px
 }
 
 .datepicker-panel>ul>li[data-view="month current"],
 .datepicker-panel>ul>li[data-view="year current"],
 .datepicker-panel>ul>li[data-view="years current"] {
     width: 150px
 }
 
 .datepicker-panel>ul[data-view=months]>li,
 .datepicker-panel>ul[data-view=years]>li {
     height: 52.5px;
     line-height: 52.5px;
     width: 52.5px
 }
 
 .datepicker-panel>ul[data-view=week]>li,
 .datepicker-panel>ul[data-view=week]>li:hover {
     background-color: #fff;
     cursor: default
 }
 
 .datepicker-hide {
     display: none
 }
 
 .datepicker-container {
     width: 224px;
     padding: .625rem
 }
 
 .datepicker-panel>ul>li[data-view="month current"],
 .datepicker-panel>ul>li[data-view="year current"],
 .datepicker-panel>ul>li[data-view="years current"] {
     width: 160px;
     border-radius: 0
 }
 
 .datepicker-panel>ul[data-view=week]>li,
 .datepicker-panel>ul[data-view=week]>li:hover {
     color: rgba(53, 64, 82, .5)
 }
 
 .datepicker-panel>ul>li.picked,
 .datepicker-panel>ul>li.picked:hover {
     color: #1a91eb
 }
 
 .datepicker-dropdown {
     background: #fff;
     border: 1px solid #dfe3e9;
     box-shadow: 0 1px 4px 0 rgba(0, 0, 0, .08);
     border-radius: 4px;
     overflow: hidden
 }
 
 .datepicker-panel>ul>li {
     border-radius: 100%;
     font-size: .875rem;
     width: 32px;
     height: 32px
 }
 
 .datepicker-panel>ul>li:hover {
     background-color: #f5f7f8
 }
 
 .datepicker-panel>ul>li.highlighted {
     background-color: #1a91eb;
     color: #fff;
     border-radius: 100%
 }
 
 .datepicker-panel>ul>li[data-view=month] {
     font-size: .875rem
 }
 
 .jqvmap-label {
     position: absolute;
     display: none;
     border-radius: 3px;
     background: #292929;
     color: #fff;
     font-family: sans-serif;
     font-size: smaller;
     padding: 3px
 }
 
 .jqvmap-label,
 .jqvmap-pin {
     pointer-events: none
 }
 
 .jqvmap-zoomin,
 .jqvmap-zoomout {
     position: absolute;
     left: 10px;
     border-radius: 3px;
     background: #000;
     padding: 3px;
     color: #fff;
     width: 10px;
     height: 10px;
     cursor: pointer;
     line-height: 10px;
     text-align: center
 }
 
 .jqvmap-zoomin {
     top: 10px
 }
 
 .jqvmap-zoomout {
     top: 30px
 }
 
 .jqvmap-region {
     cursor: pointer
 }
 
 .jqvmap-ajax_response {
     width: 100%;
     height: 500px
 }
 
 .jqvmap-pin {
     -webkit-transform: translate(-3200%, -1050%);
     transform: translate(-3200%, -1050%)
 }
 
 @media (max-width:992px) {
     .jqvmap-pin {
         -webkit-transform: translate(-300%, -1050%);
         transform: translate(-300%, -1050%)
     }
 }
 
 @media (max-width:768px) {
     .jqvmap-pin {
         -webkit-transform: translate(-500%, -1500%);
         transform: translate(-500%, -1500%)
     }
 }
 
 .o-page {
     position: relative;
     height: 100%
 }
 
 .o-page--center {
     padding-top: 6.25rem
 }
 
 .o-page__sidebar {
     position: fixed;
     top: 0;
     bottom: 0;
     width: 250px;
     transition: -webkit-transform .2s;
     transition: transform .2s;
     transition: transform .2s, -webkit-transform .2s;
     z-index: 300
 }
 
 @media (max-width:992px) {
     .o-page__sidebar {
         width: 300px;
         -webkit-transform: translateX(-300px);
         transform: translateX(-300px)
     }
 }
 
 .o-page__sidebar.is-visible {
     -webkit-transform: translateX(0);
     transform: translateX(0)
 }
 
 .o-page__content {
     margin-left: 250px;
     overflow: hidden
 }
 
 @media (max-width:992px) {
     .o-page__content {
         margin-left: 0
     }
 }
 
 .o-page__content.is-pushed {
     margin-left: 250px
 }
 
 .o-page__card {
     width: 450px;
     margin: 0 auto 3.125rem
 }
 
 @media (max-width:768px) {
     .o-page__card {
         width: 95%
     }
 }
 
 .o-page__card--horizontal {
     width: 800px;
     margin: 0 auto 3.125rem
 }
 
 @media (max-width:992px) {
     .o-page__card--horizontal {
         width: 95%
     }
 }
 
 .o-media {
     display: block
 }
 
 .o-media:after {
     display: table!important;
     clear: both!important;
     content: ""!important
 }
 
 .o-media__img {
     float: left
 }
 
 .o-media__img>img {
     display: block
 }
 
 .o-media__body {
     display: block;
     overflow: hidden
 }
 
 .o-media__body,
 .o-media__body>:last-child {
     margin-bottom: 0
 }
 
 .o-line {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between
 }
 
 .o-ratio {
     display: block;
     position: relative
 }
 
 .o-ratio:before {
     display: block;
     width: 100%;
     padding-bottom: 100%;
     content: ""
 }
 
 .o-ratio>embed,
 .o-ratio>iframe,
 .o-ratio>object,
 .o-ratio__content {
     position: absolute;
     top: 0;
     bottom: 0;
     left: 0;
     width: 100%;
     height: 100%
 }
 
 .o-ratio--16\:9:before {
     padding-bottom: 56.25%
 }
 
 .o-ratio--4\:3:before {
     padding-bottom: 75%
 }
 
 .o-ratio--2\:1:before {
     padding-bottom: 50%
 }
 
 .c-alert {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     width: 100%;
     margin: 0 0 .9375rem;
     padding: .75rem 2rem .75rem .75rem;
     transition: all .3s;
     border-radius: 4px;
     color: #fff
 }
 
 .c-alert .c-close {
     position: absolute;
     top: .75rem;
     right: 1rem
 }
 
 .c-alert.fade {
     transition: opacity .3s;
     opacity: 0
 }
 
 .c-alert.fade.show {
     opacity: 1
 }
 
 .c-alert__icon {
     position: relative;
     bottom: -4px;
     margin-right: .625rem;
     font-size: 1.125rem;
     opacity: .7
 }
 
 .c-alert--success {
     background-color: #1bb934
 }
 
 .c-alert--info {
     background-color: #2ea1f8
 }
 
 .c-alert--warning {
     background-color: #fd9a18
 }
 
 .c-alert--danger {
     background-color: #ed1c24
 }
 
 .c-avatar {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     margin: 0;
     padding: 0;
     overflow: hidden;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none
 }
 
 .c-avatar.has-dropdown {
     cursor: pointer;
     overflow: visible
 }
 
 .c-avatar.has-dropdown:after {
     display: block;
     padding-left: 10px;
     color: #7f8fa4;
     font-family: FontAwesome;
     content: "\f107"
 }
 
 .c-avatar.has-dropdown:hover {
     text-decoration: none
 }
 
 .c-avatar .c-avatar__img {
     width: 70px;
     height: 70px
 }
 
 .c-avatar__img {
     border-radius: 100%;
     overflow: hidden
 }
 
 .c-avatar--xsmall .c-avatar__img {
     width: 36px;
     height: 36px
 }
 
 .c-avatar--small .c-avatar__img {
     width: 50px;
     height: 50px
 }
 
 .c-avatar--medium .c-avatar__img {
     width: 60px;
     height: 60px
 }
 
 .c-avatar--large .c-avatar__img {
     width: 90px;
     height: 90px
 }
 
 .c-avatar--xlarge .c-avatar__img {
     width: 110px;
     height: 110px
 }
 
 .c-avatar--super .c-avatar__img {
     width: 130px;
     height: 130px
 }
 
 .c-badge {
     display: inline-block;
     margin: 0;
     padding: .25rem .75rem;
     border-radius: 4px;
     color: #fff;
     font-size: .75rem;
     font-weight: 600;
     text-transform: uppercase
 }
 
 .c-badge>i {
     margin-right: .3125rem
 }
 
 .c-badge--small {
     padding: .25rem .625rem;
     font-size: 10px
 }
 
 .c-badge--xsmall {
     padding: .125rem .3125rem;
     font-size: 10px
 }
 
 .c-badge--success {
     background-color: #1bb934
 }
 
 .c-badge--info {
     background-color: #2ea1f8
 }
 
 .c-badge--warning {
     background-color: #fd9a18
 }
 
 .c-badge--danger {
     background-color: #ed1c24
 }
 
 .c-badge--primary {
     background-color: #475364
 }
 
 .c-badge--secondary {
     background-color: #b7c0cd;
     color: #fff
 }
 
 .c-breadcrumb {
     display: block;
     margin: 0;
     padding: 0
 }
 
 .c-breadcrumb__item {
     display: inline-block;
     color: #848c98
 }
 
 .c-breadcrumb__item>a {
     color: inherit
 }
 
 .c-breadcrumb__item:not(:last-child):after {
     margin: 0 .625rem;
     color: #a1a7af;
     content: "/"
 }
 
 .c-breadcrumb__item.is-active {
     color: #354052
 }
 
 .c-btn-group {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex
 }
 
 .c-btn-group>.c-btn:first-child {
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .c-btn-group>.c-btn:last-child {
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-btn-group>.c-btn:not(:first-child):not(:last-child) {
     border-radius: 0
 }
 
 .c-btn-group>.c-btn+.c-btn {
     border-left: 0
 }
 
 .c-btn-group--fullwidth {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     width: 100%
 }
 
 .c-btn-group--fullwidth>.c-btn {
     width: 100%
 }
 
 .c-btn {
     display: inline-block;
     position: relative;
     max-width: 100%;
     margin: 0;
     padding: .5rem 1.25rem;
     transition: all .15s ease-in-out;
     border: 1px solid;
     border-radius: 4px;
     border-color: #1a91eb;
     color: #fff;
     font-size: .875rem;
     font-weight: 400;
     line-height: 1.5;
     text-align: center;
     text-overflow: ellipsis;
     white-space: nowrap;
     cursor: pointer;
     overflow: hidden;
     vertical-align: middle;
     -webkit-appearance: none;
     -moz-appearance: none;
     appearance: none;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-btn:focus,
 .c-btn:hover {
     outline: 0;
     color: #fff;
     text-decoration: none
 }
 
 .c-btn i {
     position: relative;
     bottom: -1px;
     font-size: 16px
 }
 
 .c-btn.has-dropdown {
     position: relative;
     padding-right: 2.5rem
 }
 
 .c-btn.has-dropdown:after {
     display: inline-block;
     position: absolute;
     top: 0;
     right: 0;
     height: 100%;
     margin-left: 1.25rem;
     padding: .5rem 8px;
     border-left: 1px solid;
     border-color: inherit;
     color: inherit;
     font-family: FontAwesome;
     content: "\f107"
 }
 
 .c-btn.is-disabled,
 .c-btn:disabled {
     border-color: #e6eaee;
     background: #f5f8fa;
     color: rgba(53, 64, 82, .5)
 }
 
 .c-btn.is-disabled:focus,
 .c-btn.is-disabled:hover,
 .c-btn:disabled:focus,
 .c-btn:disabled:hover {
     border-color: #e6eaee;
     background: #f5f8fa;
     color: rgba(53, 64, 82, .5);
     cursor: not-allowed
 }
 
 .c-btn--fullwidth {
     width: 100%
 }
 
 .c-btn--small {
     padding: .25rem 1.125rem;
     font-size: .75rem
 }
 
 .c-btn--small i {
     margin-right: 5px
 }
 
 .c-btn--large {
     padding: .5rem 1.875rem;
     font-size: 1rem
 }
 
 .c-btn--primary {
     background: #475364;
     background: linear-gradient(180deg, #475364, #273142);
     border-color: #354052
 }
 
 .c-btn--primary:hover {
     background: #3f4958;
     background: linear-gradient(180deg, #3f4958, #151b24)
 }
 
 .c-btn--primary:focus {
     background: #3a4452;
     background: linear-gradient(180deg, #3a4452, #12161e)
 }
 
 .c-btn--secondary {
     background: #fff;
     background: linear-gradient(180deg, #fff, #f2f4f7);
     border-color: #dfe3e9;
     color: #354052
 }
 
 .c-btn--secondary:hover {
     background: #fcfcfc;
     background: linear-gradient(180deg, #fcfcfc, #eceff3);
     color: #354052
 }
 
 .c-btn--secondary:focus {
     background: #fafafa;
     background: linear-gradient(180deg, #fafafa, #e9ecf1);
     color: #354052
 }
 
 .c-btn--info {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea);
     border-color: #1a91eb
 }
 
 .c-btn--info:hover {
     background: #1a98f7;
     background: linear-gradient(180deg, #1a98f7, #1485db)
 }
 
 .c-btn--info:focus {
     background: #1094f7;
     background: linear-gradient(180deg, #1094f7, #137fd1)
 }
 
 .c-btn--success {
     background: #39b54a;
     background: linear-gradient(180deg, #39b54a, #34aa44);
     border-color: #249533
 }
 
 .c-btn--success:hover {
     background: #34a544;
     background: linear-gradient(180deg, #34a544, #2f9a3e)
 }
 
 .c-btn--success:focus {
     background: #329e40;
     background: linear-gradient(180deg, #329e40, #2d933b)
 }
 
 .c-btn--fancy {
     background: #886ce6;
     background: linear-gradient(180deg, #886ce6, #7d5be2);
     border-color: #7d5be2
 }
 
 .c-btn--fancy:hover {
     background: #7a5be3;
     background: linear-gradient(180deg, #7a5be3, #6f4adf)
 }
 
 .c-btn--fancy:focus {
     background: #7352e2;
     background: linear-gradient(180deg, #7352e2, #6841dd)
 }
 
 .c-btn--warning {
     background: #fd9a18;
     background: linear-gradient(180deg, #fd9a18, #f16911);
     border-color: #f16911
 }
 
 .c-btn--warning:hover {
     background: #fd9104;
     background: linear-gradient(180deg, #fd9104, #e0600d)
 }
 
 .c-btn--warning:focus {
     background: #f48c02;
     background: linear-gradient(180deg, #f48c02, #d75c0d)
 }
 
 .c-btn--danger {
     background: #f95359;
     background: linear-gradient(180deg, #f95359, #d35847);
     border-color: #d35847
 }
 
 .c-btn--danger:hover {
     background: #f83f46;
     background: linear-gradient(180deg, #f83f46, #cf4937)
 }
 
 .c-btn--danger:focus {
     background: #f8353c;
     background: linear-gradient(180deg, #f8353c, #cb4331)
 }
 
 .c-card {
     position: relative;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-card--responsive {
     overflow: auto
 }
 
 .c-card__header {
     padding: 1.5625rem 1.875rem;
     border-bottom: 1px solid #e6eaee;
     background-color: #fafbfc
 }
 
 .c-card__header--transparent {
     background-color: transparent
 }
 
 .c-card__title {
     margin: 0;
     font-size: 1.125rem
 }
 
 .c-card__meta {
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-card__icon {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: absolute;
     top: -35px;
     right: 0;
     left: 0;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 70px;
     height: 70px;
     margin: 0 auto;
     border-radius: 100%;
     color: #fff;
     font-size: 1.75rem;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-card__icon:hover i {
     color: inherit;
     text-decoration: none
 }
 
 .c-card__body {
     padding: 1.875rem
 }
 
 .c-choice {
     margin: 0 0 .9375rem;
     color: #354052;
     font-size: 0;
     font-weight: 400
 }
 
 .c-choice.is-disabled .c-choice__label {
     color: rgba(53, 64, 82, .5);
     cursor: default
 }
 
 .c-choice.is-disabled .c-choice__label:before {
     border-color: #dfe3e9;
     background-color: #e6eaee
 }
 
 .c-choice__input {
     position: absolute;
     width: 1px;
     height: 1px;
     margin: -1px;
     padding: 0;
     border: 0;
     white-space: nowrap;
     overflow: hidden;
     clip: rect(0 0 0 0);
     -webkit-clip-path: inset(50%);
     clip-path: inset(50%)
 }
 
 .c-choice__input+.c-choice__label {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     color: #354052;
     font-size: .875rem;
     cursor: pointer
 }
 
 .c-choice__input+.c-choice__label:before {
     display: inline-block;
     position: relative;
     top: -1px;
     width: 16px;
     height: 16px;
     margin: 0 15px 0 0;
     transition: all .3s;
     border: 1px solid #bbc5d5;
     background-color: #fff;
     color: #fff;
     line-height: 15px;
     text-align: center;
     content: "";
     visibility: visible
 }
 
 .c-choice--checkbox .c-choice__input+.c-choice__label:before {
     border-radius: 4px
 }
 
 .c-choice--checkbox .c-choice__input:checked+.c-choice__label:before {
     border-color: #34aa44;
     background-color: #39b54a;
     font-family: FontAwesome;
     font-size: 10px;
     content: "\f00c"
 }
 
 .c-choice--radio .c-choice__input+.c-choice__label:before {
     border-radius: 100%
 }
 
 .c-choice--radio .c-choice__input:checked+.c-choice__label:before {
     border: 4px solid #39b54a;
     background-color: #fff;
     box-shadow: 0 0 0 1px #34aa44
 }
 
 .c-close {
     padding: 0;
     border: 0;
     background-color: transparent;
     color: #fff;
     font-size: 1.625rem;
     line-height: 1;
     opacity: .7;
     -webkit-appearance: none;
     -moz-appearance: none;
     appearance: none
 }
 
 .c-close:focus,
 .c-close:hover {
     opacity: 1
 }
 
 .c-close:focus {
     outline: 0
 }
 
 .c-dropdown {
     display: inline-block;
     position: relative
 }
 
 .c-dropdown__menu {
     position: absolute;
     top: 100%;
     right: 0;
     width: 9.375rem;
     margin: 5px 0 0;
     transition: visibility .3s ease, opacity .3s ease;
     border: 1px solid #dfe3e9;
     border-radius: 4px;
     background-color: #fff;
     text-align: left;
     box-shadow: 0 1px 4px rgba(0, 0, 0, .08);
     z-index: 400
 }
 
 .c-dropdown .c-dropdown__menu,
 .dropdown .c-dropdown__menu {
     opacity: 0;
     visibility: hidden
 }
 
 .c-dropdown.show .c-dropdown__menu,
 .dropdown.show .c-dropdown__menu {
     opacity: 1;
     visibility: visible
 }
 
 .c-dropdown__menu--large {
     width: 18.75rem
 }
 
 .c-dropdown__menu--large .c-dropdown__item {
     padding: .625rem .9375rem
 }
 
 .c-dropdown__item {
     display: block;
     padding: .5rem .9375rem;
     border-bottom: 1px solid #dfe3e9;
     color: #354052;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-dropdown__item:hover {
     background-color: #f2f4f7;
     color: #2ea1f8;
     text-decoration: none
 }
 
 .c-dropdown__item:last-child {
     border-bottom: 0
 }
 
 .c-field {
     position: relative;
     width: 100%
 }
 
 .c-field.has-addon-left,
 .c-field.has-addon-right {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-orient: horizontal;
     -webkit-box-direction: normal;
     -ms-flex-flow: row nowrap;
     flex-flow: row nowrap
 }
 
 .c-field.has-addon-left>.c-input,
 .c-field.has-addon-right>.c-input {
     -ms-flex-preferred-size: calc(1 - $field-addon-width);
     flex-basis: calc(1 - $field-addon-width)
 }
 
 .c-field.has-addon-left>.c-field__addon {
     border-right: 0;
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .c-field.has-addon-left>.c-input {
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-field.has-addon-right>.c-field__addon {
     border-left: 0;
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-field.has-addon-right>.c-input {
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .c-field.has-icon-left>.c-field__icon,
 .c-field.has-icon-right>.c-field__icon {
     position: absolute;
     top: 50%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     color: #ced0da;
     font-size: 1.125rem
 }
 
 .c-field.has-icon-left>.c-input {
     padding-left: 40px
 }
 
 .c-field.has-icon-left>.c-field__icon {
     left: 15px
 }
 
 .c-field.has-icon-right>.c-input {
     padding-right: 40px
 }
 
 .c-field.has-icon-right>.c-field__icon {
     right: 15px
 }
 
 .c-field--inline {
     width: 270px
 }
 
 @media (max-width:768px) {
     .c-field--inline {
         width: 100%
     }
 }
 
 .c-field__label {
     display: block;
     -ms-flex-preferred-size: 100%;
     flex-basis: 100%;
     margin: 0 0 .3125rem;
     color: #7f8fa4;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-field__addon {
     background: #fff;
     background: linear-gradient(180deg, #fff, #f2f4f7);
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -ms-flex-preferred-size: 2.625rem;
     flex-basis: 2.625rem;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     border: 1px solid #dfe3e9;
     border-radius: 4px;
     color: #ced0da;
     font-size: 1.125rem;
     text-align: center
 }
 
 .c-field__message {
     display: inline-block;
     margin: 0 .3125rem 0 0;
     padding: 0;
     color: rgba(53, 64, 82, .5);
     font-size: .75rem
 }
 
 .c-field__message>i {
     margin-right: .3125rem
 }
 
 .c-icon {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 50px;
     height: 50px;
     border-radius: 4px;
     background: #2ea1f8;
     color: #fff;
     font-size: 1.25rem
 }
 
 .c-icon:hover {
     color: #fff;
     text-decoration: none
 }
 
 .c-icon--circle {
     border-radius: 100%
 }
 
 .c-icon--large {
     width: 60px;
     height: 60px;
     font-size: 1.5rem
 }
 
 .c-icon--small {
     width: 40px;
     height: 40px;
     font-size: 1rem
 }
 
 .c-icon--success {
     background: #39b54a;
     background: linear-gradient(180deg, #39b54a, #34aa44)
 }
 
 .c-icon--info {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-icon--warning {
     background: #fd9a18;
     background: linear-gradient(180deg, #fd9a18, #f16911)
 }
 
 .c-icon--danger {
     background: #f95359;
     background: linear-gradient(180deg, #f95359, #d35847)
 }
 
 .c-icon--fancy {
     background: #886ce6;
     background: linear-gradient(180deg, #886ce6, #7d5be2)
 }
 
 .c-input {
     display: block;
     width: 100%;
     margin: 0;
     padding: .59375rem .9375rem;
     transition: all .3s;
     border: 1px solid #dfe3e9;
     border-radius: 4px;
     background-color: #fff;
     color: #354052;
     font-size: .875rem;
     font-weight: 400;
     resize: none
 }
 
 .c-input::-webkit-input-placeholder {
     transition: opacity .3s;
     color: rgba(53, 64, 82, .5);
     font-weight: 400;
     opacity: .5
 }
 
 .c-input:-ms-input-placeholder,
 .c-input::-ms-input-placeholder {
     transition: opacity .3s;
     color: rgba(53, 64, 82, .5);
     font-weight: 400;
     opacity: .5
 }
 
 .c-input::placeholder {
     transition: opacity .3s;
     color: rgba(53, 64, 82, .5);
     font-weight: 400;
     opacity: .5
 }
 
 .c-input:focus {
     border-color: #2ea1f8;
     outline: none
 }
 
 .c-input:focus::-webkit-input-placeholder {
     opacity: .2
 }
 
 .c-input:focus:-ms-input-placeholder,
 .c-input:focus::-ms-input-placeholder {
     opacity: .2
 }
 
 .c-input:focus::placeholder {
     opacity: .2
 }
 
 .c-input.is-disabled,
 .c-input:disabled {
     border-color: #dfe3e9;
     background-color: #e6eaee;
     color: rgba(53, 64, 82, .5);
     cursor: not-allowed
 }
 
 textarea.c-input {
     min-height: 90px;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-input--success,
 .c-input--success:focus {
     border-color: #1bb934
 }
 
 .c-input--warning,
 .c-input--warning:focus {
     border-color: #fd9a18
 }
 
 .c-input--danger,
 .c-input--danger:focus {
     border-color: #ed1c24;
     color: #ed1c24
 }
 
 .modal-open {
     overflow: hidden
 }
 
 .c-modal {
     display: none;
     position: fixed;
     top: 0;
     right: 0;
     bottom: 0;
     left: 0;
     padding: 0 .9375rem;
     outline: 0;
     z-index: 500
 }
 
 .modal-open .c-modal {
     overflow-x: hidden;
     overflow-y: auto
 }
 
 .c-modal__dialog {
     position: relative;
     max-width: 550px;
     margin: 1.875rem auto
 }
 
 .c-modal.fade .c-modal__dialog {
     -webkit-animation-duration: .3s;
     animation-duration: .3s;
     -webkit-animation-fill-mode: both;
     animation-fill-mode: both
 }
 
 .c-modal.show .c-modal__dialog {
     -webkit-animation-name: e;
     animation-name: e
 }
 
 .c-modal .c-modal__dialog {
     max-width: 550px
 }
 
 .c-modal--xsmall .c-modal__dialog {
     max-width: 350px
 }
 
 .c-modal--small .c-modal__dialog {
     max-width: 450px
 }
 
 .c-modal--medium .c-modal__dialog {
     max-width: 550px
 }
 
 .c-modal--large .c-modal__dialog {
     max-width: 650px
 }
 
 .c-modal--xlarge .c-modal__dialog {
     max-width: 750px
 }
 
 .c-modal--huge .c-modal__dialog {
     max-width: 950px
 }
 
 .c-modal__content {
     position: relative;
     width: 100%;
     border-radius: 4px;
     outline: 0;
     overflow: hidden
 }
 
 .c-modal__header {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea);
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: 20px 1.875rem
 }
 
 .c-modal__title {
     margin: 0;
     color: #fff;
     font-size: 1.125rem
 }
 
 .c-modal__close {
     color: #fff;
     font-size: 1.125rem;
     cursor: pointer;
     opacity: .8
 }
 
 .c-modal__close:hover {
     color: #fff
 }
 
 .c-modal__close--absolute {
     position: absolute;
     top: 1.875rem;
     right: 1.875rem;
     z-index: 500
 }
 
 @media (max-width:768px) {
     .c-modal__close--absolute {
         top: .9375rem
     }
 }
 
 .c-modal__subheader {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding: 15px 30px;
     background-color: #222c3c;
     color: #fff
 }
 
 .c-modal__subheader p {
     color: inherit
 }
 
 .c-modal__subheader-tab {
     color: #7f8fa4
 }
 
 .c-modal__subheader-tab:hover {
     color: #fff;
     text-decoration: none
 }
 
 .c-modal__subheader-tab.is-active {
     padding: .9375rem 0;
     border-bottom: 3px solid #2ea1f8;
     color: #fff
 }
 
 .c-modal__body {
     padding: 1.875rem;
     background-color: #fff;
     overflow: hidden
 }
 
 .c-modal__body--maximized {
     max-height: 450px;
     overflow: auto
 }
 
 .c-modal__footer {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: .9375rem 1.875rem;
     border-top: 1px solid #e6eaee;
     background-color: #eff3f6
 }
 
 .modal-backdrop {
     position: fixed;
     top: 0;
     right: 0;
     bottom: 0;
     left: 0;
     background-color: rgba(29, 37, 49, .9);
     z-index: 400
 }
 
 .modal-backdrop.fade {
     opacity: 0
 }
 
 .modal-backdrop.show {
     opacity: .9
 }
 
 .modal-scrollbar-measure {
     position: absolute;
     top: -9999px;
     width: 50px;
     height: 50px;
     overflow: scroll
 }
 
 @-webkit-keyframes e {
     0% {
         -webkit-transform: translateY(30px);
         transform: translateY(30px);
         opacity: 0
     }
     50% {
         -webkit-transform: translateY(0);
         transform: translateY(0);
         opacity: 1
     }
 }
 
 @keyframes e {
     0% {
         -webkit-transform: translateY(30px);
         transform: translateY(30px);
         opacity: 0
     }
     to {
         -webkit-transform: translateY(0);
         transform: translateY(0);
         opacity: 1
     }
 }
 
 .c-nav {
     display: block
 }
 
 @media (max-width:768px) {
     .c-nav {
         position: relative;
         -webkit-box-flex: 1;
         -ms-flex: 1 0 100%;
         flex: 1 0 100%;
         -webkit-box-ordinal-group: 3;
         -ms-flex-order: 2;
         order: 2;
         margin-top: .9375rem;
         margin-right: -.9375rem!important;
         margin-left: -.9375rem;
         border-top: 1px solid #e6eaee
     }
     .c-nav.collapse {
         display: none
     }
     .c-nav.show {
         display: block
     }
     .c-nav.collapsing {
         position: relative;
         height: 0;
         transition: height .6s;
         overflow: hidden
     }
 }
 
 .c-nav__list {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-nav__item {
     position: relative;
     margin: 0 40px 0 0
 }
 
 @media (max-width:768px) {
     .c-nav__item {
         -ms-flex-preferred-size: 100%;
         flex-basis: 100%;
         margin-right: 0;
         padding: 15px;
         border-bottom: 1px solid #e6eaee
     }
 }
 
 .c-nav__item:last-child {
     margin-right: 0
 }
 
 @media (max-width:768px) {
     .c-nav__item:last-child {
         padding-bottom: 5px;
         border-bottom: 0
     }
 }
 
 .c-nav__link {
     color: #9fa9ba;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-nav__link:hover {
     text-decoration: none
 }
 
 .c-nav__link.is-active {
     color: #2ea1f8
 }
 
 @media (max-width:768px) {
     .c-nav__link {
         display: block;
         color: #354052;
         font-size: 1rem
     }
 }
 
 .c-nav-toggle {
     display: none;
     position: relative;
     width: 30px;
     height: 30px;
     margin: 0 0 0 .9375rem;
     padding: 0;
     border: 0;
     outline: none;
     background-color: transparent;
     cursor: pointer
 }
 
 @media (max-width:768px) {
     .c-nav-toggle {
         display: block
     }
 }
 
 .c-nav-toggle__bar {
     display: block;
     position: relative;
     width: 30px;
     height: 4px;
     transition: all .3s;
     border-radius: 4px;
     background-color: #354052
 }
 
 .c-nav-toggle__bar:first-child {
     -webkit-transform: translateY(-6px);
     transform: translateY(-6px)
 }
 
 .c-nav-toggle__bar:last-child {
     -webkit-transform: translateY(6px);
     transform: translateY(6px)
 }
 
 .c-navbar {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     width: 100%;
     min-width: 100%;
     min-height: 70px;
     padding: 13px 30px;
     border-bottom: 1px solid #e6eaee;
     background-color: #fff
 }
 
 @media (max-width:768px) {
     .c-navbar {
         padding: .625rem .9375rem
     }
 }
 
 .c-navbar__search {
     width: 270px!important
 }
 
 .c-navbar__brand {
     display: inline-block;
     width: 44px;
     height: 44px;
     margin: 0 20px 0 0
 }
 
 @media (max-width:768px) {
     .c-navbar__brand {
         margin-right: auto
     }
 }
 
 .c-navbar__title {
     margin: 0;
     padding: 0;
     font-size: 1.125rem
 }
 
 .c-navbar--inverse {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-navbar--inverse .c-nav,
 .c-navbar--inverse .c-nav__item {
     border-color: #53b2f9
 }
 
 .c-navbar--inverse .c-avatar.has-dropdown:after,
 .c-navbar--inverse .c-nav__link {
     color: #fff
 }
 
 .c-navbar--inverse .c-nav-toggle__icon,
 .c-navbar--inverse .c-nav-toggle__icon:after,
 .c-navbar--inverse .c-nav-toggle__icon:before {
     background-color: #fff;
     opacity: .95
 }
 
 .c-notification {
     display: inline-block;
     position: relative;
     margin-right: .625rem
 }
 
 .c-notification__icon {
     color: #7f8fa4;
     font-size: 20px
 }
 
 .c-notification__number {
     display: inline-block;
     position: absolute;
     top: -3px;
     right: -.625rem;
     min-width: 20px;
     height: 20px;
     padding: 0 5px;
     border-radius: 20px;
     background-color: #ff7800;
     color: #fff;
     font-size: .75rem;
     line-height: 20px;
     text-align: center
 }
 
 .c-pagination {
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     margin-bottom: 15px;
     text-align: center
 }
 
 .c-pagination,
 .c-pagination__list {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex
 }
 
 .c-pagination__list {
     font-size: 0
 }
 
 .c-pagination__item {
     display: inline-block;
     margin-left: -1px
 }
 
 .c-pagination__item:first-child .c-pagination__control {
     border-right: 0;
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .c-pagination__item:last-child .c-pagination__control {
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-pagination__control {
     display: block;
     height: 2.1875rem;
     padding: 0 16px;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     color: #ced0da;
     font-size: .875rem;
     font-weight: 600;
     line-height: 2.1875rem;
     text-align: center
 }
 
 .c-pagination__control i {
     position: relative;
     bottom: -1px;
     transition: color .15s;
     color: #ced0da;
     font-size: 16px
 }
 
 .c-pagination__control:hover {
     color: #354052;
     text-decoration: none
 }
 
 .c-pagination__control:hover i {
     color: #354052
 }
 
 .c-pagination__link {
     display: block;
     height: 2.1875rem;
     padding: 0 16px;
     border: 1px solid #e6eaee;
     background-color: #fff;
     color: rgba(53, 64, 82, .5);
     font-size: 14px;
     line-height: 2.1875rem;
     text-align: center
 }
 
 .c-pagination__link.is-active {
     color: #354052
 }
 
 .c-pagination__link:hover {
     color: #354052;
     text-decoration: none
 }
 
 .c-pagination__counter {
     display: inline-block;
     margin: 0;
     padding: 8px 15px;
     color: #7f8fa4;
     vertical-align: middle
 }
 
 .c-popover {
     display: block;
     position: absolute;
     top: 0;
     left: 0;
     max-width: 18.75rem;
     padding: .9375rem 20px;
     border-radius: 4px;
     background-clip: padding-box;
     background-color: #1d2531;
     color: #fff;
     text-align: center;
     z-index: 400;
     word-wrap: break-word
 }
 
 .c-popover .arrow {
     display: none
 }
 
 .c-popover:before {
     position: absolute;
     border: 6px solid transparent;
     content: " "
 }
 
 .c-popover.fade {
     transition: opacity .3s;
     opacity: 0
 }
 
 .c-popover.fade.show {
     opacity: 1
 }
 
 .c-popover.bs-popover-top {
     margin-bottom: .9375rem
 }
 
 .c-popover.bs-popover-top:before {
     top: 100%;
     left: 50%;
     -webkit-transform: translate(-50%);
     transform: translate(-50%);
     border-top-color: #1d2531
 }
 
 .c-popover.bs-popover-bottom {
     margin-top: .9375rem
 }
 
 .c-popover.bs-popover-bottom:before {
     bottom: 100%;
     left: 50%;
     -webkit-transform: translate(-50%);
     transform: translate(-50%);
     border-bottom-color: #1d2531
 }
 
 .c-popover.bs-popover-right {
     margin-left: .9375rem
 }
 
 .c-popover.bs-popover-right:before {
     top: 50%;
     right: 100%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     border-right-color: #1d2531
 }
 
 .c-popover.bs-popover-left {
     margin-right: .9375rem
 }
 
 .c-popover.bs-popover-left:before {
     top: 50%;
     left: 100%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     border-left-color: #1d2531
 }
 
 .c-progress {
     display: block;
     width: 100%;
     height: 20px;
     margin: 0 0 15px;
     border-radius: 20px;
     background-color: #e2e7ee
 }
 
 .c-progress__bar {
     position: relative;
     height: 100%;
     border-radius: 20px;
     text-overflow: ellipsis;
     white-space: nowrap;
     overflow: hidden;
     background: #2da1f8;
     background: linear-gradient(180deg, #2da1f8, #1991eb)
 }
 
 .c-progress--xsmall {
     height: 2px
 }
 
 .c-progress--small {
     height: 4px
 }
 
 .c-progress--medium {
     height: 8px
 }
 
 .c-progress--danger .c-progress__bar {
     background: #f95359;
     background: linear-gradient(180deg, #f95359, #d35847)
 }
 
 .c-progress--info .c-progress__bar {
     background: #2da1f8;
     background: linear-gradient(180deg, #2da1f8, #1991eb)
 }
 
 .c-progress--primary .c-progress__bar {
     background: #475364;
     background: linear-gradient(180deg, #475364, #273142)
 }
 
 .c-progress--success .c-progress__bar {
     background: #5ad946;
     background: linear-gradient(180deg, #5ad946, #2bb415)
 }
 
 .c-progress--warning .c-progress__bar {
     background: #f8cf5d;
     background: linear-gradient(180deg, #f8cf5d, #fdc018)
 }
 
 .c-progress--fancy .c-progress__bar {
     background: #9d90e4;
     background: linear-gradient(180deg, #9d90e4, #8261e6)
 }
 
 .c-progress__bar .c-progress__bar {
     position: absolute;
     top: 0;
     left: 0;
     height: 100%;
     background-color: red;
     z-index: 200
 }
 
 .c-range {
     width: 100%;
     outline: 0;
     -webkit-appearance: none;
     -moz-appearance: none;
     appearance: none
 }
 
 .c-range--inline {
     width: 96px
 }
 
 .c-range::-webkit-slider-thumb {
     position: relative;
     top: -4px;
     width: 10px;
     height: 10px;
     border-radius: 100%;
     cursor: pointer;
     -webkit-appearance: none;
     appearance: none;
     background: #2da1f8;
     background: linear-gradient(180deg, #2da1f8, #1991eb)
 }
 
 .c-range::-webkit-slider-runnable-track {
     height: 2px;
     border-radius: 1px;
     background-color: #ced0da
 }
 
 .c-switch {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     position: relative;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     cursor: pointer
 }
 
 .c-switch:before {
     display: inline-block;
     position: relative;
     width: 48px;
     height: 26px;
     transition: all .3s;
     border: 1px solid #e6eaee;
     border-radius: 83px;
     background-color: #dfe3e9;
     content: " "
 }
 
 .c-switch:after {
     position: absolute;
     top: 3px;
     left: 3px;
     width: 20px;
     height: 20px;
     transition: all .3s;
     border-radius: 83px;
     background-color: #fff;
     content: " "
 }
 
 .c-switch.is-active:before {
     background-color: #34aa44
 }
 
 .c-switch.is-active:after {
     left: 25px
 }
 
 .c-switch.is-disabled {
     cursor: not-allowed
 }
 
 .c-switch.is-disabled:after {
     opacity: .5
 }
 
 .c-switch.is-disabled.is-active:before {
     background-color: #dfe3e9
 }
 
 .c-switch.is-disabled.is-active:after {
     right: 2px
 }
 
 .c-switch__input {
     display: none
 }
 
 .c-switch__label {
     margin: 0 0 0 .625rem;
     color: #354052;
     font-size: .875rem;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none
 }
 
 .c-table {
     display: table;
     width: 100%;
     max-width: 100%;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     border-collapse: collapse
 }
 
 .c-table-responsive {
     display: block;
     width: 100%;
     overflow-x: auto;
     -webkit-overflow-scrolling: touch
 }
 
 .c-table-responsive .c-table {
     display: block;
     overflow-y: hidden
 }
 
 @media (max-width:576px) {
     .c-table-responsive\@mobile {
         display: block;
         width: 100%;
         overflow-x: auto;
         -webkit-overflow-scrolling: touch
     }
     .c-table-responsive\@mobile .c-table {
         display: block;
         overflow-y: hidden
     }
 }
 
 @media (max-width:768px) {
     .c-table-responsive\@tablet {
         display: block;
         width: 100%;
         overflow-x: auto;
         -webkit-overflow-scrolling: touch
     }
     .c-table-responsive\@tablet .c-table {
         display: block;
         overflow-y: hidden
     }
 }
 
 @media (max-width:992px) {
     .c-table-responsive\@desktop {
         display: block;
         width: 100%;
         overflow-x: auto;
         -webkit-overflow-scrolling: touch
     }
     .c-table-responsive\@desktop .c-table {
         display: block;
         overflow-y: hidden
     }
 }
 
 @media (max-width:1200px) {
     .c-table-responsive\@wide {
         display: block;
         width: 100%;
         overflow-x: auto;
         -webkit-overflow-scrolling: touch
     }
     .c-table-responsive\@wide .c-table {
         display: block;
         overflow-y: hidden
     }
 }
 
 .c-table--highlight tbody tr:hover,
 .c-table--zebra tbody tr:nth-of-type(odd) {
     background-color: rgba(230, 234, 238, .3)
 }
 
 .c-table--fixed {
     table-layout: fixed
 }
 
 .c-table__title {
     position: relative;
     padding: 25px 30px;
     border: 1px solid #e6eaee;
     border-bottom: 0;
     border-radius: 4px 4px 0 0;
     background-color: #fff;
     color: #354052;
     font-size: 24px;
     text-align: left
 }
 
 .c-table__title small {
     display: inline-block;
     margin-left: 10px;
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-table__title small:before {
     display: inline-block;
     position: relative;
     bottom: -2px;
     width: 1px;
     height: 14px;
     margin-right: 12px;
     background-color: #dfe3e9;
     content: ""
 }
 
 .c-table__title-action {
     position: absolute;
     top: 30px;
     right: 30px;
     color: #afb4bb;
     font-size: 22px
 }
 
 .c-table__head--slim {
     padding: 0;
     background-color: #f5f8fa
 }
 
 .c-table__head--slim .c-table__cell {
     padding: 10px 0 10px 30px
 }
 
 .c-table__row {
     border-top: 1px solid #e6eaee;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-table__row:last-child {
     border-bottom: 0
 }
 
 .c-table__row--danger,
 .c-table__row--info,
 .c-table__row--success,
 .c-table__row--warning {
     border-left: 3px solid
 }
 
 .c-table__row--success {
     border-left-color: #1bb934
 }
 
 .c-table__row--danger {
     border-left-color: #ed1c24
 }
 
 .c-table__row--info {
     border-left-color: #2ea1f8
 }
 
 .c-table__row--warning {
     border-left-color: #fd9a18
 }
 
 .c-table__cell {
     padding: 20px 0 20px 30px;
     color: #354052;
     font-size: .875rem;
     font-weight: 500;
     text-align: left;
     white-space: nowrap;
     vertical-align: middle
 }
 
 .c-table__cell:last-child {
     padding-right: 1.875rem
 }
 
 .c-table__cell--head {
     color: #7f8fa4;
     font-size: .875rem;
     font-weight: 500
 }
 
 .c-table__cell--img {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-tabs__list {
     padding: 20px 30px;
     border: 1px solid #dfe3e9;
     border-top-left-radius: 4px;
     border-top-right-radius: 4px;
     background-color: #fff
 }
 
 .c-tabs__list li {
     display: inline-block
 }
 
 .c-tabs__link {
     margin: 0 30px 0 0;
     padding: 0 0 21px;
     color: #7f8fa4;
     font-size: 1rem
 }
 
 .c-tabs__link:hover {
     color: #354052;
     text-decoration: none
 }
 
 .c-tabs__link.active,
 .c-tabs__link.is-active {
     border-bottom: 2px solid #2ea1f8;
     color: #2d3848;
     font-weight: 600
 }
 
 .c-tabs__list--splitted {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     padding: 0;
     border: 0;
     background-color: transparent
 }
 
 .c-tabs__list--splitted .c-tabs__item {
     -ms-flex-item-align: end;
     align-self: flex-end;
     margin: 0
 }
 
 .c-tabs__list--splitted .c-tabs__link {
     display: inline-block;
     -ms-flex-item-align: end;
     align-self: flex-end;
     margin-right: 4px;
     padding: 15px 30px;
     border: 1px solid #dfe3e9;
     border-top-left-radius: 4px;
     border-top-right-radius: 4px;
     font-size: 14px;
     background: #fff;
     background: linear-gradient(180deg, #fff, #f4f7fa)
 }
 
 .c-tabs__list--splitted .c-tabs__link.active,
 .c-tabs__list--splitted .c-tabs__link.is-active {
     padding: 20px 35px;
     border-bottom-color: #fff;
     background: #fff;
     font-size: 18px
 }
 
 .c-tabs__pane {
     display: none;
     margin: 0;
     padding: 20px 30px;
     border: 1px solid #dfe3e9;
     border-top: 0;
     border-bottom-left-radius: 4px;
     border-bottom-right-radius: 4px;
     background-color: #fff;
     color: #354052
 }
 
 .c-tabs__pane.active,
 .c-tabs__pane.is-active {
     display: block
 }
 
 .c-toggle {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     width: 100%;
     height: 45px;
     cursor: pointer
 }
 
 .c-toggle.is-disabled {
     cursor: not-allowed;
     opacity: .5
 }
 
 .c-toggle.is-disabled .c-toggle__btn.is-active {
     border-color: #e7e9ed;
     background-color: #fafbfc;
     color: #565f6d
 }
 
 .c-toggle__btn {
     width: 50%;
     height: 45px;
     transition: all .3s;
     border: 1px solid #e7e9ed;
     border-radius: 4px;
     background-color: #fafbfc;
     color: #565f6d;
     font-size: 14px;
     text-align: center;
     text-transform: uppercase;
     cursor: inherit
 }
 
 .c-toggle__btn:first-child {
     border-top-right-radius: 0;
     border-bottom-right-radius: 0
 }
 
 .c-toggle__btn:nth-child(2) {
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-toggle__btn.is-active {
     border-color: #249533;
     background-color: #39b54a;
     color: #fff
 }
 
 .c-toggle__label {
     width: 100%;
     height: 45px;
     line-height: 45px;
     cursor: inherit
 }
 
 .c-toggle__input {
     display: none;
     width: 100%;
     height: 100%
 }
 
 .c-toolbar {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding: 20px 30px;
     background-color: #fff;
     box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .07)
 }
 
 @media (max-width:768px) {
     .c-toolbar {
         padding: 20px 15px
     }
 }
 
 .c-toolbar__title {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     margin: 0;
     color: #354052;
     font-size: 18px
 }
 
 .c-toolbar__title.has-divider:after {
     display: block;
     width: 1px;
     height: 15px;
     margin: 0 13px;
     background-color: #dfe3e9;
     content: " "
 }
 
 .c-toolbar__meta {
     margin: 0;
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-toolbar__icon {
     display: block;
     color: #ced0da;
     font-size: 18px
 }
 
 .c-toolbar__icon.is-active {
     color: #2ea1f8
 }
 
 .c-toolbar__icon.has-divider:after {
     display: inline-block;
     position: relative;
     top: 0;
     width: 1px;
     height: 15px;
     margin: 0 13px;
     background-color: #dfe3e9;
     content: " "
 }
 
 .c-toolbar__link {
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-toolbar__link.has-dropdown:after {
     display: inline-block;
     margin-left: 10px;
     color: inherit;
     font-family: FontAwesome;
     font-size: 12px;
     content: "\f3d0"
 }
 
 @media only screen and (max-width:576px) {
     .c-toolbar__nav {
         margin-right: auto;
         margin-left: auto
     }
 }
 
 .c-toolbar__nav-item {
     margin-right: 25px;
     padding: 0 0 27px;
     color: #7f8fa4;
     font-size: 1rem
 }
 
 .c-toolbar__nav-item:last-child {
     margin-right: 0
 }
 
 .c-toolbar__nav-item:hover {
     color: #354052;
     text-decoration: none
 }
 
 .c-toolbar__nav-item.is-active {
     border-bottom: 2px solid #2ea1f8;
     color: #2d3848;
     font-weight: 600
 }
 
 @media only screen and (max-width:576px) {
     .c-toolbar__nav-item {
         padding-bottom: 20px
     }
 }
 
 .c-toolbar__state {
     border-right: 1px solid #e6eaee;
     text-align: center
 }
 
 @media (max-width:768px) {
     .c-toolbar__state {
         margin-bottom: 1.875rem;
         border-right: 0
     }
     .c-toolbar__state:nth-child(3),
     .c-toolbar__state:nth-child(4) {
         margin-bottom: 0
     }
 }
 
 .c-toolbar__state:last-child {
     border-right: 0
 }
 
 .c-toolbar__state-number {
     margin-bottom: 0;
     font-size: 1.75rem
 }
 
 .c-toolbar__state-title {
     color: #7f8fa4;
     font-size: .875rem;
     text-transform: uppercase
 }
 
 .c-tooltip {
     position: relative
 }
 
 .c-tooltip:after {
     max-height: 5rem;
     padding: .625rem .9375rem;
     border-radius: 4px;
     background-color: #1d2531;
     color: #fff;
     font-size: .875rem;
     font-weight: 400;
     line-height: 1.5;
     text-align: center;
     text-transform: none;
     white-space: nowrap;
     content: attr(aria-label);
     overflow: hidden;
     z-index: 200
 }
 
 .c-tooltip:after,
 .c-tooltip:before {
     position: absolute;
     transition: opacity .3s;
     opacity: 0;
     visibility: hidden
 }
 
 .c-tooltip:before {
     border: 6px solid transparent;
     content: " "
 }
 
 .c-tooltip:hover:after,
 .c-tooltip:hover:before {
     opacity: 1;
     visibility: visible
 }
 
 .c-tooltip--top:after {
     bottom: 100%;
     left: 50%;
     -webkit-transform: translate(-50%, -12px);
     transform: translate(-50%, -12px)
 }
 
 .c-tooltip--top:before {
     bottom: 100%;
     left: 50%;
     -webkit-transform: translate(-50%);
     transform: translate(-50%);
     border-top-color: #1d2531
 }
 
 .c-tooltip--bottom:after {
     top: 100%;
     left: 50%;
     -webkit-transform: translate(-50%, 12px);
     transform: translate(-50%, 12px)
 }
 
 .c-tooltip--bottom:before {
     top: 100%;
     left: 50%;
     -webkit-transform: translate(-50%);
     transform: translate(-50%);
     border-bottom-color: #1d2531
 }
 
 .c-tooltip--right:after {
     top: 50%;
     left: 100%;
     -webkit-transform: translate(12px, -50%);
     transform: translate(12px, -50%)
 }
 
 .c-tooltip--right:before {
     top: 50%;
     left: 100%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     border-right-color: #1d2531
 }
 
 .c-tooltip--left:after {
     top: 50%;
     right: 100%;
     -webkit-transform: translate(-12px, -50%);
     transform: translate(-12px, -50%)
 }
 
 .c-tooltip--left:before {
     top: 50%;
     right: 100%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     border-left-color: #1d2531
 }
 
 .c-profile-card {
     margin: 0 0 .9375rem;
     padding: .625rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-profile-card__cover {
     height: 100px;
     border-top-left-radius: 3px;
     border-top-right-radius: 3px;
     overflow: hidden
 }
 
 .c-profile-card__cover>img {
     display: block
 }
 
 .c-profile-card__user {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     top: -20px
 }
 
 .c-profile-card__avatar {
     position: relative;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     width: 75px;
     height: 75px;
     margin: 0 .9375rem 0 0;
     border: 2px solid #fff;
     border-radius: 100%;
     overflow: hidden
 }
 
 .c-profile-card__avatar>img {
     display: block
 }
 
 .c-profile-card__name {
     -ms-flex-item-align: end;
     align-self: flex-end;
     margin: .9375rem 0 0;
     font-size: 1rem;
     line-height: 1.25
 }
 
 .c-profile-card__username {
     display: block;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-profile-card__stats {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex
 }
 
 .c-profile-card__stats:last-child {
     margin-right: 0
 }
 
 .c-profile-card__state {
     margin-right: 30px;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 600;
     line-height: 1.375;
     text-transform: uppercase
 }
 
 .c-profile-card__state:last-child {
     margin-right: 0
 }
 
 .c-profile-card__state-number {
     display: block;
     margin: 0;
     color: #354052;
     font-size: 1.25rem;
     font-weight: 600
 }
 
 .c-profile-card__social {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: 0 .625rem 12px;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-profile-card__social-icon {
     display: block;
     width: 38px;
     height: 38px;
     border-radius: 100%;
     color: #fff;
     line-height: 38px;
     text-align: center
 }
 
 .c-profile-card__social-icon:hover {
     color: #fff
 }
 
 .c-profile-card__meta {
     padding: .9375rem .625rem 0
 }
 
 .c-profile-card__meta:last-child {
     margin-bottom: 0
 }
 
 .c-profile-card__meta-item {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     margin: 0 0 .625rem;
     color: #354052;
     font-size: .875rem
 }
 
 .c-profile-card__meta-item>i {
     margin-right: .75rem;
     color: #7f8fa4;
     font-size: 1rem
 }
 
 .c-event {
     margin-bottom: 1.875rem;
     padding: .625rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background: #fff
 }
 
 .c-event__img {
     position: relative;
     margin-bottom: .9375rem;
     border-radius: 3px;
     overflow: hidden
 }
 
 .c-event__img>img {
     display: block
 }
 
 .c-event__status {
     position: absolute;
     top: .625rem;
     right: .625rem;
     padding: 5px 12px;
     border-radius: 4px;
     background: rgba(49, 53, 63, .25);
     color: #fff;
     font-size: .75rem;
     font-weight: 500;
     text-transform: uppercase
 }
 
 .c-event__meta {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: 5px;
     padding: 0 10px 0 20px
 }
 
 .c-event__title {
     margin: 0;
     font-size: 1rem;
     font-weight: 500
 }
 
 .c-event__place {
     display: block;
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-event__btn {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-sidebar {
     position: relative;
     height: 100%;
     margin: 0;
     padding-bottom: 1.875rem;
     background-color: #222c3c;
     overflow: auto;
     z-index: 200
 }
 
 .c-sidebar::-webkit-scrollbar {
     width: 6px;
     background-color: transparent
 }
 
 .c-sidebar::-webkit-scrollbar-thumb {
     border-radius: 6px;
     background-color: rgba(0, 0, 0, .5)
 }
 
 .c-sidebar__brand {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     height: 70px;
     padding-left: 1.875rem;
     border-bottom: 1px solid #2a3547;
     color: #e6eaee;
     font-size: 16px;
     vertical-align: middle
 }
 
 .c-sidebar__brand:hover {
     color: #e6eaee;
     text-decoration: none
 }
 
 .c-sidebar__brand-img {
     display: inline-block;
     height: 44px;
     margin-right: .625rem
 }
 
 .c-sidebar__title {
     margin-top: 25px;
     margin-bottom: 15px;
     padding-left: 30px;
     color: #7f8fa4;
     font-size: 12px;
     text-transform: uppercase
 }
 
 .c-sidebar__divider {
     display: block;
     width: 100%;
     height: 1px;
     background-color: #2a3547
 }
 
 .c-sidebar__item {
     position: relative;
     transition: background-color .2s
 }
 
 .c-sidebar__item.has-submenu>.c-sidebar__link:after {
     display: inline-block;
     position: absolute;
     top: 12px;
     right: 30px;
     color: #b7c0cd;
     font-family: FontAwesome;
     content: "\f0d7"
 }
 
 .c-sidebar__item.is-open {
     background-color: #1d2531;
     box-shadow: inset 3px 0 0 0 #00a8ff
 }
 
 .c-sidebar__item.is-open>.c-sidebar__link:after {
     content: "\f0d8"
 }
 
 .c-sidebar__link {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding: 10px 0 10px 30px;
     transition: background-color .2s;
     color: #b7c0cd;
     font-size: .875rem
 }
 
 .c-sidebar__link.is-active {
     background-color: #1d2531;
     box-shadow: inset 3px 0 0 0 #00a8ff
 }
 
 .c-sidebar__link:hover {
     color: #fff;
     text-decoration: none
 }
 
 .c-sidebar__submenu.collapse {
     display: none
 }
 
 .c-sidebar__submenu.show {
     display: block
 }
 
 .c-sidebar__submenu.collapsing {
     position: relative;
     height: 0;
     transition: height .3s;
     overflow: hidden
 }
 
 .c-sidebar--light {
     border-right: 1px solid #e6eaee;
     background-color: #fff
 }
 
 .c-sidebar--light .c-sidebar__divider {
     background-color: #e6eaee
 }
 
 .c-sidebar--light .c-sidebar__brand {
     border-color: #e6eaee;
     color: #354052
 }
 
 .c-sidebar--light .c-sidebar__item.has-submenu>.c-sidebar__link:after {
     color: #b7c0cd
 }
 
 .c-sidebar--light .c-sidebar__item.is-open {
     background-color: #fafbfc
 }
 
 .c-sidebar--light .c-sidebar__link {
     color: #7f8fa4
 }
 
 .c-sidebar--light .c-sidebar__link.is-active {
     background-color: transparent;
     color: #354052
 }
 
 .c-sidebar--light .c-sidebar__link.is-active:hover,
 .c-sidebar--light .c-sidebar__link.is-active i {
     color: #2ea1f8
 }
 
 .c-sidebar--light .c-sidebar__link:hover {
     color: #354052
 }
 
 @media (max-width:992px) {
     .c-sidebar--light {
         box-shadow: 1px 1px 5px rgba(0, 0, 0, .1)
     }
 }
 
 .c-sidebar-toggle {
     display: none;
     position: relative;
     width: 30px;
     height: 30px;
     margin: 0 .9375rem 0 0;
     padding: 0;
     border: 0;
     outline: none;
     background-color: transparent;
     cursor: pointer
 }
 
 @media (max-width:992px) {
     .c-sidebar-toggle {
         display: block
     }
 }
 
 .c-sidebar-toggle__bar {
     display: block;
     position: relative;
     width: 30px;
     height: 4px;
     transition: all .3s;
     border-radius: 4px;
     background-color: #354052
 }
 
 .c-sidebar-toggle__bar:first-child {
     -webkit-transform: translateY(-6px);
     transform: translateY(-6px)
 }
 
 .c-sidebar-toggle__bar:last-child {
     -webkit-transform: translateY(6px);
     transform: translateY(6px)
 }
 
 .c-project {
     margin-bottom: 1.875rem;
     padding: .625rem .625rem 20px;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background: #fff
 }
 
 .c-project:hover .c-project__profile:nth-child(n) {
     -webkit-transform: translateX(0);
     transform: translateX(0)
 }
 
 .c-project__img {
     position: relative;
     margin-bottom: .75rem;
     border-radius: 4px;
     overflow: hidden
 }
 
 .c-project__img>img {
     display: block
 }
 
 .c-project__title {
     margin: 0;
     padding-left: .625rem;
     font-size: 1rem;
     font-weight: 500
 }
 
 .c-project__status {
     display: block;
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-project__team {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     margin-top: 1.25rem;
     padding-left: .625rem
 }
 
 .c-project__profile {
     display: block;
     position: relative;
     width: 32px;
     height: 32px;
     margin-right: .3125rem;
     transition: -webkit-transform .3s;
     transition: transform .3s;
     transition: transform .3s, -webkit-transform .3s;
     text-align: center
 }
 
 .c-project__profile:nth-child(2) {
     -webkit-transform: translateX(-20px);
     transform: translateX(-20px)
 }
 
 .c-project__profile:nth-child(3) {
     -webkit-transform: translateX(-40px);
     transform: translateX(-40px)
 }
 
 .c-project__profile:nth-child(4) {
     -webkit-transform: translateX(-60px);
     transform: translateX(-60px)
 }
 
 .c-project__profile:nth-child(5) {
     -webkit-transform: translateX(-80px);
     transform: translateX(-80px)
 }
 
 @media (max-width:1200px) {
     .c-project__profile:nth-child(n) {
         -webkit-transform: translateX(0);
         transform: translateX(0)
     }
 }
 
 .c-project__profile img {
     border-radius: 100%
 }
 
 .c-project__profile--btn {
     border-radius: 100%;
     background-color: #e6eaee;
     color: #7f8fa4;
     font-size: .875rem;
     line-height: 32px
 }
 
 .c-project__profile--btn:hover {
     background-color: #1bb934;
     color: #fff
 }
 
 .c-post {
     margin: 0 0 .9375rem;
     border-radius: 4px
 }
 
 .c-post,
 .c-post__content {
     width: 100%;
     min-width: 100%
 }
 
 .c-post__content {
     min-height: 120px;
     padding: .625rem 1.875rem .625rem 20px;
     border: 1px solid #e6eaee;
     background-color: #fff;
     color: #354052;
     font-size: .875rem;
     overflow-y: auto;
     resize: none
 }
 
 .c-post__toolbar {
     padding: 12px;
     border: 1px solid #e6eaee;
     border-top: 0;
     background-color: #fafbfc;
     overflow: hidden
 }
 
 .c-post__submit {
     margin-right: 12px;
     padding-right: .8rem;
     padding-left: .8rem
 }
 
 .c-post__time {
     position: relative;
     padding: 0 0 0 12px
 }
 
 .c-post__time:before {
     display: inline-block;
     position: absolute;
     top: -24px;
     left: 0;
     width: 1px;
     height: 70px;
     background-color: #e6eaee;
     content: ""
 }
 
 .c-post__time i {
     margin-right: 5px
 }
 
 @media (max-width:576px) {
     .c-post__time {
         display: none
     }
 }
 
 .c-stream {
     margin: 0 0 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-stream-item {
     padding: .9375rem 20px .9375rem 12px;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-stream-item__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: .3125rem
 }
 
 .c-stream-item__name {
     color: #354052;
     font-weight: 400
 }
 
 .c-stream-item__username {
     margin: 0 0 0 .3125rem;
     color: #7f8fa4;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-stream-item__name:hover .c-stream-item__username {
     display: inline-block;
     text-decoration: none
 }
 
 .c-stream-item__time {
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-stream-item__gallery {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: .9375rem
 }
 
 .c-stream-item__gallery>img {
     width: 49%;
     border-radius: 4px
 }
 
 .c-stream-item__actionlist {
     display: inline-block
 }
 
 .c-stream-item__action {
     display: inline-block;
     margin: 0 1.875rem 0 0;
     color: #7f8fa4;
     vertical-align: middle
 }
 
 .c-stream-item__action:hover {
     text-decoration: none
 }
 
 .c-stream-item__action>i {
     margin-right: .3125rem;
     font-size: .875rem
 }
 
 .c-strem-item__actiontoggle {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     bottom: -.1875rem;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     color: #7f8fa4
 }
 
 .c-gallery-card {
     margin: 0 0 1.875rem;
     padding: 10px;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background: #fff
 }
 
 .c-gallery-card__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin: 0 0 .9375rem
 }
 
 .c-gallery-card__title {
     display: inline-block;
     margin: 0;
     font-size: 1rem
 }
 
 .c-gallery-card__items {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-wrap: wrap;
     flex-wrap: wrap;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between
 }
 
 .c-gallery-card__img {
     width: calc(50% - 5px);
     margin: 0 0 .625rem;
     border-radius: 4px
 }
 
 .c-state {
     position: relative;
     margin-bottom: 1.875rem;
     padding: .9375rem;
     border-radius: 4px
 }
 
 .c-state__title {
     margin: .625rem 0 0 .9375rem;
     color: #fff;
     font-size: .75rem;
     text-transform: uppercase;
     opacity: .7
 }
 
 .c-state__number {
     display: block;
     margin: 0 0 20px .9375rem;
     color: #fff;
     font-size: 2.25rem;
     font-weight: 400
 }
 
 .c-state__status {
     margin: 0;
     padding: .625rem 0 0 .9375rem;
     border-top: 1px solid hsla(0, 0%, 100%, .2);
     color: #fff;
     font-size: .75rem;
     font-weight: 400;
     text-transform: uppercase
 }
 
 .c-state__indicator {
     display: inline-block;
     position: absolute;
     top: .9375rem;
     right: .9375rem;
     color: #fff;
     font-size: 38px;
     opacity: .2
 }
 
 .c-state__indicator>i {
     display: block
 }
 
 .c-state,
 .c-state--info {
     background-color: #3b517c
 }
 
 .c-state--success {
     background-color: #66b92e
 }
 
 .c-state--warning {
     background-color: #da932c
 }
 
 .c-state--danger {
     background-color: #d65b4a
 }
 
 .c-todo {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: 1.25rem 1.875rem;
     border-top: 1px solid #e6eaee;
     font-size: 0;
     font-weight: 400
 }
 
 .c-todo.is-disabled .c-todo__label {
     color: rgba(53, 64, 82, .5);
     cursor: default
 }
 
 .c-todo.is-disabled .c-todo__label:before {
     border-color: #dfe3e9;
     background-color: #e6eaee;
     text-decoration: none
 }
 
 .c-todo__input {
     position: absolute;
     width: 1px;
     height: 1px;
     margin: -1px;
     padding: 0;
     border: 0;
     white-space: nowrap;
     overflow: hidden;
     clip: rect(0 0 0 0);
     -webkit-clip-path: inset(50%);
     clip-path: inset(50%)
 }
 
 .c-todo__input+.c-todo__label {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     position: relative;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding-left: 1.5625rem;
     color: #354052;
     font-size: .875rem;
     cursor: pointer
 }
 
 .c-todo.is-completed .c-todo__input+.c-todo__label {
     text-decoration: line-through;
     opacity: .5
 }
 
 .c-todo__input+.c-todo__label:before {
     display: inline-block;
     position: absolute;
     top: 2px;
     left: 0;
     width: 16px;
     height: 16px;
     margin: 0 15px 0 0;
     transition: all .3s;
     border: 1px solid #b7c0cd;
     border-radius: 100%;
     background-color: #fff;
     color: #fff;
     font-family: FontAwesome;
     font-size: 10px;
     line-height: 15px;
     text-align: center;
     text-decoration: none;
     content: "";
     visibility: visible
 }
 
 .c-todo__input:checked+.c-todo__label:before {
     border-color: #b7c0cd;
     background-color: #b7c0cd;
     color: #fff;
     content: "\f00c"
 }
 
 .c-todo-stream {
     display: block;
     position: relative
 }
 
 .c-todo-stream:before {
     position: absolute;
     top: 7.5%;
     left: 7px;
     width: 2px;
     height: 85%;
     background-color: #b7c0cd;
     content: "";
     opacity: .2
 }
 
 .c-todo-stream .c-todo {
     margin-bottom: 15px;
     padding: 0;
     border-top: 0
 }
 
 .c-messanger {
     background-color: #fff
 }
 
 .c-messages {
     height: calc(100vh - 139px);
     border-right: 1px solid #e6eaee;
     overflow-y: auto
 }
 
 .c-messages::-webkit-scrollbar {
     width: 6px;
     background-color: transparent
 }
 
 .c-messages::-webkit-scrollbar-thumb {
     border-radius: 6px;
     background-color: rgba(0, 0, 0, .5)
 }
 
 .c-message {
     display: block;
     position: relative;
     padding: 15px 20px 15px 30px;
     border-bottom: 1px solid #e6eaee;
     color: #354052
 }
 
 .c-message:hover {
     text-decoration: none
 }
 
 .c-message.is-active {
     border-left: 3px solid #53b2f9
 }
 
 .c-message__title {
     margin: 0;
     color: #354052;
     font-size: 16px
 }
 
 .c-message__title-meta {
     display: block;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-message__time {
     position: absolute;
     top: 15px;
     right: 20px;
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-message__snippet {
     margin: 10px 40px 0 0
 }
 
 .c-message__counter {
     display: inline-block;
     position: absolute;
     right: 20px;
     bottom: 25px;
     width: 24px;
     height: 24px;
     border-radius: 100%;
     background-color: #53b2f9;
     color: #fff;
     font-size: 14px;
     line-height: 24px;
     text-align: center
 }
 
 .c-chat {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-orient: vertical;
     -webkit-box-direction: normal;
     -ms-flex-flow: column;
     flex-flow: column;
     overflow-y: auto;
     height: calc(100vh - 139px)
 }
 
 .c-chat__body {
     -webkit-box-flex: 1;
     -ms-flex: 1;
     flex: 1;
     overflow-y: auto
 }
 
 .c-chat__body::-webkit-scrollbar {
     width: 6px;
     background-color: transparent
 }
 
 .c-chat__body::-webkit-scrollbar-thumb {
     border-radius: 6px;
     background-color: rgba(0, 0, 0, .5)
 }
 
 .c-chat__post {
     -webkit-box-flex: 0;
     -ms-flex: 0 0 60px;
     flex: 0 0 60px
 }
 
 .c-chat__message {
     padding: 20px 80px 20px 30px
 }
 
 .c-chat__message-author {
     display: inline-block;
     margin-bottom: 10px;
     color: #354052;
     font-size: 1rem;
     font-weight: 500
 }
 
 .c-chat__message-time {
     margin-left: 8px;
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-chat__message-content {
     margin: 0;
     color: #354052
 }
 
 .c-chat__divider {
     display: block;
     position: relative;
     color: #7f8fa4;
     font-size: .875rem;
     text-align: center
 }
 
 .c-chat__divider:after,
 .c-chat__divider:before {
     position: absolute;
     top: 50%;
     width: 42%;
     height: 1px;
     background-color: #e6eaee;
     content: ""
 }
 
 .c-chat__divider:before {
     left: 0
 }
 
 .c-chat__divider:after {
     right: 0
 }
 
 .c-chat__divider.is-active {
     color: #1a91eb
 }
 
 .c-chat__divider.is-active:after,
 .c-chat__divider.is-active:before {
     background-color: #1a91eb
 }
 
 .c-chat__composer {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: calc(100% - 60px);
     height: 60px;
     margin: 0 auto 20px;
     padding: 0;
     background-color: #fff
 }
 
 .c-chat__composer .c-field__addon {
     padding: 5px 25px
 }
 
 .c-credit-card {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     min-height: 196px
 }
 
 .c-credit-card__card {
     position: absolute;
     width: 50%;
     margin-right: 1.875rem;
     margin-left: 1.875rem;
     padding: 1.5625rem 1.875rem;
     border: 2px solid #2ea1f8;
     border-radius: 8px;
     background-color: #fff
 }
 
 .c-credit-card__logo {
     margin-bottom: .9375rem
 }
 
 .c-credit-card__number {
     margin-bottom: .3125rem;
     color: #354052;
     font-size: 1.25rem;
     font-weight: 600
 }
 
 .c-credit-card__status {
     margin: 0;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-credit-card__user {
     -ms-flex-item-align: end;
     align-self: flex-end;
     width: 100%;
     max-height: 195px;
     padding: 30px 0;
     padding-left: calc(50% + 60px);
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-credit-card__user-title {
     margin-bottom: 20px;
     font-size: 18px;
     font-weight: 600
 }
 
 .c-credit-card__user-meta {
     margin: 0 0 5px;
     font-size: .875rem
 }
 
 .c-search-result {
     position: relative;
     margin-bottom: 1.875rem;
     padding: 1.25rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-search-result__avatar {
     margin-right: 1.25rem
 }
 
 .c-search-result__title {
     margin-bottom: 0;
     font-size: 1rem
 }
 
 .c-search-result__meta {
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-search-result__actionlist {
     position: absolute;
     top: 1.25rem;
     right: 1.25rem
 }
 
 .c-search-result__action {
     display: inline-block;
     margin-left: .625rem;
     float: left
 }
 
 .c-search-result__action>a {
     color: #b7c0cd
 }
 
 .c-search-result__gallery {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-top: 20px
 }
 
 .c-search-result__gallery-item {
     -ms-flex-preferred-size: 30%;
     flex-basis: 30%;
     border-radius: 4px;
     overflow: hidden
 }
 
 .c-search-result__gallery-item>a {
     display: block;
     line-height: 0
 }
 
 .c-search-form {
     margin-bottom: 1.875rem;
     padding: 1.25rem;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-search-form__label {
     margin-bottom: .625rem;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 600;
     text-transform: uppercase
 }
 
 .c-search-form__section {
     margin-bottom: .9375rem
 }
 
 .c-graph-card {
     margin: 0 0 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     overflow: hidden
 }
 
 .c-graph-card__content {
     padding: 1.875rem 1.875rem 0
 }
 
 .c-graph-card__title {
     margin: 0;
     font-size: 1.125rem
 }
 
 .c-graph-card__date {
     margin: 0 0 .625rem;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-graph-card__number {
     margin: 0;
     color: #354052;
     font-size: 2.5rem;
     font-weight: 300
 }
 
 .c-graph-card__status {
     margin: 0;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-graph-card__chart {
     position: relative;
     margin: 0;
     padding: 1.875rem
 }
 
 .c-graph-card__footer {
     height: 100%;
     padding: .9375rem 1.875rem;
     border-top: 1px solid #e6eaee;
     background-color: #fafbfc
 }
 
 .c-divider {
     display: block;
     position: relative;
     height: 1px;
     background-color: #dfe3e9;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 400;
     text-align: center;
     text-transform: uppercase
 }
 
 .c-divider.has-text {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     background-color: transparent
 }
 
 .c-divider.has-text:after,
 .c-divider.has-text:before {
     width: 42%;
     height: 1px;
     background-color: #dfe3e9;
     content: ""
 }
 
 @media (max-width:768px) {
     .c-divider:after,
     .c-divider:before {
         width: 30%
     }
 }
 
 .c-divider--small {
     font-size: .875rem;
     text-transform: none
 }
 
 .c-divider--small.has-text:after,
 .c-divider--small.has-text:before {
     width: 25%;
     height: 1px;
     background-color: #dfe3e9
 }
 
 .c-progress-card {
     margin: 0 0 1.875rem;
     padding: 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-progress-card__title {
     margin: 0;
     font-size: 1.125rem
 }
 
 .c-progress-card__date {
     margin: 0 0 1.875rem;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-progress-card__item {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin: 0 0 .9375rem
 }
 
 .c-progress-card__item:last-child {
     margin-bottom: 0
 }
 
 .c-progress-card__label {
     -ms-flex-preferred-size: 20%;
     flex-basis: 20%;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-progress-card__progress {
     -ms-flex-preferred-size: 80%;
     flex-basis: 80%;
     margin: 0
 }
 
 .c-progress-card__legends {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between
 }
 
 .c-progress-card__legend {
     display: inline-block;
     width: 10px;
     height: 2px;
     margin: 0 .625rem 0 0;
     vertical-align: middle
 }
 
 .c-panel {
     height: 100%;
     margin: 0;
     padding: 1.875rem;
     border: 1px solid #e6eaee;
     border-top: 0;
     background-color: #fff
 }
 
 .c-panel__title {
     margin-bottom: 1.875rem;
     font-size: 1rem
 }
 
 .c-panel__widget:after {
     display: block;
     height: 1px;
     margin: 1.875rem 0;
     background-color: #e6eaee;
     content: " ";
     opacity: .5
 }
 
 .c-panel__widget:last-child:after {
     content: none
 }
 
 .c-state-card {
     -webkit-box-pack: start;
     -ms-flex-pack: start;
     justify-content: flex-start;
     margin-bottom: 1.875rem;
     padding: 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-state-card,
 .c-state-card__icon {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-state-card__icon {
     position: relative;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 60px;
     height: 60px;
     margin-right: 1.25rem;
     border-radius: 100%;
     background-color: #b7c0cd;
     color: #fff;
     font-size: 1.25rem;
     text-align: center
 }
 
 .c-state-card__icon:after {
     position: absolute;
     top: 0;
     right: 0;
     bottom: 0;
     left: 0;
     width: 52px;
     height: 52px;
     margin: auto;
     border: 2px solid #fff;
     border-radius: 100%;
     content: "";
     opacity: .2
 }
 
 .c-state-card__icon--info {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-state-card__icon--fancy {
     background: #886ce6;
     background: linear-gradient(180deg, #886ce6, #7d5be2)
 }
 
 .c-state-card__icon--warning {
     background: #fd9a18;
     background: linear-gradient(180deg, #fd9a18, #f16911)
 }
 
 .c-state-card__icon--success {
     background: #39b54a;
     background: linear-gradient(180deg, #39b54a, #34aa44)
 }
 
 .c-state-card__number {
     display: inline-block;
     position: relative;
     margin: 0;
     font-size: 30px;
     font-weight: 400
 }
 
 .c-state-card__status {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: absolute;
     top: 15px;
     right: -20px;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 14px;
     height: 14px;
     border-radius: 100%;
     background-color: #bbc5d5;
     color: #fff;
     font-size: .625rem;
     text-align: center
 }
 
 .c-state-card__meta {
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 500;
     text-transform: uppercase
 }
 
 .c-summary {
     position: relative;
     padding: 0 0 1.875rem 1.875rem
 }
 
 .c-summary.has-divider:after {
     display: block;
     position: absolute;
     top: 0;
     right: 0;
     width: 1px;
     height: 100%;
     content: "";
     background: #e6eaee;
     background: linear-gradient(0deg, #e6eaee, #fff)
 }
 
 @media (max-width:768px) {
     .c-summary.has-divider:after {
         top: auto;
         bottom: 15px;
         width: 100%;
         height: 1px
     }
 }
 
 .c-summary__title {
     margin-bottom: .9375rem;
     color: #7f8fa4;
     font-size: .875rem;
     font-weight: 500;
     text-transform: uppercase
 }
 
 .c-summary__number {
     display: inline-block;
     position: relative;
     margin: 0;
     font-size: 1.875rem;
     font-weight: 400
 }
 
 .c-summary__status {
     display: block;
     position: absolute;
     top: .9375rem;
     right: -1.25rem;
     width: 14px;
     height: 14px;
     border-radius: 100%;
     background-color: #bbc5d5;
     color: #fff;
     font-size: .625rem;
     line-height: 14px;
     text-align: center
 }
 
 .c-summary__meta {
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 400;
     text-transform: uppercase
 }
 
 .c-chart-container {
     position: relative;
     height: 95px
 }
 
 .c-chart__title {
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-project-card {
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     overflow: hidden
 }
 
 .c-project-card__img {
     display: block
 }
 
 .c-project-card__head {
     padding: 1.25rem 1.875rem;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-project-card__title {
     margin: 0;
     font-size: 1rem
 }
 
 .c-project-card__info {
     margin: 0;
     color: #7f8fa4;
     font-size: .75rem
 }
 
 .c-project-card__meta {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: 1.25rem 1.875rem
 }
 
 .c-menu__title {
     margin: 0 0 .625rem 20px;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 400;
     text-transform: uppercase
 }
 
 .c-menu__link {
     display: inline-block;
     width: 100%;
     padding: 5px 0 5px 20px;
     border-radius: 4px;
     color: #354052;
     font-size: .875rem;
     font-weight: 400
 }
 
 .c-menu__link:hover {
     text-decoration: none
 }
 
 .c-menu__link.is-active {
     background-color: #2ea1f8;
     color: #fff
 }
 
 .c-menu__icon {
     margin-right: .625rem;
     color: #7f8fa4
 }
 
 .c-menu__link:hover .c-menu__icon {
     color: #2ea1f8
 }
 
 .c-board {
     position: relative;
     margin-bottom: 3.125rem
 }
 
 .c-board__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: .625rem
 }
 
 .c-board__title {
     margin-bottom: 0;
     font-size: 1rem;
     font-weight: 600
 }
 
 .c-board__actions {
     display: block;
     position: relative
 }
 
 .c-board__actions>a {
     color: #bbc5d5
 }
 
 .c-board__content {
     position: relative;
     padding: 10px;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fafbfc;
     overflow-y: auto
 }
 
 .c-board__btn {
     display: block;
     position: absolute;
     bottom: -16px;
     left: 16px;
     width: 32px;
     height: 32px;
     border-radius: 100%;
     color: #fff;
     font-size: .875rem;
     line-height: 32px;
     text-align: center;
     z-index: 200;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-board__btn:hover {
     color: #fff
 }
 
 .c-add-board {
     display: block;
     width: 100%;
     margin-top: 2.125rem;
     margin-bottom: 1.875rem;
     padding: .9375rem 1.25rem;
     border-radius: 4px;
     background-color: #b7c0cd;
     color: #fff;
     font-size: .875rem;
     font-weight: 600;
     opacity: .8
 }
 
 .c-add-board>i {
     margin-right: .625rem
 }
 
 .c-add-board:hover {
     color: #fff;
     text-decoration: none;
     opacity: 1
 }
 
 .c-board--danger .c-board__content,
 .c-board--info .c-board__content,
 .c-board--success .c-board__content,
 .c-board--warning .c-board__content {
     border-top: 3px solid #2ea1f8;
     border-top-left-radius: 0;
     border-top-right-radius: 0
 }
 
 .c-board--info .c-board__content {
     border-top-color: #2ea1f8
 }
 
 .c-board--success .c-board__content {
     border-top-color: #1bb934
 }
 
 .c-board--warning .c-board__content {
     border-top-color: #fd9a18
 }
 
 .c-board--danger .c-board__content {
     border-top-color: #ed1c24
 }
 
 .c-task {
     margin-bottom: .625rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     cursor: -webkit-grabbing;
     cursor: grabbing;
     overflow: hidden
 }
 
 .c-task:last-of-type {
     margin-bottom: 0
 }
 
 .c-task__img {
     display: block
 }
 
 .c-task__content {
     margin: 0;
     padding: .9375rem .625rem .625rem;
     color: #354052;
     font-size: .875rem;
     font-weight: 600
 }
 
 .c-task__indicators {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     padding: 0 0 .625rem .625rem
 }
 
 .c-task__indicator,
 .c-task__indicators {
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-task__indicator {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     margin-right: .9375rem;
     color: #7f8fa4;
     font-size: .75rem;
     font-weight: 600
 }
 
 .c-task__indicator>i {
     display: inline-block;
     margin-right: .3125rem;
     color: #bbc5d5;
     font-size: 1rem
 }
 
 .c-task--danger,
 .c-task--info,
 .c-task--success,
 .c-task--warning {
     border-left: 3px solid #2ea1f8;
     border-top-left-radius: 0;
     border-bottom-left-radius: 0
 }
 
 .c-task--info {
     border-left-color: #2ea1f8
 }
 
 .c-task--success {
     border-left-color: #1bb934
 }
 
 .c-task--warning {
     border-left-color: #fd9a18
 }
 
 .c-task--danger {
     border-left-color: #ed1c24
 }
 
 .c-plan {
     margin: 0 5px 30px;
     padding: 40px 20px 25px;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: transparent;
     text-align: left
 }
 
 .c-plan__img {
     position: absolute;
     top: -20px
 }
 
 .c-plan__title {
     margin: 0;
     color: #7f8fa4;
     font-size: 12px;
     text-transform: uppercase
 }
 
 .c-plan__price {
     margin: 0;
     font-size: 28px
 }
 
 .c-plan__note {
     margin: 0;
     font-size: .75rem;
     font-weight: 600;
     text-transform: uppercase
 }
 
 .c-plan__divider {
     display: block;
     width: 100%;
     height: 1px;
     margin: 15px 0;
     background-color: #e6eaee
 }
 
 .c-plan__feature {
     margin-bottom: 5px;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-fileitem {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     padding: .9375rem 0;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-fileitem:first-child {
     padding-top: 0
 }
 
 .c-fileitem:last-child {
     padding-bottom: 0;
     border-bottom: 0
 }
 
 .c-fileitem__content {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-fileitem__img {
     max-width: 60px;
     margin-right: 20px;
     border-radius: 4px;
     overflow: hidden
 }
 
 .c-fileitem__img>img {
     display: block
 }
 
 .c-fileitem__name {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     color: #354052;
     font-weight: 600
 }
 
 .c-fileitem__name>img {
     margin-right: 8px
 }
 
 .c-fileitem__date {
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-fileitem__date i {
     margin: 0 5px
 }
 
 @media (max-width:768px) {
     .c-overview-card .u-border-left,
     .c-overview-card .u-border-right {
         border: 0!important
     }
 }
 
 @media (max-width:768px) {
     .c-overview-card__section {
         margin-bottom: 30px
     }
     .c-overview-card__section h3 {
         font-size: 1.25rem
     }
 }
 
 .c-invoice {
     position: relative;
     padding: 6.25rem 6.25rem 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff;
     color: #354052;
     box-shadow: 0 0 10px 1px rgba(53, 64, 82, .1)
 }
 
 @media (max-width:768px) {
     .c-invoice {
         padding: 1.875rem
     }
 }
 
 .c-invoice__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: 3.125rem
 }
 
 @media (max-width:768px) {
     .c-invoice__header {
         -webkit-box-orient: vertical;
         -webkit-box-direction: normal;
         -ms-flex-flow: column;
         flex-flow: column
     }
 }
 
 .c-invoice__brand {
     margin-bottom: 1.875rem
 }
 
 @media (max-width:768px) {
     .c-invoice__brand {
         margin: 0
     }
 }
 
 .c-invoice__brand-img {
     max-width: 40px;
     margin-bottom: .9375rem
 }
 
 .c-invoice__brand-name {
     font-size: 1.5rem
 }
 
 .c-invoice__title {
     margin-top: 1.875rem
 }
 
 .c-invoice__title h4 {
     margin: 0
 }
 
 .c-invoice__date {
     color: #7f8fa4
 }
 
 .c-invoice__details {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: 6.25rem
 }
 
 .c-invoice__company-name {
     margin-bottom: .9375rem;
     font-size: 1.25rem
 }
 
 .c-invoice__company-address {
     margin-bottom: .9375rem;
     color: #7f8fa4
 }
 
 .c-invoice__body {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex
 }
 
 @media (max-width:768px) {
     .c-invoice__body {
         -webkit-box-orient: vertical;
         -webkit-box-direction: normal;
         -ms-flex-flow: column;
         flex-flow: column
     }
 }
 
 .c-invoice__desc {
     -webkit-box-flex: 0;
     -ms-flex: 0 1 250px;
     flex: 0 1 250px;
     font-size: 1.25rem
 }
 
 @media (max-width:768px) {
     .c-invoice__desc {
         -webkit-box-flex: 0;
         -ms-flex: 0;
         flex: 0;
         margin-bottom: 1.875rem
     }
 }
 
 .c-invoice__number {
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-invoice__table {
     width: 100%
 }
 
 .c-invoice__table .c-table {
     margin-bottom: .625rem
 }
 
 @media (max-width:768px) {
     .c-invoice__table .c-table {
         display: table;
         width: 100%
     }
 }
 
 .c-invoice__terms {
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-invoice__footer {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-top: 6.25rem;
     padding-top: 1.875rem;
     border-top: 1px solid #e6eaee
 }
 
 @media (max-width:576px) {
     .c-invoice__footer {
         -webkit-box-orient: vertical;
         -webkit-box-direction: normal;
         -ms-flex-flow: column;
         flex-flow: column;
         text-align: center
     }
 }
 
 .c-invoice__footer-brand {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 @media (max-width:576px) {
     .c-invoice__footer-brand {
         -webkit-box-orient: vertical;
         -webkit-box-direction: normal;
         -ms-flex-flow: column;
         flex-flow: column;
         -webkit-box-pack: center;
         -ms-flex-pack: center;
         justify-content: center
     }
 }
 
 .c-invoice__footer-brand img {
     max-width: 30px;
     margin-right: .625rem
 }
 
 @media (max-width:576px) {
     .c-invoice__footer-brand img {
         margin-bottom: .625rem
     }
 }
 
 .c-feed {
     position: relative;
     margin-bottom: 1.875rem;
     padding-left: 32px;
     overflow: hidden
 }
 
 .c-feed:before {
     position: absolute;
     top: 4px;
     left: 6px;
     width: 1px;
     height: 100%;
     background-color: #e6eaee;
     content: ""
 }
 
 .c-feed.has-icons {
     padding-left: 47px
 }
 
 .c-feed.has-icons:before {
     left: 14px
 }
 
 .c-feed__item {
     position: relative;
     margin-bottom: 25px
 }
 
 .c-feed__item:before {
     position: absolute;
     top: 4px;
     left: -32px;
     width: 12px;
     height: 12px;
     border-radius: 100%;
     background-color: #2ea1f8;
     content: ""
 }
 
 .c-feed__item:last-child {
     margin-bottom: 0
 }
 
 .c-feed__item:last-child:after {
     position: absolute;
     top: 16px;
     left: -32px;
     width: 12px;
     height: 100%;
     border-radius: 0;
     background-color: #fff;
     content: ""
 }
 
 .c-feed__item.has-icon:before {
     display: none
 }
 
 .c-feed__item.has-icon .c-feed__item-icon {
     display: block;
     position: absolute;
     top: 4px;
     left: -47px;
     width: 30px;
     height: 30px;
     border-radius: 100%;
     background-color: #2ea1f8;
     color: #fff;
     font-size: .875rem;
     line-height: 30px;
     text-align: center
 }
 
 .c-feed__item.has-icon:last-child:after {
     top: 34px;
     left: -47px;
     width: 30px
 }
 
 .c-feed__comment {
     display: inline-block;
     margin: .625rem 0;
     padding: .625rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fafbfc
 }
 
 .c-feed__gallery {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     margin-top: .625rem
 }
 
 .c-feed__gallery-item {
     width: 80px;
     height: 80px;
     margin: 0 .625rem .625rem 0;
     border-radius: 4px;
     overflow: hidden
 }
 
 .c-feed__meta {
     display: block;
     color: #7f8fa4;
     font-size: .875rem
 }
 
 .c-feed__item--fancy:before {
     background: #886ce6;
     background: linear-gradient(180deg, #886ce6, #7d5be2)
 }
 
 .c-feed__item--info:before {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-feed__item--success:before {
     background: #39b54a;
     background: linear-gradient(180deg, #39b54a, #34aa44)
 }
 
 .c-rating {
     display: block
 }
 
 .c-rating__icon {
     color: #bbbfc5;
     font-size: .75rem
 }
 
 .c-rating__icon.is-active {
     color: #fdbc0e
 }
 
 .c-map__visual {
     margin-bottom: 1.875rem
 }
 
 .c-map__labels--left {
     margin-right: .9375rem;
     margin-left: 3.125rem
 }
 
 .c-map__labels--right {
     margin-right: 3.125rem;
     margin-left: .9375rem
 }
 
 @media (max-width:1200px) {
     .c-map__labels {
         margin-right: 0;
         margin-left: 0
     }
 }
 
 .c-map__label {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     margin-bottom: .625rem
 }
 
 .c-map__label:last-child {
     margin-bottom: 0
 }
 
 .c-map__country,
 .c-map__number {
     color: #354052
 }
 
 .c-candidate {
     margin-bottom: 1.875rem;
     padding: .9375rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-candidate__cover {
     max-height: 150px;
     border-top-left-radius: 2px;
     border-top-right-radius: 2px;
     font-size: 0;
     overflow: hidden
 }
 
 .c-candidate__info {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     top: -.9375rem;
     -webkit-box-align: end;
     -ms-flex-align: end;
     align-items: flex-end;
     padding-bottom: .9375rem;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-candidate__avatar {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     position: relative;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     margin-right: .9375rem;
     margin-left: .625rem;
     border: 4px solid #fff;
     border-radius: 100%;
     overflow: hidden
 }
 
 .c-candidate__meta {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between;
     width: 100%
 }
 
 .c-candidate__title {
     margin: 0;
     font-size: 1rem
 }
 
 .c-candidate__country {
     display: block;
     color: #7f8fa4
 }
 
 .c-candidate__country i {
     margin-right: .625rem
 }
 
 .c-candidate__actions {
     -ms-flex-item-align: center;
     align-self: center;
     margin-right: .625rem
 }
 
 .c-candidate__actions a {
     transition: opacity .3s;
     color: #7f8fa4;
     opacity: .5
 }
 
 .c-candidate__actions a:last-child {
     margin-left: .3125rem
 }
 
 .c-candidate__actions a:hover {
     opacity: 1
 }
 
 .c-candidate__footer {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: justify;
     -ms-flex-pack: justify;
     justify-content: space-between
 }
 
 .c-candidate__status {
     margin-right: 5%;
     font-size: .875rem
 }
 
 .c-stage {
     margin-bottom: 1.875rem;
     border: 1px solid #e6eaee;
     border-radius: 4px;
     background-color: #fff
 }
 
 .c-stage__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     padding: .9375rem 1.875rem;
     border-bottom: 1px solid #e6eaee;
     cursor: pointer
 }
 
 .c-stage__header:hover {
     text-decoration: none
 }
 
 .c-stage__header-img {
     max-width: 60px;
     margin-right: 20px;
     border-radius: 4px;
     overflow: hidden
 }
 
 .c-stage__icon {
     width: 30px;
     height: 30px;
     margin-right: .625rem;
     border: 1px solid #e6eaee;
     border-radius: 100%;
     color: #7f8fa4;
     font-size: 14px;
     line-height: 30px;
     text-align: center
 }
 
 .c-stage__panel {
     border-bottom: 1px solid #e6eaee
 }
 
 .c-stage__panel.collapse {
     display: none
 }
 
 .c-stage__panel.collapse.show {
     display: block
 }
 
 .c-stage__panel.collapsing {
     position: relative;
     height: 0;
     transition: height .3s;
     overflow: hidden
 }
 
 .c-stage__panel--mute {
     background-color: #fafbfc
 }
 
 .c-stage__label {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     padding: .9375rem 1.875rem;
     background-color: #1bb934;
     color: #fff
 }
 
 .c-stage__label-icon {
     margin-right: .625rem;
     font-size: 1.75rem
 }
 
 .c-stage__label-title {
     color: #fff
 }
 
 .c-counter-nav {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-line-pack: center;
     align-content: center;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center
 }
 
 .c-counter-nav__title {
     margin-right: .625rem;
     color: #7f8fa4
 }
 
 .c-counter-nav__item {
     margin-right: 1.25rem
 }
 
 .c-counter-nav__link {
     color: #7f8fa4;
     font-size: 14px
 }
 
 .c-counter-nav__link:hover {
     text-decoration: none
 }
 
 .c-counter-nav__link.is-active {
     color: #354052
 }
 
 .c-counter-nav__counter {
     display: -webkit-inline-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 28px;
     height: 28px;
     margin-right: 10px;
     border: 1px solid #e6eaee;
     border-radius: 100%;
     background-color: #fafbfc;
     color: #7f8fa4;
     text-align: center
 }
 
 .c-counter-nav__link.is-active .c-counter-nav__counter {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea);
     border-color: #2ea1f8;
     color: #fff
 }
 
 .c-counter-nav--inverse .c-counter-nav__counter {
     border-radius: 100%;
     border-color: #313c4d;
     background-color: #313c4d;
     color: #828a96;
     text-align: center
 }
 
 .c-counter-nav--inverse .c-counter-nav__link.is-active,
 .c-counter-nav__link.is-active .c-counter-nav--inverse .c-counter-nav__counter {
     color: #fff
 }
 
 .c-chat-dialogue {
     position: fixed;
     right: 1.875rem;
     bottom: 1.875rem
 }
 
 .c-chat-dialogue__btn {
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea);
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 60px;
     height: 60px;
     border-radius: 100%;
     border-color: #1a91eb;
     color: #fff;
     font-size: 1.5rem;
     box-shadow: 0 2px 7px 0 rgba(34, 44, 60, .4);
     cursor: pointer
 }
 
 .c-chat-dialogue__btn .c-chat-dialogue__btn-close,
 .c-chat-dialogue__btn.is-open .c-chat-dialogue__btn-open {
     display: none
 }
 
 .c-chat-dialogue__btn.is-open .c-chat-dialogue__btn-close {
     display: block
 }
 
 .c-chat-dialogue__header {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     padding: 1.875rem;
     border-bottom: 1px solid #e6eaee
 }
 
 .c-chat-dialogue__team {
     position: relative;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     width: 35%
 }
 
 .c-chat-dialogue__team .c-avatar {
     position: absolute;
     top: 0;
     left: 0
 }
 
 .c-chat-dialogue__team .c-avatar:nth-child(2) {
     -webkit-transform: translateX(20px);
     transform: translateX(20px)
 }
 
 .c-chat-dialogue__team .c-avatar:nth-child(3) {
     -webkit-transform: translateX(40px);
     transform: translateX(40px)
 }
 
 .c-chat-dialogue__body {
     position: absolute;
     right: 0;
     bottom: 70px;
     width: 350px;
     -webkit-transform: translateY(-20px);
     transform: translateY(-20px);
     transition: all .3s;
     border-radius: 4px;
     background-color: #fff;
     box-shadow: 0 1px 5px 0 rgba(0, 0, 0, .35);
     opacity: 0;
     overflow: hidden;
     visibility: hidden
 }
 
 .c-chat-dialogue__body.is-active {
     -webkit-transform: translateY(0);
     transform: translateY(0);
     opacity: 1;
     visibility: visible
 }
 
 .c-chat-dialogue__messages {
     height: 400px;
     padding: 0 1.25rem;
     background-color: #fafbfc;
     overflow-y: auto
 }
 
 .c-chat-dialogue__message {
     padding: 10px 20px
 }
 
 .c-chat-dialogue__message-content {
     position: relative;
     max-width: 80%;
     padding: 15px 20px;
     border-radius: 8px;
     color: #fff;
     font-size: .875rem;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea);
     word-wrap: break-word
 }
 
 .c-chat-dialogue__message-content:before {
     position: absolute;
     top: 30%;
     right: 100%;
     -webkit-transform: translateY(-50%);
     transform: translateY(-50%);
     border: 6px solid transparent;
     border-right-color: #2ea1f8;
     content: " "
 }
 
 .c-chat-dialogue__message--self {
     text-align: right
 }
 
 .c-chat-dialogue__message--self .c-chat-dialogue__message-content {
     margin-left: auto;
     border: 1px solid #e6eaee;
     background: #fff;
     color: #354052
 }
 
 .c-chat-dialogue__message--self .c-chat-dialogue__message-content:before {
     left: 100%;
     border: 6px solid transparent;
     border-left-color: #fff
 }
 
 .c-chat-dialogue__footer {
     padding: 1.25rem;
     border-top: 1px solid #e6eaee;
     background-color: #fff
 }
 
 .c-login-horizontal {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     width: 100%
 }
 
 .c-login-horizontal .c-login__content-image,
 .c-login-horizontal .c-login__content-wrapper {
     width: 50%
 }
 
 @media (max-width:768px) {
     .c-login-horizontal .c-login__content-image,
     .c-login-horizontal .c-login__content-wrapper {
         width: 100%
     }
 }
 
 .c-login-horizontal .c-login__header {
     position: relative;
     padding: 1.875rem 0 0;
     padding-left: 1.875rem;
     border: 0;
     background-color: transparent
 }
 
 @media (max-width:768px) {
     .c-login-horizontal .c-login__header {
         padding-top: 3.125rem
     }
 }
 
 .c-login-horizontal .c-login__icon {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: absolute;
     top: -35px;
     right: 0;
     left: 0;
     -webkit-box-align: center;
     -ms-flex-align: center;
     align-items: center;
     -webkit-box-pack: center;
     -ms-flex-pack: center;
     justify-content: center;
     width: 70px;
     height: 70px;
     margin: 0 auto;
     border-radius: 100%;
     color: #fff;
     font-size: 1.25rem;
     background: #2ea1f8;
     background: linear-gradient(180deg, #2ea1f8, #1990ea)
 }
 
 .c-login-horizontal .c-login__icon--left {
     right: auto;
     left: 30px
 }
 
 .c-login-horizontal .c-login__icon--rounded {
     top: -25px;
     width: 45px;
     height: 45px;
     border-radius: 4px
 }
 
 .c-login-horizontal .c-login__title {
     margin: 0;
     font-size: 24px;
     text-align: left
 }
 
 .c-login-horizontal .c-login__content {
     padding: .9375rem 1.875rem 1.875rem
 }
 
 .c-login-horizontal .c-login__content-image {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     position: relative;
     -webkit-box-orient: vertical;
     -webkit-box-direction: normal;
     -ms-flex-flow: column;
     flex-flow: column;
     -webkit-box-pack: end;
     -ms-flex-pack: end;
     justify-content: flex-end;
     padding: 1.875rem;
     border-top-right-radius: 4px;
     border-bottom-right-radius: 4px;
     background-color: #49515d;
     color: #fff;
     overflow: hidden
 }
 
 .c-login-horizontal .c-login__content-image img {
     display: block;
     position: absolute;
     top: 0;
     right: 0;
     bottom: 0;
     left: 0;
     width: 100%;
     height: 100%;
     opacity: .2
 }
 
 .c-login-horizontal .c-login__content-image h3,
 .c-login-horizontal .c-login__content-image p {
     color: #fff
 }
 
 .c-login-horizontal .c-login__content-image p {
     font-size: 1rem;
     opacity: .9
 }
 
 @media (max-width:768px) {
     .c-login-horizontal .c-login__content-image {
         display: none
     }
 }
 
 @media (max-width:992px) {
     .c-sidebar.is-minimized .c-sidebar__icon {
         margin-right: 15px
     }
 }
 
 @media (min-width:992px) {
     .o-page__sidebar.is-minimized {
         width: 70px
     }
     .o-page__sidebar.is-minimized+.o-page__content {
         margin-left: 70px;
         transition: margin .3s
     }
     .o-page__sidebar.is-minimized:hover {
         width: 250px
     }
     .o-page__sidebar.is-minimized:hover+.o-page__content {
         margin-left: 250px
     }
     .c-sidebar.is-minimized {
         width: 70px;
         overflow-x: hidden;
         transition: width .3s
     }
     .c-sidebar.is-minimized .c-sidebar__brand {
         padding: 10px
     }
     .c-sidebar.is-minimized .c-sidebar__brand-img {
         margin-right: 15px
     }
     .c-sidebar.is-minimized .c-sidebar__item {
         border-bottom: 1px solid #2a3547
     }
     .c-sidebar.is-minimized .c-sidebar__link {
         padding: 0
     }
     .c-sidebar.is-minimized .c-sidebar__icon {
         display: block;
         min-width: 70px;
         padding: 15px 0;
         font-size: 20px;
         text-align: center
     }
     .c-sidebar.is-minimized .c-sidebar__item.has-submenu>.c-sidebar__link:after {
         display: none;
         top: 20px
     }
     .c-sidebar.is-minimized:hover {
         width: 250px
     }
     .c-sidebar.is-minimized:hover .c-sidebar__brand-text {
         display: block
     }
     .c-sidebar.is-minimized:hover .c-sidebar__brand-img {
         margin-right: 15px
     }
     .c-sidebar.is-minimized:hover .c-sidebar__item.has-submenu>.c-sidebar__link:after,
     .c-sidebar.is-minimized:hover .c-sidebar__title {
         display: block
     }
 }
 
 .u-h1 {
     font-size: 2.25rem
 }
 
 .u-h2 {
     font-size: 1.75rem
 }
 
 .u-h3 {
     font-size: 1.5rem
 }
 
 .u-h4 {
     font-size: 1.25rem
 }
 
 .u-h5 {
     font-size: 1.125rem
 }
 
 .u-h6 {
     font-size: 1rem
 }
 
 .u-text-mute {
     color: #7f8fa4!important
 }
 
 .u-text-danger {
     color: #ed1c24!important
 }
 
 .u-text-success {
     color: #45b854!important
 }
 
 .u-text-dark {
     color: #354052
 }
 
 .u-text-white {
     color: #fff
 }
 
 .u-text-tiny {
     font-size: .625rem!important
 }
 
 .u-text-xsmall {
     font-size: .75rem!important
 }
 
 .u-text-small {
     font-size: .875rem!important
 }
 
 .u-text-large {
     font-size: 1rem!important
 }
 
 .u-text-big {
     font-size: 3.375rem;
     font-weight: 600;
     line-height: 48px
 }
 
 .u-text-bold {
     font-weight: 600!important
 }
 
 .u-text-right {
     text-align: right!important
 }
 
 .u-text-left {
     text-align: left!important
 }
 
 .u-text-center {
     text-align: center!important
 }
 
 .u-text-uppercase {
     text-transform: uppercase!important
 }
 
 .u-text-lowercase {
     text-transform: lowercase!important
 }
 
 .u-text-capitalize {
     text-transform: capitalize!important
 }
 
 .u-float-left {
     float: left!important
 }
 
 .u-float-right {
     float: right!important
 }
 
 .u-clearfix:after {
     display: table!important;
     clear: both!important;
     content: ""!important
 }
 
 .u-m-zero {
     margin: 0!important
 }
 
 .u-mt-zero {
     margin-top: 0!important
 }
 
 .u-mr-zero {
     margin-right: 0!important
 }
 
 .u-mb-zero {
     margin-bottom: 0!important
 }
 
 .u-ml-zero {
     margin-left: 0!important
 }
 
 .u-mv-zero {
     margin-top: 0!important;
     margin-bottom: 0!important
 }
 
 .u-mh-zero {
     margin-right: 0!important;
     margin-left: 0!important
 }
 
 .u-m-auto {
     margin: auto!important
 }
 
 .u-mt-auto {
     margin-top: auto!important
 }
 
 .u-mr-auto {
     margin-right: auto!important
 }
 
 .u-mb-auto {
     margin-bottom: auto!important
 }
 
 .u-ml-auto {
     margin-left: auto!important
 }
 
 .u-mv-auto {
     margin-top: auto!important;
     margin-bottom: auto!important
 }
 
 .u-mh-auto {
     margin-right: auto!important;
     margin-left: auto!important
 }
 
 .u-m-xsmall {
     margin: .625rem!important
 }
 
 .u-mt-xsmall {
     margin-top: .625rem!important
 }
 
 .u-mr-xsmall {
     margin-right: .625rem!important
 }
 
 .u-mb-xsmall {
     margin-bottom: .625rem!important
 }
 
 .u-ml-xsmall {
     margin-left: .625rem!important
 }
 
 .u-mv-xsmall {
     margin-top: .625rem!important;
     margin-bottom: .625rem!important
 }
 
 .u-mh-xsmall {
     margin-right: .625rem!important;
     margin-left: .625rem!important
 }
 
 .u-m-small {
     margin: .9375rem!important
 }
 
 .u-mt-small {
     margin-top: .9375rem!important
 }
 
 .u-mr-small {
     margin-right: .9375rem!important
 }
 
 .u-mb-small {
     margin-bottom: .9375rem!important
 }
 
 .u-ml-small {
     margin-left: .9375rem!important
 }
 
 .u-mv-small {
     margin-top: .9375rem!important;
     margin-bottom: .9375rem!important
 }
 
 .u-mh-small {
     margin-right: .9375rem!important;
     margin-left: .9375rem!important
 }
 
 .u-m-medium {
     margin: 1.875rem!important
 }
 
 .u-mt-medium {
     margin-top: 1.875rem!important
 }
 
 .u-mr-medium {
     margin-right: 1.875rem!important
 }
 
 .u-mb-medium {
     margin-bottom: 1.875rem!important
 }
 
 .u-ml-medium {
     margin-left: 1.875rem!important
 }
 
 .u-mv-medium {
     margin-top: 1.875rem!important;
     margin-bottom: 1.875rem!important
 }
 
 .u-mh-medium {
     margin-right: 1.875rem!important;
     margin-left: 1.875rem!important
 }
 
 .u-m-large {
     margin: 3.125rem!important
 }
 
 .u-mt-large {
     margin-top: 3.125rem!important
 }
 
 .u-mr-large {
     margin-right: 3.125rem!important
 }
 
 .u-mb-large {
     margin-bottom: 3.125rem!important
 }
 
 .u-ml-large {
     margin-left: 3.125rem!important
 }
 
 .u-mv-large {
     margin-top: 3.125rem!important;
     margin-bottom: 3.125rem!important
 }
 
 .u-mh-large {
     margin-right: 3.125rem!important;
     margin-left: 3.125rem!important
 }
 
 .u-m-xlarge {
     margin: 6.25rem!important
 }
 
 .u-mt-xlarge {
     margin-top: 6.25rem!important
 }
 
 .u-mr-xlarge {
     margin-right: 6.25rem!important
 }
 
 .u-mb-xlarge {
     margin-bottom: 6.25rem!important
 }
 
 .u-ml-xlarge {
     margin-left: 6.25rem!important
 }
 
 .u-mv-xlarge {
     margin-top: 6.25rem!important;
     margin-bottom: 6.25rem!important
 }
 
 .u-mh-xlarge {
     margin-right: 6.25rem!important;
     margin-left: 6.25rem!important
 }
 
 .u-p-zero {
     padding: 0!important
 }
 
 .u-pt-zero {
     padding-top: 0!important
 }
 
 .u-pr-zero {
     padding-right: 0!important
 }
 
 .u-pb-zero {
     padding-bottom: 0!important
 }
 
 .u-pl-zero {
     padding-left: 0!important
 }
 
 .u-pv-zero {
     padding-top: 0!important;
     padding-bottom: 0!important
 }
 
 .u-ph-zero {
     padding-right: 0!important;
     padding-left: 0!important
 }
 
 .u-p-auto {
     padding: auto!important
 }
 
 .u-pt-auto {
     padding-top: auto!important
 }
 
 .u-pr-auto {
     padding-right: auto!important
 }
 
 .u-pb-auto {
     padding-bottom: auto!important
 }
 
 .u-pl-auto {
     padding-left: auto!important
 }
 
 .u-pv-auto {
     padding-top: auto!important;
     padding-bottom: auto!important
 }
 
 .u-ph-auto {
     padding-right: auto!important;
     padding-left: auto!important
 }
 
 .u-p-xsmall {
     padding: .625rem!important
 }
 
 .u-pt-xsmall {
     padding-top: .625rem!important
 }
 
 .u-pr-xsmall {
     padding-right: .625rem!important
 }
 
 .u-pb-xsmall {
     padding-bottom: .625rem!important
 }
 
 .u-pl-xsmall {
     padding-left: .625rem!important
 }
 
 .u-pv-xsmall {
     padding-top: .625rem!important;
     padding-bottom: .625rem!important
 }
 
 .u-ph-xsmall {
     padding-right: .625rem!important;
     padding-left: .625rem!important
 }
 
 .u-p-small {
     padding: .9375rem!important
 }
 
 .u-pt-small {
     padding-top: .9375rem!important
 }
 
 .u-pr-small {
     padding-right: .9375rem!important
 }
 
 .u-pb-small {
     padding-bottom: .9375rem!important
 }
 
 .u-pl-small {
     padding-left: .9375rem!important
 }
 
 .u-pv-small {
     padding-top: .9375rem!important;
     padding-bottom: .9375rem!important
 }
 
 .u-ph-small {
     padding-right: .9375rem!important;
     padding-left: .9375rem!important
 }
 
 .u-p-medium {
     padding: 1.875rem!important
 }
 
 .u-pt-medium {
     padding-top: 1.875rem!important
 }
 
 .u-pr-medium {
     padding-right: 1.875rem!important
 }
 
 .u-pb-medium {
     padding-bottom: 1.875rem!important
 }
 
 .u-pl-medium {
     padding-left: 1.875rem!important
 }
 
 .u-pv-medium {
     padding-top: 1.875rem!important;
     padding-bottom: 1.875rem!important
 }
 
 .u-ph-medium {
     padding-right: 1.875rem!important;
     padding-left: 1.875rem!important
 }
 
 .u-p-large {
     padding: 3.125rem!important
 }
 
 .u-pt-large {
     padding-top: 3.125rem!important
 }
 
 .u-pr-large {
     padding-right: 3.125rem!important
 }
 
 .u-pb-large {
     padding-bottom: 3.125rem!important
 }
 
 .u-pl-large {
     padding-left: 3.125rem!important
 }
 
 .u-pv-large {
     padding-top: 3.125rem!important;
     padding-bottom: 3.125rem!important
 }
 
 .u-ph-large {
     padding-right: 3.125rem!important;
     padding-left: 3.125rem!important
 }
 
 .u-p-xlarge {
     padding: 6.25rem!important
 }
 
 .u-pt-xlarge {
     padding-top: 6.25rem!important
 }
 
 .u-pr-xlarge {
     padding-right: 6.25rem!important
 }
 
 .u-pb-xlarge {
     padding-bottom: 6.25rem!important
 }
 
 .u-pl-xlarge {
     padding-left: 6.25rem!important
 }
 
 .u-pv-xlarge {
     padding-top: 6.25rem!important;
     padding-bottom: 6.25rem!important
 }
 
 .u-ph-xlarge {
     padding-right: 6.25rem!important;
     padding-left: 6.25rem!important
 }
 
 .u-color-success {
     color: #1bb934!important
 }
 
 .u-color-info {
     color: #2ea1f8!important
 }
 
 .u-color-danger {
     color: #ed1c24!important
 }
 
 .u-color-warning {
     color: #fd9a18!important
 }
 
 .u-color-primary {
     color: #354052!important
 }
 
 .u-color-secondary,
 .u-color-white {
     color: #fff!important
 }
 
 .u-bg-success {
     background: #1bb934!important;
     background-color: #1bb934!important
 }
 
 .u-bg-info {
     background: #1a91eb!important;
     background-color: #1a91eb!important
 }
 
 .u-bg-danger {
     background: #ed1c24!important;
     background-color: #ed1c24!important
 }
 
 .u-bg-fancy {
     background: #a388e3!important;
     background-color: #a388e3!important
 }
 
 .u-bg-warning {
     background: #fd9a18!important;
     background-color: #fd9a18!important
 }
 
 .u-bg-primary {
     background: #475364!important;
     background-color: #475364!important
 }
 
 .u-bg-secondary {
     background: #fafbfc!important;
     background-color: #fafbfc!important
 }
 
 .u-bg-white {
     background: #fff!important;
     background-color: #fff!important
 }
 
 .u-color-facebook {
     color: #2492e4!important
 }
 
 .u-color-twitter {
     color: #0af!important
 }
 
 .u-color-pinterest {
     color: #e30f27!important
 }
 
 .u-color-behance {
     color: #187ac6!important
 }
 
 .u-color-dribbble {
     color: #ea4c89!important
 }
 
 .u-color-linkedin {
     color: #222!important
 }
 
 .u-bg-facebook {
     border-color: #2492e4!important;
     background: #2492e4!important
 }
 
 .u-bg-twitter {
     border-color: #0af!important;
     background: #0af!important
 }
 
 .u-bg-pinterest {
     border-color: #e30f27!important;
     background: #e30f27!important
 }
 
 .u-bg-behance {
     border-color: #187ac6!important;
     background: #187ac6!important
 }
 
 .u-bg-dribbble {
     border-color: #ea4c89!important;
     background: #ea4c89!important
 }
 
 .u-bg-linkedin {
     border-color: #222;
     background-color: #222!important
 }
 
 .u-block {
     display: block!important
 }
 
 .u-inline-block {
     display: inline-block!important
 }
 
 .u-inline {
     display: inline!important
 }
 
 .u-border-top {
     border-top: 1px solid #e6eaee!important
 }
 
 .u-border-right {
     border-right: 1px solid #e6eaee!important
 }
 
 .u-border-bottom {
     border-bottom: 1px solid #e6eaee!important
 }
 
 .u-border-left {
     border-left: 1px solid #e6eaee!important
 }
 
 .u-border-zero {
     border: 0!important
 }
 
 .u-border-top-zero {
     border-top: 0!important
 }
 
 .u-border-right-zero {
     border-right: 0!important
 }
 
 .u-border-bottom-zero {
     border-bottom: 0!important
 }
 
 .u-border-left-zero {
     border-left: 0!important
 }
 
 .u-border-rounded {
     border-radius: 4px;
     overflow: hidden
 }
 
 .u-border-circle {
     border-radius: 100%;
     overflow: hidden
 }
 
 .u-flex {
     display: -webkit-box!important;
     display: -ms-flexbox!important;
     display: flex!important
 }
 
 .u-inline-flex {
     display: -webkit-inline-box!important;
     display: -ms-inline-flexbox!important;
     display: inline-flex!important
 }
 
 .u-justify-start {
     -webkit-box-pack: start!important;
     -ms-flex-pack: start!important;
     justify-content: flex-start!important
 }
 
 .u-justify-end {
     -webkit-box-pack: end!important;
     -ms-flex-pack: end!important;
     justify-content: flex-end!important
 }
 
 .u-justify-center {
     -webkit-box-pack: center!important;
     -ms-flex-pack: center!important;
     justify-content: center!important
 }
 
 .u-justify-between {
     -webkit-box-pack: justify!important;
     -ms-flex-pack: justify!important;
     justify-content: space-between!important
 }
 
 .u-justify-around {
     -ms-flex-pack: distribute!important;
     justify-content: space-around!important
 }
 
 .u-align-items-start {
     -webkit-box-align: start!important;
     -ms-flex-align: start!important;
     align-items: flex-start!important
 }
 
 .u-align-items-end {
     -webkit-box-align: end!important;
     -ms-flex-align: end!important;
     align-items: flex-end!important
 }
 
 .u-align-items-center {
     -webkit-box-align: center!important;
     -ms-flex-align: center!important;
     align-items: center!important
 }
 
 .u-align-items-baseline {
     -webkit-box-align: baseline!important;
     -ms-flex-align: baseline!important;
     align-items: baseline!important
 }
 
 .u-align-items-stretch {
     -webkit-box-align: stretch!important;
     -ms-flex-align: stretch!important;
     align-items: stretch!important
 }
 
 .u-align-self-start {
     -ms-flex-item-align: start!important;
     align-self: flex-start!important
 }
 
 .u-align-self-end {
     -ms-flex-item-align: end!important;
     align-self: flex-end!important
 }
 
 .u-align-self-center {
     -ms-flex-item-align: center!important;
     align-self: center!important
 }
 
 .u-align-self-baseline {
     -ms-flex-item-align: baseline!important;
     align-self: baseline!important
 }
 
 .u-align-self-stretch {
     -ms-flex-item-align: stretch!important;
     align-self: stretch!important
 }
 
 .u-flex-wrap {
     -ms-flex-wrap: wrap!important;
     flex-wrap: wrap!important
 }
 
 .u-flex-nowrap {
     -ms-flex-wrap: nowrap!important;
     flex-wrap: nowrap!important
 }
 
 .u-hidden {
     display: none!important
 }
 
 .u-hidden-visually {
     position: absolute!important;
     width: 1px!important;
     height: 1px!important;
     margin: -1px!important;
     padding: 0!important;
     border: 0!important;
     white-space: nowrap!important;
     overflow: hidden!important;
     clip: rect(0 0 0 0)!important;
     -webkit-clip-path: inset(50%)!important;
     clip-path: inset(50%)!important
 }
 
 @media (max-width:576px) {
     .u-hidden-down\@mobile {
         display: none!important
     }
 }
 
 @media (max-width:768px) {
     .u-hidden-down\@tablet {
         display: none!important
     }
 }
 
 @media (max-width:992px) {
     .u-hidden-down\@desktop {
         display: none!important
     }
 }
 
 @media (max-width:1200px) {
     .u-hidden-down\@wide {
         display: none!important
     }
 }
 
 @media (min-width:576px) {
     .u-hidden-up\@mobile {
         display: none!important
     }
 }
 
 @media (min-width:768px) {
     .u-hidden-up\@tablet {
         display: none!important
     }
 }
 
 @media (min-width:992px) {
     .u-hidden-up\@desktop {
         display: none!important
     }
 }
 
 @media (min-width:1200px) {
     .u-hidden-up\@wide {
         display: none!important
     }
 }
 
 .u-opacity-heavy {
     opacity: .75!important
 }
 
 .u-opacity-medium {
     opacity: .5!important
 }
 
 .u-opacity-light {
     opacity: .25!important
 }
 
 .u-opacity-zero {
     opacity: 0
 }
 
 .u-width-25 {
     width: 25%!important
 }
 
 .u-width-50 {
     width: 50%!important
 }
 
 .u-width-75 {
     width: 75%!important
 }
 
 .u-width-100 {
     width: 100%!important
 }
 
 .u-height-25 {
     height: 25%!important
 }
 
 .u-height-50 {
     height: 50%!important
 }
 
 .u-height-75 {
     height: 75%!important
 }
 
 .u-height-100 {
     height: 100%!important
 }
 
 .u-overflow-auto {
     overflow: auto
 }
 
 .u-overflow-scroll {
     overflow: scroll
 }
 
 .u-overflow-x-auto {
     overflow: auto
 }
 
 .u-overflow-x-scroll {
     overflow: scroll
 }
 
 .u-overflow-y-auto {
     overflow: auto
 }
 
 .u-overflow-y-scroll {
     overflow: scroll
 }
 
 .fade {
     transition: opacity .3s;
     opacity: 0
 }
 
 .fade.show {
     opacity: 1
 }