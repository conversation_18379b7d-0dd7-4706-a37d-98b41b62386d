@charset "UTF-8";
/*!
 * Dashboard UI Kit - v2.1
 * Copyright 2018 ZawiaStudio
 * @zawiastudio | zawiastudio.com 
 */
/* ==========================================================================
   #BASE GLOBAL
   ========================================================================== */
/**
 * Resets, unclassed (bare) elements.
 *
 * 1. Ensure the page always fills at least the entire height of the viewport.
 *
 * 2. Set the default `font-size` and `line-height` for the entire project,
 *    sourced from our default variables. The `font-size` is calculated to exist
 *    in ems, the `line-height` is calculated to exist unitlessly.
 *
 * 3. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 */
html {
    height: 100%;
    /* [1] */
    min-height: 100%;
    /* [1] */
    font-family: "Source Sans Pro", sans-serif;
    font-size: 16px;
    /* [2] */
    line-height: 1.5;
    /* [2] */
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    /* [3] */ }

body {
    height: 100%;
    /* [1] */
    min-height: 100%;
    /* [1] */
    margin: 0;
    background-color: #EFF3F6; }

button,
input,
optgroup,
select,
textarea {
    font-family: "Source Sans Pro", sans-serif; }

/**
 * Simple reset
 */
body,
h1, h2, h3, h4, h5, h6,
blockquote, p, pre,
dl, dd, ol, ul,
figure,
hr,
fieldset, legend {
    margin: 0;
    padding: 0; }

article,
aside,
footer,
header,
nav,
section,
figcaption,
figure,
main {
    display: block; }

button {
    cursor: pointer; }

ul,
ol {
    margin: 0;
    padding: 0;
    list-style-type: none; }

/**
 * Remove trailing margins from nested lists.
 */
li > ol,
li > ul {
    margin-bottom: 0; }

img {
    display: inline-block;
    max-width: 100%;
    height: auto;
    border-style: none; }

textarea {
    display: block;
    margin: 0;
    outline: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none; }

/* ==========================================================================
   #BASE TYPOGRAPHY
   ========================================================================== */
/**
 * Headings, paragraphs, links.
 */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 0.5rem;
    color: #354052;
    font-weight: 500; }

h1 {
    font-size: 2.25rem; }

h2 {
    font-size: 1.75rem; }

h3 {
    font-size: 1.5rem; }

h4 {
    font-size: 1.25rem; }

h5 {
    font-size: 1.125rem; }

h6 {
    font-size: 1rem; }

p {
    color: #354052;
    font-size: 0.875rem; }

strong {
    color: #354052; }

dl dt,
dl dd {
    color: #354052; }

/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
    -webkit-transition: color 0.15s ease-in-out;
    transition: color 0.15s ease-in-out;
    background-color: transparent;
    /* [1] */
    color: #2ea1f8;
    text-decoration: none;
    cursor: pointer;
    -webkit-text-decoration-skip: objects;
    text-decoration-skip: objects;
    /* [2] */ }
a:hover {
    color: #2ea1f8;
    text-decoration: underline; }

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre,
code,
kbd,
samp {
    font-family: monospace, monospace;
    /* [1] */
    font-size: 1em;
    /* [2] */ }

mark {
    background-color: #ff0;
    color: #fff; }

small {
    font-size: 85%; }

blockquote {
    padding-left: 15px;
    border-left: 3px solid #e6eaee; }

/*!
 * Bootstrap Grid v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
@-ms-viewport {
    width: device-width; }

html {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-overflow-style: scrollbar; }

*,
*::before,
*::after {
    -webkit-box-sizing: inherit;
    box-sizing: inherit; }

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto; }

@media (min-width: 576px) {
    .container {
        max-width: 540px; } }

@media (min-width: 768px) {
    .container {
        max-width: 720px; } }

@media (min-width: 992px) {
    .container {
        max-width: 960px; } }

@media (min-width: 1200px) {
    .container {
        max-width: 1140px; } }

.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto; }

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px; }

.no-gutters {
    margin-right: 0;
    margin-left: 0; }

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0; }

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,
.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,
.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,
.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,
.col-xl-auto {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px; }

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%; }

.col-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none; }

.col-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%; }

.col-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%; }

.col-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%; }

.col-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%; }

.col-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%; }

.col-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%; }

.col-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%; }

.col-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%; }

.col-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%; }

.col-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%; }

.col-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%; }

.col-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%; }

.order-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1; }

.order-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13; }

.order-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0; }

.order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1; }

.order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2; }

.order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3; }

.order-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4; }

.order-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5; }

.order-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6; }

.order-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7; }

.order-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8; }

.order-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9; }

.order-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10; }

.order-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11; }

.order-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12; }

.offset-1 {
    margin-left: 8.333333%; }

.offset-2 {
    margin-left: 16.666667%; }

.offset-3 {
    margin-left: 25%; }

.offset-4 {
    margin-left: 33.333333%; }

.offset-5 {
    margin-left: 41.666667%; }

.offset-6 {
    margin-left: 50%; }

.offset-7 {
    margin-left: 58.333333%; }

.offset-8 {
    margin-left: 66.666667%; }

.offset-9 {
    margin-left: 75%; }

.offset-10 {
    margin-left: 83.333333%; }

.offset-11 {
    margin-left: 91.666667%; }

@media (min-width: 576px) {
    .col-sm {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%; }
    .col-sm-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none; }
    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%; }
    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%; }
    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%; }
    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%; }
    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%; }
    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%; }
    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%; }
    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%; }
    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%; }
    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%; }
    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%; }
    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%; }
    .order-sm-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1; }
    .order-sm-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13; }
    .order-sm-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0; }
    .order-sm-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1; }
    .order-sm-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2; }
    .order-sm-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3; }
    .order-sm-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4; }
    .order-sm-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5; }
    .order-sm-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6; }
    .order-sm-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7; }
    .order-sm-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8; }
    .order-sm-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9; }
    .order-sm-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10; }
    .order-sm-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11; }
    .order-sm-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12; }
    .offset-sm-0 {
        margin-left: 0; }
    .offset-sm-1 {
        margin-left: 8.333333%; }
    .offset-sm-2 {
        margin-left: 16.666667%; }
    .offset-sm-3 {
        margin-left: 25%; }
    .offset-sm-4 {
        margin-left: 33.333333%; }
    .offset-sm-5 {
        margin-left: 41.666667%; }
    .offset-sm-6 {
        margin-left: 50%; }
    .offset-sm-7 {
        margin-left: 58.333333%; }
    .offset-sm-8 {
        margin-left: 66.666667%; }
    .offset-sm-9 {
        margin-left: 75%; }
    .offset-sm-10 {
        margin-left: 83.333333%; }
    .offset-sm-11 {
        margin-left: 91.666667%; } }

@media (min-width: 768px) {
    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%; }
    .col-md-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none; }
    .col-md-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%; }
    .col-md-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%; }
    .col-md-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%; }
    .col-md-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%; }
    .col-md-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%; }
    .col-md-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%; }
    .col-md-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%; }
    .col-md-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%; }
    .col-md-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%; }
    .col-md-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%; }
    .col-md-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%; }
    .col-md-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%; }
    .order-md-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1; }
    .order-md-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13; }
    .order-md-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0; }
    .order-md-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1; }
    .order-md-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2; }
    .order-md-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3; }
    .order-md-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4; }
    .order-md-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5; }
    .order-md-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6; }
    .order-md-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7; }
    .order-md-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8; }
    .order-md-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9; }
    .order-md-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10; }
    .order-md-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11; }
    .order-md-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12; }
    .offset-md-0 {
        margin-left: 0; }
    .offset-md-1 {
        margin-left: 8.333333%; }
    .offset-md-2 {
        margin-left: 16.666667%; }
    .offset-md-3 {
        margin-left: 25%; }
    .offset-md-4 {
        margin-left: 33.333333%; }
    .offset-md-5 {
        margin-left: 41.666667%; }
    .offset-md-6 {
        margin-left: 50%; }
    .offset-md-7 {
        margin-left: 58.333333%; }
    .offset-md-8 {
        margin-left: 66.666667%; }
    .offset-md-9 {
        margin-left: 75%; }
    .offset-md-10 {
        margin-left: 83.333333%; }
    .offset-md-11 {
        margin-left: 91.666667%; } }

@media (min-width: 992px) {
    .col-lg {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%; }
    .col-lg-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none; }
    .col-lg-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%; }
    .col-lg-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%; }
    .col-lg-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%; }
    .col-lg-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%; }
    .col-lg-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%; }
    .col-lg-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%; }
    .col-lg-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%; }
    .col-lg-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%; }
    .col-lg-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%; }
    .col-lg-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%; }
    .col-lg-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%; }
    .col-lg-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%; }
    .order-lg-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1; }
    .order-lg-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13; }
    .order-lg-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0; }
    .order-lg-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1; }
    .order-lg-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2; }
    .order-lg-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3; }
    .order-lg-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4; }
    .order-lg-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5; }
    .order-lg-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6; }
    .order-lg-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7; }
    .order-lg-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8; }
    .order-lg-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9; }
    .order-lg-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10; }
    .order-lg-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11; }
    .order-lg-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12; }
    .offset-lg-0 {
        margin-left: 0; }
    .offset-lg-1 {
        margin-left: 8.333333%; }
    .offset-lg-2 {
        margin-left: 16.666667%; }
    .offset-lg-3 {
        margin-left: 25%; }
    .offset-lg-4 {
        margin-left: 33.333333%; }
    .offset-lg-5 {
        margin-left: 41.666667%; }
    .offset-lg-6 {
        margin-left: 50%; }
    .offset-lg-7 {
        margin-left: 58.333333%; }
    .offset-lg-8 {
        margin-left: 66.666667%; }
    .offset-lg-9 {
        margin-left: 75%; }
    .offset-lg-10 {
        margin-left: 83.333333%; }
    .offset-lg-11 {
        margin-left: 91.666667%; } }

@media (min-width: 1200px) {
    .col-xl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%; }
    .col-xl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none; }
    .col-xl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%; }
    .col-xl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%; }
    .col-xl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%; }
    .col-xl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%; }
    .col-xl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%; }
    .col-xl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%; }
    .col-xl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%; }
    .col-xl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%; }
    .col-xl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%; }
    .col-xl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%; }
    .col-xl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%; }
    .col-xl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%; }
    .order-xl-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1; }
    .order-xl-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13; }
    .order-xl-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0; }
    .order-xl-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1; }
    .order-xl-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2; }
    .order-xl-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3; }
    .order-xl-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4; }
    .order-xl-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5; }
    .order-xl-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6; }
    .order-xl-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7; }
    .order-xl-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8; }
    .order-xl-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9; }
    .order-xl-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10; }
    .order-xl-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11; }
    .order-xl-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12; }
    .offset-xl-0 {
        margin-left: 0; }
    .offset-xl-1 {
        margin-left: 8.333333%; }
    .offset-xl-2 {
        margin-left: 16.666667%; }
    .offset-xl-3 {
        margin-left: 25%; }
    .offset-xl-4 {
        margin-left: 33.333333%; }
    .offset-xl-5 {
        margin-left: 41.666667%; }
    .offset-xl-6 {
        margin-left: 50%; }
    .offset-xl-7 {
        margin-left: 58.333333%; }
    .offset-xl-8 {
        margin-left: 66.666667%; }
    .offset-xl-9 {
        margin-left: 75%; }
    .offset-xl-10 {
        margin-left: 83.333333%; }
    .offset-xl-11 {
        margin-left: 91.666667%; } }

.d-none {
    display: none !important; }

.d-inline {
    display: inline !important; }

.d-inline-block {
    display: inline-block !important; }

.d-block {
    display: block !important; }

.d-table {
    display: table !important; }

.d-table-row {
    display: table-row !important; }

.d-table-cell {
    display: table-cell !important; }

.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important; }

.d-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important; }

@media (min-width: 576px) {
    .d-sm-none {
        display: none !important; }
    .d-sm-inline {
        display: inline !important; }
    .d-sm-inline-block {
        display: inline-block !important; }
    .d-sm-block {
        display: block !important; }
    .d-sm-table {
        display: table !important; }
    .d-sm-table-row {
        display: table-row !important; }
    .d-sm-table-cell {
        display: table-cell !important; }
    .d-sm-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important; }
    .d-sm-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important; } }

@media (min-width: 768px) {
    .d-md-none {
        display: none !important; }
    .d-md-inline {
        display: inline !important; }
    .d-md-inline-block {
        display: inline-block !important; }
    .d-md-block {
        display: block !important; }
    .d-md-table {
        display: table !important; }
    .d-md-table-row {
        display: table-row !important; }
    .d-md-table-cell {
        display: table-cell !important; }
    .d-md-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important; }
    .d-md-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important; } }

@media (min-width: 992px) {
    .d-lg-none {
        display: none !important; }
    .d-lg-inline {
        display: inline !important; }
    .d-lg-inline-block {
        display: inline-block !important; }
    .d-lg-block {
        display: block !important; }
    .d-lg-table {
        display: table !important; }
    .d-lg-table-row {
        display: table-row !important; }
    .d-lg-table-cell {
        display: table-cell !important; }
    .d-lg-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important; }
    .d-lg-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important; } }

@media (min-width: 1200px) {
    .d-xl-none {
        display: none !important; }
    .d-xl-inline {
        display: inline !important; }
    .d-xl-inline-block {
        display: inline-block !important; }
    .d-xl-block {
        display: block !important; }
    .d-xl-table {
        display: table !important; }
    .d-xl-table-row {
        display: table-row !important; }
    .d-xl-table-cell {
        display: table-cell !important; }
    .d-xl-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important; }
    .d-xl-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important; } }

@media print {
    .d-print-none {
        display: none !important; }
    .d-print-inline {
        display: inline !important; }
    .d-print-inline-block {
        display: inline-block !important; }
    .d-print-block {
        display: block !important; }
    .d-print-table {
        display: table !important; }
    .d-print-table-row {
        display: table-row !important; }
    .d-print-table-cell {
        display: table-cell !important; }
    .d-print-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important; }
    .d-print-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important; } }

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important; }

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important; }

.flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important; }

.flex-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important; }

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important; }

.flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important; }

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important; }

.justify-content-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important; }

.justify-content-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important; }

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important; }

.justify-content-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important; }

.justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important; }

.align-items-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important; }

.align-items-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important; }

.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important; }

.align-items-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important; }

.align-items-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important; }

.align-content-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important; }

.align-content-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important; }

.align-content-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important; }

.align-content-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important; }

.align-content-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important; }

.align-content-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important; }

.align-self-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important; }

.align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important; }

.align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important; }

.align-self-center {
    -ms-flex-item-align: center !important;
    align-self: center !important; }

.align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important; }

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important; }

@media (min-width: 576px) {
    .flex-sm-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important; }
    .flex-sm-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important; }
    .flex-sm-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important; }
    .flex-sm-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important; }
    .flex-sm-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
    .justify-content-sm-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important; }
    .justify-content-sm-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important; }
    .justify-content-sm-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important; }
    .justify-content-sm-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important; }
    .justify-content-sm-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
    .align-items-sm-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important; }
    .align-items-sm-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important; }
    .align-items-sm-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important; }
    .align-items-sm-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important; }
    .align-items-sm-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important; }
    .align-content-sm-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
    .align-content-sm-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
    .align-content-sm-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important; }
    .align-content-sm-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
    .align-content-sm-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
    .align-self-sm-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important; }
    .align-self-sm-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
    .align-self-sm-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
    .align-self-sm-center {
        -ms-flex-item-align: center !important;
        align-self: center !important; }
    .align-self-sm-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
    .align-self-sm-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 768px) {
    .flex-md-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important; }
    .flex-md-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important; }
    .flex-md-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important; }
    .flex-md-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important; }
    .flex-md-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
    .flex-md-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
    .justify-content-md-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important; }
    .justify-content-md-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important; }
    .justify-content-md-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important; }
    .justify-content-md-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important; }
    .justify-content-md-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
    .align-items-md-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important; }
    .align-items-md-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important; }
    .align-items-md-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important; }
    .align-items-md-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important; }
    .align-items-md-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important; }
    .align-content-md-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
    .align-content-md-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
    .align-content-md-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important; }
    .align-content-md-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
    .align-content-md-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
    .align-content-md-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
    .align-self-md-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important; }
    .align-self-md-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
    .align-self-md-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
    .align-self-md-center {
        -ms-flex-item-align: center !important;
        align-self: center !important; }
    .align-self-md-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
    .align-self-md-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 992px) {
    .flex-lg-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important; }
    .flex-lg-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important; }
    .flex-lg-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important; }
    .flex-lg-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important; }
    .flex-lg-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
    .justify-content-lg-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important; }
    .justify-content-lg-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important; }
    .justify-content-lg-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important; }
    .justify-content-lg-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important; }
    .justify-content-lg-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
    .align-items-lg-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important; }
    .align-items-lg-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important; }
    .align-items-lg-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important; }
    .align-items-lg-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important; }
    .align-items-lg-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important; }
    .align-content-lg-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
    .align-content-lg-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
    .align-content-lg-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important; }
    .align-content-lg-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
    .align-content-lg-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
    .align-self-lg-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important; }
    .align-self-lg-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
    .align-self-lg-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
    .align-self-lg-center {
        -ms-flex-item-align: center !important;
        align-self: center !important; }
    .align-self-lg-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
    .align-self-lg-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 1200px) {
    .flex-xl-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important; }
    .flex-xl-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important; }
    .flex-xl-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important; }
    .flex-xl-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important; }
    .flex-xl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
    .justify-content-xl-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important; }
    .justify-content-xl-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important; }
    .justify-content-xl-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important; }
    .justify-content-xl-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important; }
    .justify-content-xl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
    .align-items-xl-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important; }
    .align-items-xl-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important; }
    .align-items-xl-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important; }
    .align-items-xl-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important; }
    .align-items-xl-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important; }
    .align-content-xl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
    .align-content-xl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
    .align-content-xl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important; }
    .align-content-xl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
    .align-content-xl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
    .align-self-xl-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important; }
    .align-self-xl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
    .align-self-xl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
    .align-self-xl-center {
        -ms-flex-item-align: center !important;
        align-self: center !important; }
    .align-self-xl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
    .align-self-xl-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

/*# sourceMappingURL=bootstrap-grid.css.map */
/**
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
    font-family: 'FontAwesome';
    src: url("../fonts/fontawesome-webfont.eot?v=4.7.0");
    src: url("../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("../fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("../fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("../fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
    font-weight: normal;
    font-style: normal; }

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale; }

/* makes the font 33% larger relative to the icon container */
.fa-lg {
    font-size: 1.33333333em;
    line-height: 0.75em;
    vertical-align: -15%; }

.fa-2x {
    font-size: 2em; }

.fa-3x {
    font-size: 3em; }

.fa-4x {
    font-size: 4em; }

.fa-5x {
    font-size: 5em; }

.fa-fw {
    width: 1.28571429em;
    text-align: center; }

.fa-ul {
    padding-left: 0;
    margin-left: 2.14285714em;
    list-style-type: none; }

.fa-ul > li {
    position: relative; }

.fa-li {
    position: absolute;
    left: -2.14285714em;
    width: 2.14285714em;
    top: 0.14285714em;
    text-align: center; }

.fa-li.fa-lg {
    left: -1.85714286em; }

.fa-border {
    padding: .2em .25em .15em;
    border: solid 0.08em #eeeeee;
    border-radius: .1em; }

.fa-pull-left {
    float: left; }

.fa-pull-right {
    float: right; }

.fa.fa-pull-left {
    margin-right: .3em; }

.fa.fa-pull-right {
    margin-left: .3em; }

/* Deprecated as of 4.4.0 */
.pull-right {
    float: right; }

.pull-left {
    float: left; }

.fa.pull-left {
    margin-right: .3em; }

.fa.pull-right {
    margin-left: .3em; }

.fa-spin {
    -webkit-animation: fa-spin 2s infinite linear;
    animation: fa-spin 2s infinite linear; }

.fa-pulse {
    -webkit-animation: fa-spin 1s infinite steps(8);
    animation: fa-spin 1s infinite steps(8); }

@-webkit-keyframes fa-spin {
    0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }

@keyframes fa-spin {
    0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }

.fa-rotate-90 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg); }

.fa-rotate-180 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }

.fa-rotate-270 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg); }

.fa-flip-horizontal {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
    -webkit-transform: scale(-1, 1);
    transform: scale(-1, 1); }

.fa-flip-vertical {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
    -webkit-transform: scale(1, -1);
    transform: scale(1, -1); }

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
    -webkit-filter: none;
    filter: none; }

.fa-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle; }

.fa-stack-1x,
.fa-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center; }

.fa-stack-1x {
    line-height: inherit; }

.fa-stack-2x {
    font-size: 2em; }

.fa-inverse {
    color: #ffffff; }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
    content: "\f000"; }

.fa-music:before {
    content: "\f001"; }

.fa-search:before {
    content: "\f002"; }

.fa-envelope-o:before {
    content: "\f003"; }

.fa-heart:before {
    content: "\f004"; }

.fa-star:before {
    content: "\f005"; }

.fa-star-o:before {
    content: "\f006"; }

.fa-user:before {
    content: "\f007"; }

.fa-film:before {
    content: "\f008"; }

.fa-th-large:before {
    content: "\f009"; }

.fa-th:before {
    content: "\f00a"; }

.fa-th-list:before {
    content: "\f00b"; }

.fa-check:before {
    content: "\f00c"; }

.fa-remove:before,
.fa-close:before,
.fa-times:before {
    content: "\f00d"; }

.fa-search-plus:before {
    content: "\f00e"; }

.fa-search-minus:before {
    content: "\f010"; }

.fa-power-off:before {
    content: "\f011"; }

.fa-signal:before {
    content: "\f012"; }

.fa-gear:before,
.fa-cog:before {
    content: "\f013"; }

.fa-trash-o:before {
    content: "\f014"; }

.fa-home:before {
    content: "\f015"; }

.fa-file-o:before {
    content: "\f016"; }

.fa-clock-o:before {
    content: "\f017"; }

.fa-road:before {
    content: "\f018"; }

.fa-download:before {
    content: "\f019"; }

.fa-arrow-circle-o-down:before {
    content: "\f01a"; }

.fa-arrow-circle-o-up:before {
    content: "\f01b"; }

.fa-inbox:before {
    content: "\f01c"; }

.fa-play-circle-o:before {
    content: "\f01d"; }

.fa-rotate-right:before,
.fa-repeat:before {
    content: "\f01e"; }

.fa-refresh:before {
    content: "\f021"; }

.fa-list-alt:before {
    content: "\f022"; }

.fa-lock:before {
    content: "\f023"; }

.fa-flag:before {
    content: "\f024"; }

.fa-headphones:before {
    content: "\f025"; }

.fa-volume-off:before {
    content: "\f026"; }

.fa-volume-down:before {
    content: "\f027"; }

.fa-volume-up:before {
    content: "\f028"; }

.fa-qrcode:before {
    content: "\f029"; }

.fa-barcode:before {
    content: "\f02a"; }

.fa-tag:before {
    content: "\f02b"; }

.fa-tags:before {
    content: "\f02c"; }

.fa-book:before {
    content: "\f02d"; }

.fa-bookmark:before {
    content: "\f02e"; }

.fa-print:before {
    content: "\f02f"; }

.fa-camera:before {
    content: "\f030"; }

.fa-font:before {
    content: "\f031"; }

.fa-bold:before {
    content: "\f032"; }

.fa-italic:before {
    content: "\f033"; }

.fa-text-height:before {
    content: "\f034"; }

.fa-text-width:before {
    content: "\f035"; }

.fa-align-left:before {
    content: "\f036"; }

.fa-align-center:before {
    content: "\f037"; }

.fa-align-right:before {
    content: "\f038"; }

.fa-align-justify:before {
    content: "\f039"; }

.fa-list:before {
    content: "\f03a"; }

.fa-dedent:before,
.fa-outdent:before {
    content: "\f03b"; }

.fa-indent:before {
    content: "\f03c"; }

.fa-video-camera:before {
    content: "\f03d"; }

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
    content: "\f03e"; }

.fa-pencil:before {
    content: "\f040"; }

.fa-map-marker:before {
    content: "\f041"; }

.fa-adjust:before {
    content: "\f042"; }

.fa-tint:before {
    content: "\f043"; }

.fa-edit:before,
.fa-pencil-square-o:before {
    content: "\f044"; }

.fa-share-square-o:before {
    content: "\f045"; }

.fa-check-square-o:before {
    content: "\f046"; }

.fa-arrows:before {
    content: "\f047"; }

.fa-step-backward:before {
    content: "\f048"; }

.fa-fast-backward:before {
    content: "\f049"; }

.fa-backward:before {
    content: "\f04a"; }

.fa-play:before {
    content: "\f04b"; }

.fa-pause:before {
    content: "\f04c"; }

.fa-stop:before {
    content: "\f04d"; }

.fa-forward:before {
    content: "\f04e"; }

.fa-fast-forward:before {
    content: "\f050"; }

.fa-step-forward:before {
    content: "\f051"; }

.fa-eject:before {
    content: "\f052"; }

.fa-chevron-left:before {
    content: "\f053"; }

.fa-chevron-right:before {
    content: "\f054"; }

.fa-plus-circle:before {
    content: "\f055"; }

.fa-minus-circle:before {
    content: "\f056"; }

.fa-times-circle:before {
    content: "\f057"; }

.fa-check-circle:before {
    content: "\f058"; }

.fa-question-circle:before {
    content: "\f059"; }

.fa-info-circle:before {
    content: "\f05a"; }

.fa-crosshairs:before {
    content: "\f05b"; }

.fa-times-circle-o:before {
    content: "\f05c"; }

.fa-check-circle-o:before {
    content: "\f05d"; }

.fa-ban:before {
    content: "\f05e"; }

.fa-arrow-left:before {
    content: "\f060"; }

.fa-arrow-right:before {
    content: "\f061"; }

.fa-arrow-up:before {
    content: "\f062"; }

.fa-arrow-down:before {
    content: "\f063"; }

.fa-mail-forward:before,
.fa-share:before {
    content: "\f064"; }

.fa-expand:before {
    content: "\f065"; }

.fa-compress:before {
    content: "\f066"; }

.fa-plus:before {
    content: "\f067"; }

.fa-minus:before {
    content: "\f068"; }

.fa-asterisk:before {
    content: "\f069"; }

.fa-exclamation-circle:before {
    content: "\f06a"; }

.fa-gift:before {
    content: "\f06b"; }

.fa-leaf:before {
    content: "\f06c"; }

.fa-fire:before {
    content: "\f06d"; }

.fa-eye:before {
    content: "\f06e"; }

.fa-eye-slash:before {
    content: "\f070"; }

.fa-warning:before,
.fa-exclamation-triangle:before {
    content: "\f071"; }

.fa-plane:before {
    content: "\f072"; }

.fa-calendar:before {
    content: "\f073"; }

.fa-random:before {
    content: "\f074"; }

.fa-comment:before {
    content: "\f075"; }

.fa-magnet:before {
    content: "\f076"; }

.fa-chevron-up:before {
    content: "\f077"; }

.fa-chevron-down:before {
    content: "\f078"; }

.fa-retweet:before {
    content: "\f079"; }

.fa-shopping-cart:before {
    content: "\f07a"; }

.fa-folder:before {
    content: "\f07b"; }

.fa-folder-open:before {
    content: "\f07c"; }

.fa-arrows-v:before {
    content: "\f07d"; }

.fa-arrows-h:before {
    content: "\f07e"; }

.fa-bar-chart-o:before,
.fa-bar-chart:before {
    content: "\f080"; }

.fa-twitter-square:before {
    content: "\f081"; }

.fa-facebook-square:before {
    content: "\f082"; }

.fa-camera-retro:before {
    content: "\f083"; }

.fa-key:before {
    content: "\f084"; }

.fa-gears:before,
.fa-cogs:before {
    content: "\f085"; }

.fa-comments:before {
    content: "\f086"; }

.fa-thumbs-o-up:before {
    content: "\f087"; }

.fa-thumbs-o-down:before {
    content: "\f088"; }

.fa-star-half:before {
    content: "\f089"; }

.fa-heart-o:before {
    content: "\f08a"; }

.fa-sign-out:before {
    content: "\f08b"; }

.fa-linkedin-square:before {
    content: "\f08c"; }

.fa-thumb-tack:before {
    content: "\f08d"; }

.fa-external-link:before {
    content: "\f08e"; }

.fa-sign-in:before {
    content: "\f090"; }

.fa-trophy:before {
    content: "\f091"; }

.fa-github-square:before {
    content: "\f092"; }

.fa-upload:before {
    content: "\f093"; }

.fa-lemon-o:before {
    content: "\f094"; }

.fa-phone:before {
    content: "\f095"; }

.fa-square-o:before {
    content: "\f096"; }

.fa-bookmark-o:before {
    content: "\f097"; }

.fa-phone-square:before {
    content: "\f098"; }

.fa-twitter:before {
    content: "\f099"; }

.fa-facebook-f:before,
.fa-facebook:before {
    content: "\f09a"; }

.fa-github:before {
    content: "\f09b"; }

.fa-unlock:before {
    content: "\f09c"; }

.fa-credit-card:before {
    content: "\f09d"; }

.fa-feed:before,
.fa-rss:before {
    content: "\f09e"; }

.fa-hdd-o:before {
    content: "\f0a0"; }

.fa-bullhorn:before {
    content: "\f0a1"; }

.fa-bell:before {
    content: "\f0f3"; }

.fa-certificate:before {
    content: "\f0a3"; }

.fa-hand-o-right:before {
    content: "\f0a4"; }

.fa-hand-o-left:before {
    content: "\f0a5"; }

.fa-hand-o-up:before {
    content: "\f0a6"; }

.fa-hand-o-down:before {
    content: "\f0a7"; }

.fa-arrow-circle-left:before {
    content: "\f0a8"; }

.fa-arrow-circle-right:before {
    content: "\f0a9"; }

.fa-arrow-circle-up:before {
    content: "\f0aa"; }

.fa-arrow-circle-down:before {
    content: "\f0ab"; }

.fa-globe:before {
    content: "\f0ac"; }

.fa-wrench:before {
    content: "\f0ad"; }

.fa-tasks:before {
    content: "\f0ae"; }

.fa-filter:before {
    content: "\f0b0"; }

.fa-briefcase:before {
    content: "\f0b1"; }

.fa-arrows-alt:before {
    content: "\f0b2"; }

.fa-group:before,
.fa-users:before {
    content: "\f0c0"; }

.fa-chain:before,
.fa-link:before {
    content: "\f0c1"; }

.fa-cloud:before {
    content: "\f0c2"; }

.fa-flask:before {
    content: "\f0c3"; }

.fa-cut:before,
.fa-scissors:before {
    content: "\f0c4"; }

.fa-copy:before,
.fa-files-o:before {
    content: "\f0c5"; }

.fa-paperclip:before {
    content: "\f0c6"; }

.fa-save:before,
.fa-floppy-o:before {
    content: "\f0c7"; }

.fa-square:before {
    content: "\f0c8"; }

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
    content: "\f0c9"; }

.fa-list-ul:before {
    content: "\f0ca"; }

.fa-list-ol:before {
    content: "\f0cb"; }

.fa-strikethrough:before {
    content: "\f0cc"; }

.fa-underline:before {
    content: "\f0cd"; }

.fa-table:before {
    content: "\f0ce"; }

.fa-magic:before {
    content: "\f0d0"; }

.fa-truck:before {
    content: "\f0d1"; }

.fa-pinterest:before {
    content: "\f0d2"; }

.fa-pinterest-square:before {
    content: "\f0d3"; }

.fa-google-plus-square:before {
    content: "\f0d4"; }

.fa-google-plus:before {
    content: "\f0d5"; }

.fa-money:before {
    content: "\f0d6"; }

.fa-caret-down:before {
    content: "\f0d7"; }

.fa-caret-up:before {
    content: "\f0d8"; }

.fa-caret-left:before {
    content: "\f0d9"; }

.fa-caret-right:before {
    content: "\f0da"; }

.fa-columns:before {
    content: "\f0db"; }

.fa-unsorted:before,
.fa-sort:before {
    content: "\f0dc"; }

.fa-sort-down:before,
.fa-sort-desc:before {
    content: "\f0dd"; }

.fa-sort-up:before,
.fa-sort-asc:before {
    content: "\f0de"; }

.fa-envelope:before {
    content: "\f0e0"; }

.fa-linkedin:before {
    content: "\f0e1"; }

.fa-rotate-left:before,
.fa-undo:before {
    content: "\f0e2"; }

.fa-legal:before,
.fa-gavel:before {
    content: "\f0e3"; }

.fa-dashboard:before,
.fa-tachometer:before {
    content: "\f0e4"; }

.fa-comment-o:before {
    content: "\f0e5"; }

.fa-comments-o:before {
    content: "\f0e6"; }

.fa-flash:before,
.fa-bolt:before {
    content: "\f0e7"; }

.fa-sitemap:before {
    content: "\f0e8"; }

.fa-umbrella:before {
    content: "\f0e9"; }

.fa-paste:before,
.fa-clipboard:before {
    content: "\f0ea"; }

.fa-lightbulb-o:before {
    content: "\f0eb"; }

.fa-exchange:before {
    content: "\f0ec"; }

.fa-cloud-download:before {
    content: "\f0ed"; }

.fa-cloud-upload:before {
    content: "\f0ee"; }

.fa-user-md:before {
    content: "\f0f0"; }

.fa-stethoscope:before {
    content: "\f0f1"; }

.fa-suitcase:before {
    content: "\f0f2"; }

.fa-bell-o:before {
    content: "\f0a2"; }

.fa-coffee:before {
    content: "\f0f4"; }

.fa-cutlery:before {
    content: "\f0f5"; }

.fa-file-text-o:before {
    content: "\f0f6"; }

.fa-building-o:before {
    content: "\f0f7"; }

.fa-hospital-o:before {
    content: "\f0f8"; }

.fa-ambulance:before {
    content: "\f0f9"; }

.fa-medkit:before {
    content: "\f0fa"; }

.fa-fighter-jet:before {
    content: "\f0fb"; }

.fa-beer:before {
    content: "\f0fc"; }

.fa-h-square:before {
    content: "\f0fd"; }

.fa-plus-square:before {
    content: "\f0fe"; }

.fa-angle-double-left:before {
    content: "\f100"; }

.fa-angle-double-right:before {
    content: "\f101"; }

.fa-angle-double-up:before {
    content: "\f102"; }

.fa-angle-double-down:before {
    content: "\f103"; }

.fa-angle-left:before {
    content: "\f104"; }

.fa-angle-right:before {
    content: "\f105"; }

.fa-angle-up:before {
    content: "\f106"; }

.fa-angle-down:before {
    content: "\f107"; }

.fa-desktop:before {
    content: "\f108"; }

.fa-laptop:before {
    content: "\f109"; }

.fa-tablet:before {
    content: "\f10a"; }

.fa-mobile-phone:before,
.fa-mobile:before {
    content: "\f10b"; }

.fa-circle-o:before {
    content: "\f10c"; }

.fa-quote-left:before {
    content: "\f10d"; }

.fa-quote-right:before {
    content: "\f10e"; }

.fa-spinner:before {
    content: "\f110"; }

.fa-circle:before {
    content: "\f111"; }

.fa-mail-reply:before,
.fa-reply:before {
    content: "\f112"; }

.fa-github-alt:before {
    content: "\f113"; }

.fa-folder-o:before {
    content: "\f114"; }

.fa-folder-open-o:before {
    content: "\f115"; }

.fa-smile-o:before {
    content: "\f118"; }

.fa-frown-o:before {
    content: "\f119"; }

.fa-meh-o:before {
    content: "\f11a"; }

.fa-gamepad:before {
    content: "\f11b"; }

.fa-keyboard-o:before {
    content: "\f11c"; }

.fa-flag-o:before {
    content: "\f11d"; }

.fa-flag-checkered:before {
    content: "\f11e"; }

.fa-terminal:before {
    content: "\f120"; }

.fa-code:before {
    content: "\f121"; }

.fa-mail-reply-all:before,
.fa-reply-all:before {
    content: "\f122"; }

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
    content: "\f123"; }

.fa-location-arrow:before {
    content: "\f124"; }

.fa-crop:before {
    content: "\f125"; }

.fa-code-fork:before {
    content: "\f126"; }

.fa-unlink:before,
.fa-chain-broken:before {
    content: "\f127"; }

.fa-question:before {
    content: "\f128"; }

.fa-info:before {
    content: "\f129"; }

.fa-exclamation:before {
    content: "\f12a"; }

.fa-superscript:before {
    content: "\f12b"; }

.fa-subscript:before {
    content: "\f12c"; }

.fa-eraser:before {
    content: "\f12d"; }

.fa-puzzle-piece:before {
    content: "\f12e"; }

.fa-microphone:before {
    content: "\f130"; }

.fa-microphone-slash:before {
    content: "\f131"; }

.fa-shield:before {
    content: "\f132"; }

.fa-calendar-o:before {
    content: "\f133"; }

.fa-fire-extinguisher:before {
    content: "\f134"; }

.fa-rocket:before {
    content: "\f135"; }

.fa-maxcdn:before {
    content: "\f136"; }

.fa-chevron-circle-left:before {
    content: "\f137"; }

.fa-chevron-circle-right:before {
    content: "\f138"; }

.fa-chevron-circle-up:before {
    content: "\f139"; }

.fa-chevron-circle-down:before {
    content: "\f13a"; }

.fa-html5:before {
    content: "\f13b"; }

.fa-css3:before {
    content: "\f13c"; }

.fa-anchor:before {
    content: "\f13d"; }

.fa-unlock-alt:before {
    content: "\f13e"; }

.fa-bullseye:before {
    content: "\f140"; }

.fa-ellipsis-h:before {
    content: "\f141"; }

.fa-ellipsis-v:before {
    content: "\f142"; }

.fa-rss-square:before {
    content: "\f143"; }

.fa-play-circle:before {
    content: "\f144"; }

.fa-ticket:before {
    content: "\f145"; }

.fa-minus-square:before {
    content: "\f146"; }

.fa-minus-square-o:before {
    content: "\f147"; }

.fa-level-up:before {
    content: "\f148"; }

.fa-level-down:before {
    content: "\f149"; }

.fa-check-square:before {
    content: "\f14a"; }

.fa-pencil-square:before {
    content: "\f14b"; }

.fa-external-link-square:before {
    content: "\f14c"; }

.fa-share-square:before {
    content: "\f14d"; }

.fa-compass:before {
    content: "\f14e"; }

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
    content: "\f150"; }

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
    content: "\f151"; }

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
    content: "\f152"; }

.fa-euro:before,
.fa-eur:before {
    content: "\f153"; }

.fa-gbp:before {
    content: "\f154"; }

.fa-dollar:before,
.fa-usd:before {
    content: "\f155"; }

.fa-rupee:before,
.fa-inr:before {
    content: "\f156"; }

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
    content: "\f157"; }

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
    content: "\f158"; }

.fa-won:before,
.fa-krw:before {
    content: "\f159"; }

.fa-bitcoin:before,
.fa-btc:before {
    content: "\f15a"; }

.fa-file:before {
    content: "\f15b"; }

.fa-file-text:before {
    content: "\f15c"; }

.fa-sort-alpha-asc:before {
    content: "\f15d"; }

.fa-sort-alpha-desc:before {
    content: "\f15e"; }

.fa-sort-amount-asc:before {
    content: "\f160"; }

.fa-sort-amount-desc:before {
    content: "\f161"; }

.fa-sort-numeric-asc:before {
    content: "\f162"; }

.fa-sort-numeric-desc:before {
    content: "\f163"; }

.fa-thumbs-up:before {
    content: "\f164"; }

.fa-thumbs-down:before {
    content: "\f165"; }

.fa-youtube-square:before {
    content: "\f166"; }

.fa-youtube:before {
    content: "\f167"; }

.fa-xing:before {
    content: "\f168"; }

.fa-xing-square:before {
    content: "\f169"; }

.fa-youtube-play:before {
    content: "\f16a"; }

.fa-dropbox:before {
    content: "\f16b"; }

.fa-stack-overflow:before {
    content: "\f16c"; }

.fa-instagram:before {
    content: "\f16d"; }

.fa-flickr:before {
    content: "\f16e"; }

.fa-adn:before {
    content: "\f170"; }

.fa-bitbucket:before {
    content: "\f171"; }

.fa-bitbucket-square:before {
    content: "\f172"; }

.fa-tumblr:before {
    content: "\f173"; }

.fa-tumblr-square:before {
    content: "\f174"; }

.fa-long-arrow-down:before {
    content: "\f175"; }

.fa-long-arrow-up:before {
    content: "\f176"; }

.fa-long-arrow-left:before {
    content: "\f177"; }

.fa-long-arrow-right:before {
    content: "\f178"; }

.fa-apple:before {
    content: "\f179"; }

.fa-windows:before {
    content: "\f17a"; }

.fa-android:before {
    content: "\f17b"; }

.fa-linux:before {
    content: "\f17c"; }

.fa-dribbble:before {
    content: "\f17d"; }

.fa-skype:before {
    content: "\f17e"; }

.fa-foursquare:before {
    content: "\f180"; }

.fa-trello:before {
    content: "\f181"; }

.fa-female:before {
    content: "\f182"; }

.fa-male:before {
    content: "\f183"; }

.fa-gittip:before,
.fa-gratipay:before {
    content: "\f184"; }

.fa-sun-o:before {
    content: "\f185"; }

.fa-moon-o:before {
    content: "\f186"; }

.fa-archive:before {
    content: "\f187"; }

.fa-bug:before {
    content: "\f188"; }

.fa-vk:before {
    content: "\f189"; }

.fa-weibo:before {
    content: "\f18a"; }

.fa-renren:before {
    content: "\f18b"; }

.fa-pagelines:before {
    content: "\f18c"; }

.fa-stack-exchange:before {
    content: "\f18d"; }

.fa-arrow-circle-o-right:before {
    content: "\f18e"; }

.fa-arrow-circle-o-left:before {
    content: "\f190"; }

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
    content: "\f191"; }

.fa-dot-circle-o:before {
    content: "\f192"; }

.fa-wheelchair:before {
    content: "\f193"; }

.fa-vimeo-square:before {
    content: "\f194"; }

.fa-turkish-lira:before,
.fa-try:before {
    content: "\f195"; }

.fa-plus-square-o:before {
    content: "\f196"; }

.fa-space-shuttle:before {
    content: "\f197"; }

.fa-slack:before {
    content: "\f198"; }

.fa-envelope-square:before {
    content: "\f199"; }

.fa-wordpress:before {
    content: "\f19a"; }

.fa-openid:before {
    content: "\f19b"; }

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
    content: "\f19c"; }

.fa-mortar-board:before,
.fa-graduation-cap:before {
    content: "\f19d"; }

.fa-yahoo:before {
    content: "\f19e"; }

.fa-google:before {
    content: "\f1a0"; }

.fa-reddit:before {
    content: "\f1a1"; }

.fa-reddit-square:before {
    content: "\f1a2"; }

.fa-stumbleupon-circle:before {
    content: "\f1a3"; }

.fa-stumbleupon:before {
    content: "\f1a4"; }

.fa-delicious:before {
    content: "\f1a5"; }

.fa-digg:before {
    content: "\f1a6"; }

.fa-pied-piper-pp:before {
    content: "\f1a7"; }

.fa-pied-piper-alt:before {
    content: "\f1a8"; }

.fa-drupal:before {
    content: "\f1a9"; }

.fa-joomla:before {
    content: "\f1aa"; }

.fa-language:before {
    content: "\f1ab"; }

.fa-fax:before {
    content: "\f1ac"; }

.fa-building:before {
    content: "\f1ad"; }

.fa-child:before {
    content: "\f1ae"; }

.fa-paw:before {
    content: "\f1b0"; }

.fa-spoon:before {
    content: "\f1b1"; }

.fa-cube:before {
    content: "\f1b2"; }

.fa-cubes:before {
    content: "\f1b3"; }

.fa-behance:before {
    content: "\f1b4"; }

.fa-behance-square:before {
    content: "\f1b5"; }

.fa-steam:before {
    content: "\f1b6"; }

.fa-steam-square:before {
    content: "\f1b7"; }

.fa-recycle:before {
    content: "\f1b8"; }

.fa-automobile:before,
.fa-car:before {
    content: "\f1b9"; }

.fa-cab:before,
.fa-taxi:before {
    content: "\f1ba"; }

.fa-tree:before {
    content: "\f1bb"; }

.fa-spotify:before {
    content: "\f1bc"; }

.fa-deviantart:before {
    content: "\f1bd"; }

.fa-soundcloud:before {
    content: "\f1be"; }

.fa-database:before {
    content: "\f1c0"; }

.fa-file-pdf-o:before {
    content: "\f1c1"; }

.fa-file-word-o:before {
    content: "\f1c2"; }

.fa-file-excel-o:before {
    content: "\f1c3"; }

.fa-file-powerpoint-o:before {
    content: "\f1c4"; }

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
    content: "\f1c5"; }

.fa-file-zip-o:before,
.fa-file-archive-o:before {
    content: "\f1c6"; }

.fa-file-sound-o:before,
.fa-file-audio-o:before {
    content: "\f1c7"; }

.fa-file-movie-o:before,
.fa-file-video-o:before {
    content: "\f1c8"; }

.fa-file-code-o:before {
    content: "\f1c9"; }

.fa-vine:before {
    content: "\f1ca"; }

.fa-codepen:before {
    content: "\f1cb"; }

.fa-jsfiddle:before {
    content: "\f1cc"; }

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
    content: "\f1cd"; }

.fa-circle-o-notch:before {
    content: "\f1ce"; }

.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
    content: "\f1d0"; }

.fa-ge:before,
.fa-empire:before {
    content: "\f1d1"; }

.fa-git-square:before {
    content: "\f1d2"; }

.fa-git:before {
    content: "\f1d3"; }

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
    content: "\f1d4"; }

.fa-tencent-weibo:before {
    content: "\f1d5"; }

.fa-qq:before {
    content: "\f1d6"; }

.fa-wechat:before,
.fa-weixin:before {
    content: "\f1d7"; }

.fa-send:before,
.fa-paper-plane:before {
    content: "\f1d8"; }

.fa-send-o:before,
.fa-paper-plane-o:before {
    content: "\f1d9"; }

.fa-history:before {
    content: "\f1da"; }

.fa-circle-thin:before {
    content: "\f1db"; }

.fa-header:before {
    content: "\f1dc"; }

.fa-paragraph:before {
    content: "\f1dd"; }

.fa-sliders:before {
    content: "\f1de"; }

.fa-share-alt:before {
    content: "\f1e0"; }

.fa-share-alt-square:before {
    content: "\f1e1"; }

.fa-bomb:before {
    content: "\f1e2"; }

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
    content: "\f1e3"; }

.fa-tty:before {
    content: "\f1e4"; }

.fa-binoculars:before {
    content: "\f1e5"; }

.fa-plug:before {
    content: "\f1e6"; }

.fa-slideshare:before {
    content: "\f1e7"; }

.fa-twitch:before {
    content: "\f1e8"; }

.fa-yelp:before {
    content: "\f1e9"; }

.fa-newspaper-o:before {
    content: "\f1ea"; }

.fa-wifi:before {
    content: "\f1eb"; }

.fa-calculator:before {
    content: "\f1ec"; }

.fa-paypal:before {
    content: "\f1ed"; }

.fa-google-wallet:before {
    content: "\f1ee"; }

.fa-cc-visa:before {
    content: "\f1f0"; }

.fa-cc-mastercard:before {
    content: "\f1f1"; }

.fa-cc-discover:before {
    content: "\f1f2"; }

.fa-cc-amex:before {
    content: "\f1f3"; }

.fa-cc-paypal:before {
    content: "\f1f4"; }

.fa-cc-stripe:before {
    content: "\f1f5"; }

.fa-bell-slash:before {
    content: "\f1f6"; }

.fa-bell-slash-o:before {
    content: "\f1f7"; }

.fa-trash:before {
    content: "\f1f8"; }

.fa-copyright:before {
    content: "\f1f9"; }

.fa-at:before {
    content: "\f1fa"; }

.fa-eyedropper:before {
    content: "\f1fb"; }

.fa-paint-brush:before {
    content: "\f1fc"; }

.fa-birthday-cake:before {
    content: "\f1fd"; }

.fa-area-chart:before {
    content: "\f1fe"; }

.fa-pie-chart:before {
    content: "\f200"; }

.fa-line-chart:before {
    content: "\f201"; }

.fa-lastfm:before {
    content: "\f202"; }

.fa-lastfm-square:before {
    content: "\f203"; }

.fa-toggle-off:before {
    content: "\f204"; }

.fa-toggle-on:before {
    content: "\f205"; }

.fa-bicycle:before {
    content: "\f206"; }

.fa-bus:before {
    content: "\f207"; }

.fa-ioxhost:before {
    content: "\f208"; }

.fa-angellist:before {
    content: "\f209"; }

.fa-cc:before {
    content: "\f20a"; }

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
    content: "\f20b"; }

.fa-meanpath:before {
    content: "\f20c"; }

.fa-buysellads:before {
    content: "\f20d"; }

.fa-connectdevelop:before {
    content: "\f20e"; }

.fa-dashcube:before {
    content: "\f210"; }

.fa-forumbee:before {
    content: "\f211"; }

.fa-leanpub:before {
    content: "\f212"; }

.fa-sellsy:before {
    content: "\f213"; }

.fa-shirtsinbulk:before {
    content: "\f214"; }

.fa-simplybuilt:before {
    content: "\f215"; }

.fa-skyatlas:before {
    content: "\f216"; }

.fa-cart-plus:before {
    content: "\f217"; }

.fa-cart-arrow-down:before {
    content: "\f218"; }

.fa-diamond:before {
    content: "\f219"; }

.fa-ship:before {
    content: "\f21a"; }

.fa-user-secret:before {
    content: "\f21b"; }

.fa-motorcycle:before {
    content: "\f21c"; }

.fa-street-view:before {
    content: "\f21d"; }

.fa-heartbeat:before {
    content: "\f21e"; }

.fa-venus:before {
    content: "\f221"; }

.fa-mars:before {
    content: "\f222"; }

.fa-mercury:before {
    content: "\f223"; }

.fa-intersex:before,
.fa-transgender:before {
    content: "\f224"; }

.fa-transgender-alt:before {
    content: "\f225"; }

.fa-venus-double:before {
    content: "\f226"; }

.fa-mars-double:before {
    content: "\f227"; }

.fa-venus-mars:before {
    content: "\f228"; }

.fa-mars-stroke:before {
    content: "\f229"; }

.fa-mars-stroke-v:before {
    content: "\f22a"; }

.fa-mars-stroke-h:before {
    content: "\f22b"; }

.fa-neuter:before {
    content: "\f22c"; }

.fa-genderless:before {
    content: "\f22d"; }

.fa-facebook-official:before {
    content: "\f230"; }

.fa-pinterest-p:before {
    content: "\f231"; }

.fa-whatsapp:before {
    content: "\f232"; }

.fa-server:before {
    content: "\f233"; }

.fa-user-plus:before {
    content: "\f234"; }

.fa-user-times:before {
    content: "\f235"; }

.fa-hotel:before,
.fa-bed:before {
    content: "\f236"; }

.fa-viacoin:before {
    content: "\f237"; }

.fa-train:before {
    content: "\f238"; }

.fa-subway:before {
    content: "\f239"; }

.fa-medium:before {
    content: "\f23a"; }

.fa-yc:before,
.fa-y-combinator:before {
    content: "\f23b"; }

.fa-optin-monster:before {
    content: "\f23c"; }

.fa-opencart:before {
    content: "\f23d"; }

.fa-expeditedssl:before {
    content: "\f23e"; }

.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
    content: "\f240"; }

.fa-battery-3:before,
.fa-battery-three-quarters:before {
    content: "\f241"; }

.fa-battery-2:before,
.fa-battery-half:before {
    content: "\f242"; }

.fa-battery-1:before,
.fa-battery-quarter:before {
    content: "\f243"; }

.fa-battery-0:before,
.fa-battery-empty:before {
    content: "\f244"; }

.fa-mouse-pointer:before {
    content: "\f245"; }

.fa-i-cursor:before {
    content: "\f246"; }

.fa-object-group:before {
    content: "\f247"; }

.fa-object-ungroup:before {
    content: "\f248"; }

.fa-sticky-note:before {
    content: "\f249"; }

.fa-sticky-note-o:before {
    content: "\f24a"; }

.fa-cc-jcb:before {
    content: "\f24b"; }

.fa-cc-diners-club:before {
    content: "\f24c"; }

.fa-clone:before {
    content: "\f24d"; }

.fa-balance-scale:before {
    content: "\f24e"; }

.fa-hourglass-o:before {
    content: "\f250"; }

.fa-hourglass-1:before,
.fa-hourglass-start:before {
    content: "\f251"; }

.fa-hourglass-2:before,
.fa-hourglass-half:before {
    content: "\f252"; }

.fa-hourglass-3:before,
.fa-hourglass-end:before {
    content: "\f253"; }

.fa-hourglass:before {
    content: "\f254"; }

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
    content: "\f255"; }

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
    content: "\f256"; }

.fa-hand-scissors-o:before {
    content: "\f257"; }

.fa-hand-lizard-o:before {
    content: "\f258"; }

.fa-hand-spock-o:before {
    content: "\f259"; }

.fa-hand-pointer-o:before {
    content: "\f25a"; }

.fa-hand-peace-o:before {
    content: "\f25b"; }

.fa-trademark:before {
    content: "\f25c"; }

.fa-registered:before {
    content: "\f25d"; }

.fa-creative-commons:before {
    content: "\f25e"; }

.fa-gg:before {
    content: "\f260"; }

.fa-gg-circle:before {
    content: "\f261"; }

.fa-tripadvisor:before {
    content: "\f262"; }

.fa-odnoklassniki:before {
    content: "\f263"; }

.fa-odnoklassniki-square:before {
    content: "\f264"; }

.fa-get-pocket:before {
    content: "\f265"; }

.fa-wikipedia-w:before {
    content: "\f266"; }

.fa-safari:before {
    content: "\f267"; }

.fa-chrome:before {
    content: "\f268"; }

.fa-firefox:before {
    content: "\f269"; }

.fa-opera:before {
    content: "\f26a"; }

.fa-internet-explorer:before {
    content: "\f26b"; }

.fa-tv:before,
.fa-television:before {
    content: "\f26c"; }

.fa-contao:before {
    content: "\f26d"; }

.fa-500px:before {
    content: "\f26e"; }

.fa-amazon:before {
    content: "\f270"; }

.fa-calendar-plus-o:before {
    content: "\f271"; }

.fa-calendar-minus-o:before {
    content: "\f272"; }

.fa-calendar-times-o:before {
    content: "\f273"; }

.fa-calendar-check-o:before {
    content: "\f274"; }

.fa-industry:before {
    content: "\f275"; }

.fa-map-pin:before {
    content: "\f276"; }

.fa-map-signs:before {
    content: "\f277"; }

.fa-map-o:before {
    content: "\f278"; }

.fa-map:before {
    content: "\f279"; }

.fa-commenting:before {
    content: "\f27a"; }

.fa-commenting-o:before {
    content: "\f27b"; }

.fa-houzz:before {
    content: "\f27c"; }

.fa-vimeo:before {
    content: "\f27d"; }

.fa-black-tie:before {
    content: "\f27e"; }

.fa-fonticons:before {
    content: "\f280"; }

.fa-reddit-alien:before {
    content: "\f281"; }

.fa-edge:before {
    content: "\f282"; }

.fa-credit-card-alt:before {
    content: "\f283"; }

.fa-codiepie:before {
    content: "\f284"; }

.fa-modx:before {
    content: "\f285"; }

.fa-fort-awesome:before {
    content: "\f286"; }

.fa-usb:before {
    content: "\f287"; }

.fa-product-hunt:before {
    content: "\f288"; }

.fa-mixcloud:before {
    content: "\f289"; }

.fa-scribd:before {
    content: "\f28a"; }

.fa-pause-circle:before {
    content: "\f28b"; }

.fa-pause-circle-o:before {
    content: "\f28c"; }

.fa-stop-circle:before {
    content: "\f28d"; }

.fa-stop-circle-o:before {
    content: "\f28e"; }

.fa-shopping-bag:before {
    content: "\f290"; }

.fa-shopping-basket:before {
    content: "\f291"; }

.fa-hashtag:before {
    content: "\f292"; }

.fa-bluetooth:before {
    content: "\f293"; }

.fa-bluetooth-b:before {
    content: "\f294"; }

.fa-percent:before {
    content: "\f295"; }

.fa-gitlab:before {
    content: "\f296"; }

.fa-wpbeginner:before {
    content: "\f297"; }

.fa-wpforms:before {
    content: "\f298"; }

.fa-envira:before {
    content: "\f299"; }

.fa-universal-access:before {
    content: "\f29a"; }

.fa-wheelchair-alt:before {
    content: "\f29b"; }

.fa-question-circle-o:before {
    content: "\f29c"; }

.fa-blind:before {
    content: "\f29d"; }

.fa-audio-description:before {
    content: "\f29e"; }

.fa-volume-control-phone:before {
    content: "\f2a0"; }

.fa-braille:before {
    content: "\f2a1"; }

.fa-assistive-listening-systems:before {
    content: "\f2a2"; }

.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
    content: "\f2a3"; }

.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
    content: "\f2a4"; }

.fa-glide:before {
    content: "\f2a5"; }

.fa-glide-g:before {
    content: "\f2a6"; }

.fa-signing:before,
.fa-sign-language:before {
    content: "\f2a7"; }

.fa-low-vision:before {
    content: "\f2a8"; }

.fa-viadeo:before {
    content: "\f2a9"; }

.fa-viadeo-square:before {
    content: "\f2aa"; }

.fa-snapchat:before {
    content: "\f2ab"; }

.fa-snapchat-ghost:before {
    content: "\f2ac"; }

.fa-snapchat-square:before {
    content: "\f2ad"; }

.fa-pied-piper:before {
    content: "\f2ae"; }

.fa-first-order:before {
    content: "\f2b0"; }

.fa-yoast:before {
    content: "\f2b1"; }

.fa-themeisle:before {
    content: "\f2b2"; }

.fa-google-plus-circle:before,
.fa-google-plus-official:before {
    content: "\f2b3"; }

.fa-fa:before,
.fa-font-awesome:before {
    content: "\f2b4"; }

.fa-handshake-o:before {
    content: "\f2b5"; }

.fa-envelope-open:before {
    content: "\f2b6"; }

.fa-envelope-open-o:before {
    content: "\f2b7"; }

.fa-linode:before {
    content: "\f2b8"; }

.fa-address-book:before {
    content: "\f2b9"; }

.fa-address-book-o:before {
    content: "\f2ba"; }

.fa-vcard:before,
.fa-address-card:before {
    content: "\f2bb"; }

.fa-vcard-o:before,
.fa-address-card-o:before {
    content: "\f2bc"; }

.fa-user-circle:before {
    content: "\f2bd"; }

.fa-user-circle-o:before {
    content: "\f2be"; }

.fa-user-o:before {
    content: "\f2c0"; }

.fa-id-badge:before {
    content: "\f2c1"; }

.fa-drivers-license:before,
.fa-id-card:before {
    content: "\f2c2"; }

.fa-drivers-license-o:before,
.fa-id-card-o:before {
    content: "\f2c3"; }

.fa-quora:before {
    content: "\f2c4"; }

.fa-free-code-camp:before {
    content: "\f2c5"; }

.fa-telegram:before {
    content: "\f2c6"; }

.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
    content: "\f2c7"; }

.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
    content: "\f2c8"; }

.fa-thermometer-2:before,
.fa-thermometer-half:before {
    content: "\f2c9"; }

.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
    content: "\f2ca"; }

.fa-thermometer-0:before,
.fa-thermometer-empty:before {
    content: "\f2cb"; }

.fa-shower:before {
    content: "\f2cc"; }

.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
    content: "\f2cd"; }

.fa-podcast:before {
    content: "\f2ce"; }

.fa-window-maximize:before {
    content: "\f2d0"; }

.fa-window-minimize:before {
    content: "\f2d1"; }

.fa-window-restore:before {
    content: "\f2d2"; }

.fa-times-rectangle:before,
.fa-window-close:before {
    content: "\f2d3"; }

.fa-times-rectangle-o:before,
.fa-window-close-o:before {
    content: "\f2d4"; }

.fa-bandcamp:before {
    content: "\f2d5"; }

.fa-grav:before {
    content: "\f2d6"; }

.fa-etsy:before {
    content: "\f2d7"; }

.fa-imdb:before {
    content: "\f2d8"; }

.fa-ravelry:before {
    content: "\f2d9"; }

.fa-eercast:before {
    content: "\f2da"; }

.fa-microchip:before {
    content: "\f2db"; }

.fa-snowflake-o:before {
    content: "\f2dc"; }

.fa-superpowers:before {
    content: "\f2dd"; }

.fa-wpexplorer:before {
    content: "\f2de"; }

.fa-meetup:before {
    content: "\f2e0"; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0; }

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto; }

/*!
 * FullCalendar v3.8.0
 * Docs & License: https://fullcalendar.io/
 * (c) 2017 Adam Shaw
 */
.fc {
    direction: ltr;
    text-align: left; }

.fc-rtl {
    text-align: right; }

body .fc {
    /* extra precedence to overcome jqui */
    font-size: 1em; }

/* Colors
--------------------------------------------------------------------------------------------------*/
.fc-highlight {
    /* when user is selecting cells */
    background: #bce8f1;
    opacity: .3; }

.fc-bgevent {
    /* default look for background events */
    background: #8fdf82;
    opacity: .3; }

.fc-nonbusiness {
    /* default look for non-business-hours areas */
    /* will inherit .fc-bgevent's styles */
    background: #d7d7d7; }

/* Buttons (styled <button> tags, normalized to work cross-browser)
--------------------------------------------------------------------------------------------------*/
.fc button {
    /* force height to include the border and padding */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* dimensions */
    margin: 0;
    height: 2.1em;
    padding: 0 .6em;
    /* text & cursor */
    font-size: 1em;
    /* normalize */
    white-space: nowrap;
    cursor: pointer; }

/* Firefox has an annoying inner border */
.fc button::-moz-focus-inner {
    margin: 0;
    padding: 0; }

.fc-state-default {
    /* non-theme */
    border: 1px solid; }

.fc-state-default.fc-corner-left {
    /* non-theme */
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px; }

.fc-state-default.fc-corner-right {
    /* non-theme */
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px; }

/* icons in buttons */
.fc button .fc-icon {
    /* non-theme */
    position: relative;
    top: -0.05em;
    /* seems to be a good adjustment across browsers */
    margin: 0 .2em;
    vertical-align: middle; }

/*
  button states
  borrowed from twitter bootstrap (http://twitter.github.com/bootstrap/)
*/
.fc-state-default {
    background-color: #f5f5f5;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e6e6e6));
    background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
    background-repeat: repeat-x;
    border-color: #e6e6e6 #e6e6e6 #bfbfbf;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    color: #333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05); }

.fc-state-hover,
.fc-state-down,
.fc-state-active,
.fc-state-disabled {
    color: #333333;
    background-color: #e6e6e6; }

.fc-state-hover {
    color: #333333;
    text-decoration: none;
    background-position: 0 -15px;
    -webkit-transition: background-position 0.1s linear;
    transition: background-position 0.1s linear; }

.fc-state-down,
.fc-state-active {
    background-color: #cccccc;
    background-image: none;
    -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); }

.fc-state-disabled {
    cursor: default;
    background-image: none;
    opacity: 0.65;
    -webkit-box-shadow: none;
    box-shadow: none; }

/* Buttons Groups
--------------------------------------------------------------------------------------------------*/
.fc-button-group {
    display: inline-block; }

/*
every button that is not first in a button group should scootch over one pixel and cover the
previous button's border...
*/
.fc .fc-button-group > * {
    /* extra precedence b/c buttons have margin set to zero */
    float: left;
    margin: 0 0 0 -1px; }

.fc .fc-button-group > :first-child {
    /* same */
    margin-left: 0; }

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc-popover {
    position: absolute;
    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); }

.fc-popover .fc-header {
    /* TODO: be more consistent with fc-head/fc-body */
    padding: 2px 4px; }

.fc-popover .fc-header .fc-title {
    margin: 0 2px; }

.fc-popover .fc-header .fc-close {
    cursor: pointer; }

.fc-ltr .fc-popover .fc-header .fc-title,
.fc-rtl .fc-popover .fc-header .fc-close {
    float: left; }

.fc-rtl .fc-popover .fc-header .fc-title,
.fc-ltr .fc-popover .fc-header .fc-close {
    float: right; }

/* Misc Reusable Components
--------------------------------------------------------------------------------------------------*/
.fc-divider {
    border-style: solid;
    border-width: 1px; }

hr.fc-divider {
    height: 0;
    margin: 0;
    padding: 0 0 2px;
    /* height is unreliable across browsers, so use padding */
    border-width: 1px 0; }

.fc-clear {
    clear: both; }

.fc-bg,
.fc-bgevent-skeleton,
.fc-highlight-skeleton,
.fc-helper-skeleton {
    /* these element should always cling to top-left/right corners */
    position: absolute;
    top: 0;
    left: 0;
    right: 0; }

.fc-bg {
    bottom: 0;
    /* strech bg to bottom edge */ }

.fc-bg table {
    height: 100%;
    /* strech bg to bottom edge */ }

/* Tables
--------------------------------------------------------------------------------------------------*/
.fc table {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* fix scrollbar issue in firefox */
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em;
    /* normalize cross-browser */ }

.fc th {
    text-align: center; }

.fc th,
.fc td {
    border-style: solid;
    border-width: 1px;
    padding: 0;
    vertical-align: top; }

.fc td.fc-today {
    border-style: double;
    /* overcome neighboring borders */ }

/* Internal Nav Links
--------------------------------------------------------------------------------------------------*/
a[data-goto] {
    cursor: pointer; }

a[data-goto]:hover {
    text-decoration: underline; }

/* Fake Table Rows
--------------------------------------------------------------------------------------------------*/
.fc .fc-row {
    /* extra precedence to overcome themes w/ .ui-widget-content forcing a 1px border */
    /* no visible border by default. but make available if need be (scrollbar width compensation) */
    border-style: solid;
    border-width: 0; }

.fc-row table {
    /* don't put left/right border on anything within a fake row.
       the outer tbody will worry about this */
    border-left: 0 hidden transparent;
    border-right: 0 hidden transparent;
    /* no bottom borders on rows */
    border-bottom: 0 hidden transparent; }

.fc-row:first-child table {
    border-top: 0 hidden transparent;
    /* no top border on first row */ }

/* Day Row (used within the header and the DayGrid)
--------------------------------------------------------------------------------------------------*/
.fc-row {
    position: relative; }

.fc-row .fc-bg {
    z-index: 1; }

/* highlighting cells & background event skeleton */
.fc-row .fc-bgevent-skeleton,
.fc-row .fc-highlight-skeleton {
    bottom: 0;
    /* stretch skeleton to bottom of row */ }

.fc-row .fc-bgevent-skeleton table,
.fc-row .fc-highlight-skeleton table {
    height: 100%;
    /* stretch skeleton to bottom of row */ }

.fc-row .fc-highlight-skeleton td,
.fc-row .fc-bgevent-skeleton td {
    border-color: transparent; }

.fc-row .fc-bgevent-skeleton {
    z-index: 2; }

.fc-row .fc-highlight-skeleton {
    z-index: 3; }

/*
row content (which contains day/week numbers and events) as well as "helper" (which contains
temporary rendered events).
*/
.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 4;
    padding-bottom: 2px;
    /* matches the space above the events */ }

.fc-row .fc-helper-skeleton {
    z-index: 5; }

.fc .fc-row .fc-content-skeleton table,
.fc .fc-row .fc-content-skeleton td,
.fc .fc-row .fc-helper-skeleton td {
    /* see-through to the background below */
    /* extra precedence to prevent theme-provided backgrounds */
    background: none;
    /* in case <td>s are globally styled */
    border-color: transparent; }

.fc-row .fc-content-skeleton td,
.fc-row .fc-helper-skeleton td {
    /* don't put a border between events and/or the day number */
    border-bottom: 0; }

.fc-row .fc-content-skeleton tbody td,
.fc-row .fc-helper-skeleton tbody td {
    /* don't put a border between event cells */
    border-top: 0; }

/* Scrolling Container
--------------------------------------------------------------------------------------------------*/
.fc-scroller {
    -webkit-overflow-scrolling: touch; }

/* TODO: move to agenda/basic */
.fc-scroller > .fc-day-grid,
.fc-scroller > .fc-time-grid {
    position: relative;
    /* re-scope all positions */
    width: 100%;
    /* hack to force re-sizing this inner element when scrollbars appear/disappear */ }

/* Global Event Styles
--------------------------------------------------------------------------------------------------*/
.fc-event {
    position: relative;
    /* for resize handle and other inner positioning */
    display: block;
    /* make the <a> tag block */
    font-size: .85em;
    line-height: 1.3;
    border-radius: 3px;
    border: 1px solid #3a87ad;
    /* default BORDER color */ }

.fc-event,
.fc-event-dot {
    background-color: #3a87ad;
    /* default BACKGROUND color */ }

.fc-event,
.fc-event:hover {
    color: #fff;
    /* default TEXT color */
    text-decoration: none;
    /* if <a> has an href */ }

.fc-event[href],
.fc-event.fc-draggable {
    cursor: pointer;
    /* give events with links and draggable events a hand mouse pointer */ }

.fc-not-allowed,
.fc-not-allowed .fc-event {
    /* to override an event's custom cursor */
    cursor: not-allowed; }

.fc-event .fc-bg {
    /* the generic .fc-bg already does position */
    z-index: 1;
    background: #fff;
    opacity: .25; }

.fc-event .fc-content {
    position: relative;
    z-index: 2; }

/* resizer (cursor AND touch devices) */
.fc-event .fc-resizer {
    position: absolute;
    z-index: 4; }

/* resizer (touch devices) */
.fc-event .fc-resizer {
    display: none; }

.fc-event.fc-allow-mouse-resize .fc-resizer,
.fc-event.fc-selected .fc-resizer {
    /* only show when hovering or selected (with touch) */
    display: block; }

/* hit area */
.fc-event.fc-selected .fc-resizer:before {
    /* 40x40 touch area */
    content: "";
    position: absolute;
    z-index: 9999;
    /* user of this util can scope within a lower z-index */
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin-left: -20px;
    margin-top: -20px; }

/* Event Selection (only for touch devices)
--------------------------------------------------------------------------------------------------*/
.fc-event.fc-selected {
    z-index: 9999 !important;
    /* overcomes inline z-index */
    -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); }

.fc-event.fc-selected.fc-dragging {
    -webkit-box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3); }

/* Horizontal Events
--------------------------------------------------------------------------------------------------*/
/* bigger touch area when selected */
.fc-h-event.fc-selected:before {
    content: "";
    position: absolute;
    z-index: 3;
    /* below resizers */
    top: -10px;
    bottom: -10px;
    left: 0;
    right: 0; }

/* events that are continuing to/from another week. kill rounded corners and butt up against edge */
.fc-ltr .fc-h-event.fc-not-start,
.fc-rtl .fc-h-event.fc-not-end {
    margin-left: 0;
    border-left-width: 0;
    padding-left: 1px;
    /* replace the border with padding */
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }

.fc-ltr .fc-h-event.fc-not-end,
.fc-rtl .fc-h-event.fc-not-start {
    margin-right: 0;
    border-right-width: 0;
    padding-right: 1px;
    /* replace the border with padding */
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }

/* resizer (cursor AND touch devices) */
/* left resizer  */
.fc-ltr .fc-h-event .fc-start-resizer,
.fc-rtl .fc-h-event .fc-end-resizer {
    cursor: w-resize;
    left: -1px;
    /* overcome border */ }

/* right resizer */
.fc-ltr .fc-h-event .fc-end-resizer,
.fc-rtl .fc-h-event .fc-start-resizer {
    cursor: e-resize;
    right: -1px;
    /* overcome border */ }

/* resizer (mouse devices) */
.fc-h-event.fc-allow-mouse-resize .fc-resizer {
    width: 7px;
    top: -1px;
    /* overcome top border */
    bottom: -1px;
    /* overcome bottom border */ }

/* resizer (touch devices) */
.fc-h-event.fc-selected .fc-resizer {
    /* 8x8 little dot */
    border-radius: 4px;
    border-width: 1px;
    width: 6px;
    height: 6px;
    border-style: solid;
    border-color: inherit;
    background: #fff;
    /* vertically center */
    top: 50%;
    margin-top: -4px; }

/* left resizer  */
.fc-ltr .fc-h-event.fc-selected .fc-start-resizer,
.fc-rtl .fc-h-event.fc-selected .fc-end-resizer {
    margin-left: -4px;
    /* centers the 8x8 dot on the left edge */ }

/* right resizer */
.fc-ltr .fc-h-event.fc-selected .fc-end-resizer,
.fc-rtl .fc-h-event.fc-selected .fc-start-resizer {
    margin-right: -4px;
    /* centers the 8x8 dot on the right edge */ }

/* DayGrid events
----------------------------------------------------------------------------------------------------
We use the full "fc-day-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/
.fc-day-grid-event {
    margin: 1px 2px 0;
    /* spacing between events and edges */
    padding: 0 1px; }

tr:first-child > td > .fc-day-grid-event {
    margin-top: 2px;
    /* a little bit more space before the first event */ }

.fc-day-grid-event.fc-selected:after {
    content: "";
    position: absolute;
    z-index: 1;
    /* same z-index as fc-bg, behind text */
    /* overcome the borders */
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    /* darkening effect */
    background: #000;
    opacity: .25; }

.fc-day-grid-event .fc-content {
    /* force events to be one-line tall */
    white-space: nowrap;
    overflow: hidden; }

.fc-day-grid-event .fc-time {
    font-weight: bold; }

/* resizer (cursor devices) */
/* left resizer  */
.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer,
.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer {
    margin-left: -2px;
    /* to the day cell's edge */ }

/* right resizer */
.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer,
.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer {
    margin-right: -2px;
    /* to the day cell's edge */ }

/* Event Limiting
--------------------------------------------------------------------------------------------------*/
/* "more" link that represents hidden events */
a.fc-more {
    margin: 1px 3px;
    font-size: .85em;
    cursor: pointer;
    text-decoration: none; }

a.fc-more:hover {
    text-decoration: underline; }

.fc-limited {
    /* rows and cells that are hidden because of a "more" link */
    display: none; }

/* popover that appears when "more" link is clicked */
.fc-day-grid .fc-row {
    z-index: 1;
    /* make the "more" popover one higher than this */ }

.fc-more-popover {
    z-index: 2;
    width: 220px; }

.fc-more-popover .fc-event-container {
    padding: 10px; }

/* Now Indicator
--------------------------------------------------------------------------------------------------*/
.fc-now-indicator {
    position: absolute;
    border: 0 solid red; }

/* Utilities
--------------------------------------------------------------------------------------------------*/
.fc-unselectable {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent; }

/*
TODO: more distinction between this file and common.css
*/
/* Colors
--------------------------------------------------------------------------------------------------*/
.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-content,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-list-heading td {
    border-color: #ddd; }

.fc-unthemed .fc-popover {
    background-color: #fff; }

.fc-unthemed .fc-divider,
.fc-unthemed .fc-popover .fc-header,
.fc-unthemed .fc-list-heading td {
    background: #eee; }

.fc-unthemed .fc-popover .fc-header .fc-close {
    color: #666; }

.fc-unthemed td.fc-today {
    background: #fcf8e3; }

.fc-unthemed .fc-disabled-day {
    background: #d7d7d7;
    opacity: .3; }

/* Icons (inline elements with styled text that mock arrow icons)
--------------------------------------------------------------------------------------------------*/
.fc-icon {
    display: inline-block;
    height: 1em;
    line-height: 1em;
    font-size: 1em;
    text-align: center;
    overflow: hidden;
    font-family: "Courier New", Courier, monospace;
    /* don't allow browser text-selection */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }

/*
Acceptable font-family overrides for individual icons:
  "Arial", sans-serif
  "Times New Roman", serif

NOTE: use percentage font sizes or else old IE chokes
*/
.fc-icon:after {
    position: relative; }

.fc-icon-left-single-arrow:after {
    content: "\2039";
    font-weight: bold;
    font-size: 200%;
    top: -7%; }

.fc-icon-right-single-arrow:after {
    content: "\203A";
    font-weight: bold;
    font-size: 200%;
    top: -7%; }

.fc-icon-left-double-arrow:after {
    content: "\AB";
    font-size: 160%;
    top: -7%; }

.fc-icon-right-double-arrow:after {
    content: "\BB";
    font-size: 160%;
    top: -7%; }

.fc-icon-left-triangle:after {
    content: "\25C4";
    font-size: 125%;
    top: 3%; }

.fc-icon-right-triangle:after {
    content: "\25BA";
    font-size: 125%;
    top: 3%; }

.fc-icon-down-triangle:after {
    content: "\25BC";
    font-size: 125%;
    top: 2%; }

.fc-icon-x:after {
    content: "\D7";
    font-size: 200%;
    top: 6%; }

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc-unthemed .fc-popover {
    border-width: 1px;
    border-style: solid; }

.fc-unthemed .fc-popover .fc-header .fc-close {
    font-size: .9em;
    margin-top: 2px; }

/* List View
--------------------------------------------------------------------------------------------------*/
.fc-unthemed .fc-list-item:hover td {
    background-color: #f5f5f5; }

/* Colors
--------------------------------------------------------------------------------------------------*/
.ui-widget .fc-disabled-day {
    background-image: none; }

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc-popover > .ui-widget-header + .ui-widget-content {
    border-top: 0;
    /* where they meet, let the header have the border */ }

/* Global Event Styles
--------------------------------------------------------------------------------------------------*/
.ui-widget .fc-event {
    /* overpower jqui's styles on <a> tags. TODO: more DRY */
    color: #fff;
    /* default TEXT color */
    text-decoration: none;
    /* if <a> has an href */
    /* undo ui-widget-header bold */
    font-weight: normal; }

/* TimeGrid axis running down the side (for both the all-day area and the slot area)
--------------------------------------------------------------------------------------------------*/
.ui-widget td.fc-axis {
    font-weight: normal;
    /* overcome bold */ }

/* TimeGrid Slats (lines that run horizontally)
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-slats .ui-widget-content {
    background: none;
    /* see through to fc-bg */ }

.fc.fc-bootstrap3 a {
    text-decoration: none; }

.fc.fc-bootstrap3 a[data-goto]:hover {
    text-decoration: underline; }

.fc-bootstrap3 hr.fc-divider {
    border-color: inherit; }

.fc-bootstrap3 .fc-today.alert {
    border-radius: 0; }

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc-bootstrap3 .fc-popover .panel-body {
    padding: 0;
    /* undo built-in padding */ }

/* TimeGrid Slats (lines that run horizontally)
--------------------------------------------------------------------------------------------------*/
.fc-bootstrap3 .fc-time-grid .fc-slats table {
    /* some themes have background color. see through to slats */
    background: none; }

/* Toolbar
--------------------------------------------------------------------------------------------------*/
.fc-toolbar {
    text-align: center; }

.fc-toolbar.fc-header-toolbar {
    margin-bottom: 1em; }

.fc-toolbar.fc-footer-toolbar {
    margin-top: 1em; }

.fc-toolbar .fc-left {
    float: left; }

.fc-toolbar .fc-right {
    float: right; }

.fc-toolbar .fc-center {
    display: inline-block; }

/* the things within each left/right/center section */
.fc .fc-toolbar > * > * {
    /* extra precedence to override button border margins */
    float: left;
    margin-left: .75em; }

/* the first thing within each left/center/right section */
.fc .fc-toolbar > * > :first-child {
    /* extra precedence to override button border margins */
    margin-left: 0; }

/* title text */
.fc-toolbar h2 {
    margin: 0; }

/* button layering (for border precedence) */
.fc-toolbar button {
    position: relative; }

.fc-toolbar .fc-state-hover,
.fc-toolbar .ui-state-hover {
    z-index: 2; }

.fc-toolbar .fc-state-down {
    z-index: 3; }

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active {
    z-index: 4; }

.fc-toolbar button:focus {
    z-index: 5; }

/* View Structure
--------------------------------------------------------------------------------------------------*/
/* undo twitter bootstrap's box-sizing rules. normalizes positioning techniques */
/* don't do this for the toolbar because we'll want bootstrap to style those buttons as some pt */
.fc-view-container *,
.fc-view-container *:before,
.fc-view-container *:after {
    -webkit-box-sizing: content-box;
    box-sizing: content-box; }

.fc-view,
.fc-view > table {
    /* so dragged elements can be above the view's main element */
    position: relative;
    z-index: 1; }

/* BasicView
--------------------------------------------------------------------------------------------------*/
/* day row structure */
.fc-basicWeek-view .fc-content-skeleton,
.fc-basicDay-view .fc-content-skeleton {
    /* there may be week numbers in these views, so no padding-top */
    padding-bottom: 1em;
    /* ensure a space at bottom of cell for user selecting/clicking */ }

.fc-basic-view .fc-body .fc-row {
    min-height: 4em;
    /* ensure that all rows are at least this tall */ }

/* a "rigid" row will take up a constant amount of height because content-skeleton is absolute */
.fc-row.fc-rigid {
    overflow: hidden; }

.fc-row.fc-rigid .fc-content-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0; }

/* week and day number styling */
.fc-day-top.fc-other-month {
    opacity: 0.3; }

.fc-basic-view .fc-week-number,
.fc-basic-view .fc-day-number {
    padding: 2px; }

.fc-basic-view th.fc-week-number,
.fc-basic-view th.fc-day-number {
    padding: 0 2px;
    /* column headers can't have as much v space */ }

.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
    float: right; }

.fc-rtl .fc-basic-view .fc-day-top .fc-day-number {
    float: left; }

.fc-ltr .fc-basic-view .fc-day-top .fc-week-number {
    float: left;
    border-radius: 0 0 3px 0; }

.fc-rtl .fc-basic-view .fc-day-top .fc-week-number {
    float: right;
    border-radius: 0 0 0 3px; }

.fc-basic-view .fc-day-top .fc-week-number {
    min-width: 1.5em;
    text-align: center;
    background-color: #f2f2f2;
    color: #808080; }

/* when week/day number have own column */
.fc-basic-view td.fc-week-number {
    text-align: center; }

.fc-basic-view td.fc-week-number > * {
    /* work around the way we do column resizing and ensure a minimum width */
    display: inline-block;
    min-width: 1.25em; }

/* AgendaView all-day area
--------------------------------------------------------------------------------------------------*/
.fc-agenda-view .fc-day-grid {
    position: relative;
    z-index: 2;
    /* so the "more.." popover will be over the time grid */ }

.fc-agenda-view .fc-day-grid .fc-row {
    min-height: 3em;
    /* all-day section will never get shorter than this */ }

.fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
    padding-bottom: 1em;
    /* give space underneath events for clicking/selecting days */ }

/* TimeGrid axis running down the side (for both the all-day area and the slot area)
--------------------------------------------------------------------------------------------------*/
.fc .fc-axis {
    /* .fc to overcome default cell styles */
    vertical-align: middle;
    padding: 0 4px;
    white-space: nowrap; }

.fc-ltr .fc-axis {
    text-align: right; }

.fc-rtl .fc-axis {
    text-align: left; }

/* TimeGrid Structure
--------------------------------------------------------------------------------------------------*/
.fc-time-grid-container,
.fc-time-grid {
    /* so slats/bg/content/etc positions get scoped within here */
    position: relative;
    z-index: 1; }

.fc-time-grid {
    min-height: 100%;
    /* so if height setting is 'auto', .fc-bg stretches to fill height */ }

.fc-time-grid table {
    /* don't put outer borders on slats/bg/content/etc */
    border: 0 hidden transparent; }

.fc-time-grid > .fc-bg {
    z-index: 1; }

.fc-time-grid .fc-slats,
.fc-time-grid > hr {
    /* the <hr> AgendaView injects when grid is shorter than scroller */
    position: relative;
    z-index: 2; }

.fc-time-grid .fc-content-col {
    position: relative;
    /* because now-indicator lives directly inside */ }

.fc-time-grid .fc-content-skeleton {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    right: 0; }

/* divs within a cell within the fc-content-skeleton */
.fc-time-grid .fc-business-container {
    position: relative;
    z-index: 1; }

.fc-time-grid .fc-bgevent-container {
    position: relative;
    z-index: 2; }

.fc-time-grid .fc-highlight-container {
    position: relative;
    z-index: 3; }

.fc-time-grid .fc-event-container {
    position: relative;
    z-index: 4; }

.fc-time-grid .fc-now-indicator-line {
    z-index: 5; }

.fc-time-grid .fc-helper-container {
    /* also is fc-event-container */
    position: relative;
    z-index: 6; }

/* TimeGrid Slats (lines that run horizontally)
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-slats td {
    height: 1.5em;
    border-bottom: 0;
    /* each cell is responsible for its top border */ }

.fc-time-grid .fc-slats .fc-minor td {
    border-top-style: dotted; }

/* TimeGrid Highlighting Slots
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-highlight-container {
    /* a div within a cell within the fc-highlight-skeleton */
    position: relative;
    /* scopes the left/right of the fc-highlight to be in the column */ }

.fc-time-grid .fc-highlight {
    position: absolute;
    left: 0;
    right: 0;
    /* top and bottom will be in by JS */ }

/* TimeGrid Event Containment
--------------------------------------------------------------------------------------------------*/
.fc-ltr .fc-time-grid .fc-event-container {
    /* space on the sides of events for LTR (default) */
    margin: 0 2.5% 0 2px; }

.fc-rtl .fc-time-grid .fc-event-container {
    /* space on the sides of events for RTL */
    margin: 0 2px 0 2.5%; }

.fc-time-grid .fc-event,
.fc-time-grid .fc-bgevent {
    position: absolute;
    z-index: 1;
    /* scope inner z-index's */ }

.fc-time-grid .fc-bgevent {
    /* background events always span full width */
    left: 0;
    right: 0; }

/* Generic Vertical Event
--------------------------------------------------------------------------------------------------*/
.fc-v-event.fc-not-start {
    /* events that are continuing from another day */
    /* replace space made by the top border with padding */
    border-top-width: 0;
    padding-top: 1px;
    /* remove top rounded corners */
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.fc-v-event.fc-not-end {
    /* replace space made by the top border with padding */
    border-bottom-width: 0;
    padding-bottom: 1px;
    /* remove bottom rounded corners */
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0; }

/* TimeGrid Event Styling
----------------------------------------------------------------------------------------------------
We use the full "fc-time-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/
.fc-time-grid-event {
    overflow: hidden;
    /* don't let the bg flow over rounded corners */ }

.fc-time-grid-event.fc-selected {
    /* need to allow touch resizers to extend outside event's bounding box */
    /* common fc-selected styles hide the fc-bg, so don't need this anyway */
    overflow: visible; }

.fc-time-grid-event.fc-selected .fc-bg {
    display: none;
    /* hide semi-white background, to appear darker */ }

.fc-time-grid-event .fc-content {
    overflow: hidden;
    /* for when .fc-selected */ }

.fc-time-grid-event .fc-time,
.fc-time-grid-event .fc-title {
    padding: 0 1px; }

.fc-time-grid-event .fc-time {
    font-size: .85em;
    white-space: nowrap; }

/* short mode, where time and title are on the same line */
.fc-time-grid-event.fc-short .fc-content {
    /* don't wrap to second line (now that contents will be inline) */
    white-space: nowrap; }

.fc-time-grid-event.fc-short .fc-time,
.fc-time-grid-event.fc-short .fc-title {
    /* put the time and title on the same line */
    display: inline-block;
    vertical-align: top; }

.fc-time-grid-event.fc-short .fc-time span {
    display: none;
    /* don't display the full time text... */ }

.fc-time-grid-event.fc-short .fc-time:before {
    content: attr(data-start);
    /* ...instead, display only the start time */ }

.fc-time-grid-event.fc-short .fc-time:after {
    content: "\A0-\A0";
    /* seperate with a dash, wrapped in nbsp's */ }

.fc-time-grid-event.fc-short .fc-title {
    font-size: .85em;
    /* make the title text the same size as the time */
    padding: 0;
    /* undo padding from above */ }

/* resizer (cursor device) */
.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer {
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    overflow: hidden;
    line-height: 8px;
    font-size: 11px;
    font-family: monospace;
    text-align: center;
    cursor: s-resize; }

.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after {
    content: "="; }

/* resizer (touch device) */
.fc-time-grid-event.fc-selected .fc-resizer {
    /* 10x10 dot */
    border-radius: 5px;
    border-width: 1px;
    width: 8px;
    height: 8px;
    border-style: solid;
    border-color: inherit;
    background: #fff;
    /* horizontally center */
    left: 50%;
    margin-left: -5px;
    /* center on the bottom edge */
    bottom: -5px; }

/* Now Indicator
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-now-indicator-line {
    border-top-width: 1px;
    left: 0;
    right: 0; }

/* arrow on axis */
.fc-time-grid .fc-now-indicator-arrow {
    margin-top: -5px;
    /* vertically center on top coordinate */ }

.fc-ltr .fc-time-grid .fc-now-indicator-arrow {
    left: 0;
    /* triangle pointing right... */
    border-width: 5px 0 5px 6px;
    border-top-color: transparent;
    border-bottom-color: transparent; }

.fc-rtl .fc-time-grid .fc-now-indicator-arrow {
    right: 0;
    /* triangle pointing left... */
    border-width: 5px 6px 5px 0;
    border-top-color: transparent;
    border-bottom-color: transparent; }

/* List View
--------------------------------------------------------------------------------------------------*/
/* possibly reusable */
.fc-event-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 5px; }

/* view wrapper */
.fc-rtl .fc-list-view {
    direction: rtl;
    /* unlike core views, leverage browser RTL */ }

.fc-list-view {
    border-width: 1px;
    border-style: solid; }

/* table resets */
.fc .fc-list-table {
    table-layout: auto;
    /* for shrinkwrapping cell content */ }

.fc-list-table td {
    border-width: 1px 0 0;
    padding: 8px 14px; }

.fc-list-table tr:first-child td {
    border-top-width: 0; }

/* day headings with the list */
.fc-list-heading {
    border-bottom-width: 1px; }

.fc-list-heading td {
    font-weight: bold; }

.fc-ltr .fc-list-heading-main {
    float: left; }

.fc-ltr .fc-list-heading-alt {
    float: right; }

.fc-rtl .fc-list-heading-main {
    float: right; }

.fc-rtl .fc-list-heading-alt {
    float: left; }

/* event list items */
.fc-list-item.fc-has-url {
    cursor: pointer;
    /* whole row will be clickable */ }

.fc-list-item-marker,
.fc-list-item-time {
    white-space: nowrap;
    width: 1px; }

/* make the dot closer to the event title */
.fc-ltr .fc-list-item-marker {
    padding-right: 0; }

.fc-rtl .fc-list-item-marker {
    padding-left: 0; }

.fc-list-item-title a {
    /* every event title cell has an <a> tag */
    text-decoration: none;
    color: inherit; }

.fc-list-item-title a[href]:hover {
    /* hover effect only on titles with hrefs */
    text-decoration: underline; }

/* message when no events */
.fc-list-empty-wrap2 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0; }

.fc-list-empty-wrap1 {
    width: 100%;
    height: 100%;
    display: table; }

.fc-list-empty {
    display: table-cell;
    vertical-align: middle;
    text-align: center; }

.fc-unthemed .fc-list-empty {
    /* theme will provide own background */
    background-color: #eee; }

/* ==========================================================================
   #FULL CALENDAR CUSTOM
   ========================================================================== */
/**
 * Instead of modifying `_fullcalendar.scss` directly, we add Dashboard's custom
 * styles for fullcanlendar plugin here.
 */
/**
 * Calendar Wrapper
 */
.fc-view-container {
    background-color: #fff; }

/**
 * Calendar Header
 *
 * 1. These classes aren't created by fullcalendar plugin, but it's used to 
 *    customize the header of the calender to suit Dashboard's original design.
 */
.fc-header {
    /* [1] */
    height: 50px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    line-height: 50px;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }

.fc-header-title {
    /* [1] */
    margin: 0 0 0 30px;
    color: #fff;
    font-size: 1rem; }

.fc-toolbar.fc-header-toolbar {
    position: relative;
    height: 50px;
    line-height: 50px;
    padding: 0 7px;
    margin: 0;
    background-color: #222C3C; }

.fc-right .fc-button-group {
    position: relative;
    top: -43px;
    right: 10px;
    height: 36px;
    border: 1px solid #1A83D2;
    border-radius: 4px; }

.fc-button,
.fc-state-default,
.fc-agendaDay-button,
.fc-agendaWeek-button {
    padding: 0 20px !important;
    height: 34px !important;
    background-color: #1E8FE4;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: transparent;
    color: #F0F3F8;
    font-size: 0.875rem !important;
    text-shadow: none;
    background-image: none;
    border: 0;
    outline: none; }

.fc-month-button::first-letter,
.fc-agendaDay-button::first-letter,
.fc-agendaWeek-button::first-letter {
    text-transform: uppercase; }

.fc-toolbar .fc-center {
    display: block;
    width: 100px;
    margin: 0 auto; }
.fc-toolbar .fc-center h2 {
    color: #fff;
    font-size: 0.875rem; }

/**
 * Prev / Next Arrows (Buttons)
 */
.fc-left .fc-button-group .fc-button {
    margin: 0; }

.fc-state-default.fc-prev-button,
.fc-state-default.fc-next-button {
    border-radius: 100% !important; }

.fc-prev-button,
.fc-next-button {
    position: absolute !important;
    top: 11px;
    padding: 0 !important;
    height: 28px !important;
    width: 28px;
    background-color: #313C4D;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #CED0DA;
    font-size: 8px !important;
    text-shadow: none;
    background-image: none;
    border: 0; }

.fc-prev-button {
    left: 30px; }

.fc-next-button {
    right: 30px; }

/**
 * Days of the week - table header
 */
.fc th {
    padding: 12px 0;
    border: 0;
    color: #ADAFB2;
    text-transform: uppercase;
    font-size: 0.75rem;
    font-weight: 600; }

/**
 * Days Number - (table cell)
 */
.fc-day-number {
    padding: 15px 15px 0 0 !important;
    color: #354052;
    font-size: 1rem;
    font-weight: 600; }

.fc-unthemed td {
    border-color: #e6eaee; }

/**
 * Current Day
 */
.fc-unthemed td.fc-today {
    background: none; }

.fc-today .fc-day-number {
    background-color: red;
    padding: 0 !important;
    margin: 10px 10px 8px 0;
    width: 28px;
    height: 28px;
    line-height: 28px;
    border-radius: 100%;
    text-align: center;
    color: #fff; }

/**
 * Event
 */
.fc-event {
    margin: 2px 0 0 0;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 600;
    background-color: rgba(27, 185, 52, 0.1);
    border: 0;
    border-radius: 0;
    border-left: 2px solid #E6EAEE; }
.fc-event:hover {
    color: #354052; }

.fc-event .fc-content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 8px 8px 8px 12px; }

.fc-title {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1; }

.fc-time {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    text-transform: uppercase;
    color: #354052;
    font-size: 0.75rem;
    font-weight: 400 !important; }

/**
 * Event Color Variations
 */
.fc-event--blue {
    background-color: #269AF3;
    background-color: rgba(38, 154, 243, 0.1);
    border-color: #269AF3; }

.fc-event--orange {
    background-color: #FF7800;
    background-color: rgba(255, 120, 0, 0.1);
    border-color: #FF7800; }

.fc-event--green {
    background-color: #1BB934;
    background-color: rgba(27, 185, 52, 0.1);
    border-color: #1BB934; }

.gu-mirror {
    position: fixed !important;
    margin: 0 !important;
    z-index: 9999 !important;
    opacity: 0.8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    filter: alpha(opacity=80); }

.gu-hide {
    display: none !important; }

.gu-unselectable {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important; }

.gu-transit {
    opacity: 0.2;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
    filter: alpha(opacity=20); }

.select2-container {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle; }

.select2-container .select2-selection--single {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-select: none; }

.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

.select2-container .select2-selection--single .select2-selection__clear {
    position: relative; }

.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 20px; }

.select2-container .select2-selection--multiple {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-select: none; }

.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-left: 8px;
    text-overflow: ellipsis;
    white-space: nowrap; }

.select2-container .select2-search--inline {
    float: left; }

.select2-container .select2-search--inline .select2-search__field {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
    padding: 0; }

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none; }

.select2-dropdown {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    position: absolute;
    left: -100000px;
    width: 100%;
    z-index: 1051; }

.select2-results {
    display: block; }

.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0; }

.select2-results__option {
    padding: 6px;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-select: none; }

.select2-results__option[aria-selected] {
    cursor: pointer; }

.select2-container--open .select2-dropdown {
    left: 0; }

.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0; }

.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.select2-search--dropdown {
    display: block;
    padding: 4px; }

.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box; }

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none; }

.select2-search--dropdown.select2-search--hide {
    display: none; }

.select2-close-mask {
    border: 0;
    margin: 0;
    padding: 0;
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    min-height: 100%;
    min-width: 100%;
    height: auto;
    width: auto;
    opacity: 0;
    z-index: 99;
    background-color: #fff;
    filter: alpha(opacity=0); }

.select2-hidden-accessible {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important;
    height: 1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    white-space: nowrap !important; }

.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px; }

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px; }

.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold; }

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #999; }

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px; }

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0; }

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left; }

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto; }

.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: #eee;
    cursor: default; }

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
    display: none; }

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px; }

.select2-container--default .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text; }

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%; }

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    list-style: none; }

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #999;
    margin-top: 5px;
    float: left; }

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-top: 5px;
    margin-right: 10px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #999;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #333; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
    float: right; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto; }

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid black 1px;
    outline: 0; }

.select2-container--default.select2-container--disabled .select2-selection--multiple {
    background-color: #eee;
    cursor: default; }

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
    display: none; }

.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0; }

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa; }

.select2-container--default .select2-search--inline .select2-search__field {
    background: transparent;
    border: none;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-appearance: textfield; }

.select2-container--default .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto; }

.select2-container--default .select2-results__option[role=group] {
    padding: 0; }

.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #999; }

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #ddd; }

.select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -1em;
    padding-left: 2em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -2em;
    padding-left: 3em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -3em;
    padding-left: 4em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -4em;
    padding-left: 5em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -5em;
    padding-left: 6em; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #5897fb;
    color: white; }

.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px; }

.select2-container--classic .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #aaa;
    border-radius: 4px;
    outline: 0;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(50%, white), to(#eeeeee));
    background-image: linear-gradient(to bottom, white 50%, #eeeeee 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0); }

.select2-container--classic .select2-selection--single:focus {
    border: 1px solid #5897fb; }

.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px; }

.select2-container--classic .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: 10px; }

.select2-container--classic .select2-selection--single .select2-selection__placeholder {
    color: #999; }

.select2-container--classic .select2-selection--single .select2-selection__arrow {
    background-color: #ddd;
    border: none;
    border-left: 1px solid #aaa;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #eeeeee), to(#cccccc));
    background-image: linear-gradient(to bottom, #eeeeee 50%, #cccccc 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0); }

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0; }

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left; }

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    border: none;
    border-right: 1px solid #aaa;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    left: 1px;
    right: auto; }

.select2-container--classic.select2-container--open .select2-selection--single {
    border: 1px solid #5897fb; }

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
    background: transparent;
    border: none; }

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px; }

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-image: -webkit-gradient(linear, left top, left bottom, from(white), color-stop(50%, #eeeeee));
    background-image: linear-gradient(to bottom, white 0%, #eeeeee 50%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0); }

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #eeeeee), to(white));
    background-image: linear-gradient(to bottom, #eeeeee 50%, white 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0); }

.select2-container--classic .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
    outline: 0; }

.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid #5897fb; }

.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
    list-style: none;
    margin: 0;
    padding: 0 5px; }

.select2-container--classic .select2-selection--multiple .select2-selection__clear {
    display: none; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
    color: #888;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #555; }

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    float: right;
    margin-left: 5px;
    margin-right: auto; }

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto; }

.select2-container--classic.select2-container--open .select2-selection--multiple {
    border: 1px solid #5897fb; }

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0; }

.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    outline: 0; }

.select2-container--classic .select2-search--inline .select2-search__field {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none; }

.select2-container--classic .select2-dropdown {
    background-color: white;
    border: 1px solid transparent; }

.select2-container--classic .select2-dropdown--above {
    border-bottom: none; }

.select2-container--classic .select2-dropdown--below {
    border-top: none; }

.select2-container--classic .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto; }

.select2-container--classic .select2-results__option[role=group] {
    padding: 0; }

.select2-container--classic .select2-results__option[aria-disabled=true] {
    color: grey; }

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
    background-color: #3875d7;
    color: white; }

.select2-container--classic .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px; }

.select2-container--classic.select2-container--open .select2-dropdown {
    border-color: #5897fb; }

/* ==========================================================================
   #SELECT 2 CUSTOM STYLES
   ========================================================================== */
/**
 * Instead of modifying `_select2.scss` directly, we add Dashboard's custom
 * styles for select2 plugin here.
 *
 * The code is a littly messy because the default select2 css styles is 
 * difficult to be overriden.
 *
 * This file depends on scss variables from `settings.select` file located in:
 * [ settings/_settings.select.scss ]
 */
.select2-container--default .select2-selection--single {
    background: #fff;
    background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#f2f4f7));
    background: linear-gradient(to bottom, #fff, #f2f4f7);
    height: 39px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    color: rgba(53, 64, 82, 0.5);
    font-size: 0.875rem;
    font-weight: 500;
    outline: 0; }

.select2-container--default .select2-selection--single[aria-expanded="true"],
.select2-container--default .select2-selection--multiple[aria-expanded="true"] {
    outline: none;
    border: 1px solid #2ea1f8;
    border-bottom-left-radius: 4px !important;
    border-bottom-right-radius: 4px !important; }

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 39px;
    padding-right: 0.9375rem;
    padding-left: 0.9375rem;
    color: rgba(53, 64, 82, 0.5); }

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 39px; }

.select2-results {
    border-radius: 4px;
    border: 1px solid #e6eaee; }

.select2-dropdown {
    margin: 5px 0;
    background-color: #fff;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px; }

.select2-container--open .select2-dropdown--below {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px; }

.select2-container--default .select2-results > .select2-results__options {
    border-radius: 4px; }

.select2-results__option {
    padding: 0.625rem 0.9375rem;
    font-size: 0.875rem;
    color: rgba(53, 64, 82, 0.5); }
.select2-results__option + .select2-results__option {
    border-top: 1px solid #e6eaee; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #F1F4F8;
    color: #2ea1f8; }

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #F1F4F8;
    color: #2ea1f8; }

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px; }
.select2-container--default .select2-selection--single .select2-selection__arrow::after {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    margin-left: 1.25rem;
    padding: 0.5rem 8px;
    border-color: inherit;
    color: inherit;
    font-family: "FontAwesome";
    content: "\f107"; }

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    display: none; }

/**
 * Search Field
 */
.select2-search--dropdown {
    position: relative;
    padding: 5px; }
.select2-search--dropdown::after {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    margin-left: 1.25rem;
    padding: 10px 18px;
    border-color: inherit;
    color: #CED0DA;
    font-family: "FontAwesome";
    content: "\f002"; }

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #e6eaee;
    border-radius: 4px;
    padding: 8px 15px;
    outline: none;
    color: #354052;
    font-size: 14px; }

/**
 * Multiple Selection
 */
.select2-container--default .select2-selection--multiple {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-bottom: 3px;
    background-color: #fff;
    border-color: #dfe3e9; }

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: 1px solid #2ea2f8; }

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0 3px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    margin-right: 3px;
    margin-top: 3px;
    padding: 4px 30px 4px 12px;
    background-color: #F1F4F8;
    border-color: #D5DCE6;
    color: #354052; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    position: absolute;
    right: 10px;
    color: #C7D2E0;
    font-size: 18px; }

/**
 * Disabled Select
 */
.select2-container--default.select2-container--disabled .select2-selection--single {
    border-color: #DFE3E9;
    background: #EAEEF1;
    background-color: #EAEEF1;
    color: rgba(53, 64, 82, 0.7); }
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered {
    color: rgba(53, 64, 82, 0.7); }

/*
 * The MIT License
 * Copyright (c) 2012 Matias Meno <<EMAIL>>
 */
@-webkit-keyframes passing-through {
    0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px); }
30%, 70% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px); }
100% {
    opacity: 0;
    -webkit-transform: translateY(-40px);
    transform: translateY(-40px); } }

@keyframes passing-through {
    0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px); }
30%, 70% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px); }
100% {
    opacity: 0;
    -webkit-transform: translateY(-40px);
    transform: translateY(-40px); } }

@-webkit-keyframes slide-in {
    0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px); }
30% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px); } }

@keyframes slide-in {
    0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px); }
30% {
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px); } }

@-webkit-keyframes pulse {
    0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
10% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1); }
20% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

@keyframes pulse {
    0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
10% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1); }
20% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

.dropzone, .dropzone * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box; }

.dropzone {
    min-height: 150px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    background: white;
    padding: 20px 20px; }

.dropzone.dz-clickable {
    cursor: pointer; }

.dropzone.dz-clickable * {
    cursor: default; }

.dropzone.dz-clickable .dz-message, .dropzone.dz-clickable .dz-message * {
    cursor: pointer; }

.dropzone.dz-started .dz-message {
    display: none; }

.dropzone.dz-drag-hover {
    border-style: solid; }

.dropzone.dz-drag-hover .dz-message {
    opacity: 0.5; }

.dropzone .dz-message {
    text-align: center;
    margin: 2em 0; }

.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 16px;
    min-height: 100px; }

.dropzone .dz-preview:hover {
    z-index: 1000; }

.dropzone .dz-preview:hover .dz-details {
    opacity: 1; }

.dropzone .dz-preview.dz-file-preview .dz-image {
    border-radius: 20px;
    background: #999;
    background: -webkit-gradient(linear, left top, left bottom, from(#eee), to(#ddd));
    background: linear-gradient(to bottom, #eee, #ddd); }

.dropzone .dz-preview.dz-file-preview .dz-details {
    opacity: 1; }

.dropzone .dz-preview.dz-image-preview {
    background: white; }

.dropzone .dz-preview.dz-image-preview .dz-details {
    -webkit-transition: opacity 0.2s linear;
    transition: opacity 0.2s linear; }

.dropzone .dz-preview .dz-remove {
    font-size: 14px;
    text-align: center;
    display: block;
    cursor: pointer;
    border: none; }

.dropzone .dz-preview .dz-remove:hover {
    text-decoration: underline; }

.dropzone .dz-preview:hover .dz-details {
    opacity: 1; }

.dropzone .dz-preview .dz-details {
    z-index: 20;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    font-size: 13px;
    min-width: 100%;
    max-width: 100%;
    padding: 2em 1em;
    text-align: center;
    color: rgba(0, 0, 0, 0.9);
    line-height: 150%; }

.dropzone .dz-preview .dz-details .dz-size {
    margin-bottom: 1em;
    font-size: 16px; }

.dropzone .dz-preview .dz-details .dz-filename {
    white-space: nowrap; }

.dropzone .dz-preview .dz-details .dz-filename:hover span {
    border: 1px solid rgba(200, 200, 200, 0.8);
    background-color: rgba(255, 255, 255, 0.8); }

.dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
    overflow: hidden;
    text-overflow: ellipsis; }

.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
    border: 1px solid transparent; }

.dropzone .dz-preview .dz-details .dz-filename span, .dropzone .dz-preview .dz-details .dz-size span {
    background-color: rgba(255, 255, 255, 0.4);
    padding: 0 0.4em;
    border-radius: 3px; }

.dropzone .dz-preview:hover .dz-image img {
    -webkit-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
    -webkit-filter: blur(8px);
    filter: blur(8px); }

.dropzone .dz-preview .dz-image {
    border-radius: 20px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10; }

.dropzone .dz-preview .dz-image img {
    display: block; }

.dropzone .dz-preview.dz-success .dz-success-mark {
    -webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
    animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1); }

.dropzone .dz-preview.dz-error .dz-error-mark {
    opacity: 1;
    -webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
    animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1); }

.dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -27px;
    margin-top: -27px; }

.dropzone .dz-preview .dz-success-mark svg, .dropzone .dz-preview .dz-error-mark svg {
    display: block;
    width: 54px;
    height: 54px; }

.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
    -webkit-transition: all 0.2s linear;
    transition: all 0.2s linear; }

.dropzone .dz-preview.dz-complete .dz-progress {
    opacity: 0;
    -webkit-transition: opacity 0.4s ease-in;
    transition: opacity 0.4s ease-in; }

.dropzone .dz-preview:not(.dz-processing) .dz-progress {
    -webkit-animation: pulse 6s ease infinite;
    animation: pulse 6s ease infinite; }

.dropzone .dz-preview .dz-progress {
    opacity: 1;
    z-index: 1000;
    pointer-events: none;
    position: absolute;
    height: 16px;
    left: 50%;
    top: 50%;
    margin-top: -8px;
    width: 80px;
    margin-left: -40px;
    background: rgba(255, 255, 255, 0.9);
    -webkit-transform: scale(1);
    border-radius: 8px;
    overflow: hidden; }

.dropzone .dz-preview .dz-progress .dz-upload {
    background: #333;
    background: -webkit-gradient(linear, left top, left bottom, from(#666), to(#444));
    background: linear-gradient(to bottom, #666, #444);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    -webkit-transition: width 300ms ease-in-out;
    transition: width 300ms ease-in-out; }

.dropzone .dz-preview.dz-error .dz-error-message {
    display: block; }

.dropzone .dz-preview.dz-error:hover .dz-error-message {
    opacity: 1;
    pointer-events: auto; }

.dropzone .dz-preview .dz-error-message {
    pointer-events: none;
    z-index: 1000;
    position: absolute;
    display: block;
    display: none;
    opacity: 0;
    -webkit-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
    border-radius: 8px;
    font-size: 13px;
    top: 130px;
    left: -10px;
    width: 140px;
    background: #be2626;
    background: -webkit-gradient(linear, left top, left bottom, from(#be2626), to(#a92222));
    background: linear-gradient(to bottom, #be2626, #a92222);
    padding: 0.5em 1.2em;
    color: white; }

.dropzone .dz-preview .dz-error-message:after {
    content: '';
    position: absolute;
    top: -6px;
    left: 64px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #be2626; }

/* ==========================================================================
   #DROPZONE CUSTOM STYLES
   ========================================================================== */
/**
 * Instead of modifying `_dropzone.scss` directly, we add Dashboard's custom
 * styles for dropzone plugin here.
 */
.dropzone {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: #fff;
    color: #7f8fa4;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    padding: 5px;
    -webkit-transition: border-color 0.3s;
    transition: border-color 0.3s; }
.dropzone:hover {
    border-color: #2ea1f8; }

.dropzone .dz-message {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    position: absolute;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    margin: auto;
    border-radius: 4px;
    border: 2px dashed rgba(206, 208, 218, 0.5); }

.dz-icon {
    display: block;
    margin-bottom: 10px;
    font-size: 30px;
    color: #E2E3E9; }

/* ==========================================================================
   #DATATABLES CUSTOM STYLES
   ========================================================================== */
/**
 * Dashboard's custom styles for datatables plugin.
 */
.dataTable .sorting,
.dataTable .sorting_asc,
.dataTable .sorting_desc {
    position: relative;
    cursor: pointer; }
.dataTable .sorting:focus,
.dataTable .sorting_asc:focus,
.dataTable .sorting_desc:focus {
    outline: none; }

.dataTables_filter {
    margin-bottom: 0.9375rem;
    color: #354052; }
.dataTables_filter input {
    display: inline-block;
    width: 100%;
    max-width: 230px;
    margin-left: 0.625rem;
    padding: 0.59375rem 0.9375rem;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    background-color: #fff;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 400;
    resize: none; }
.dataTables_filter input:focus {
    border-color: #2ea1f8;
    outline: none; }

.dataTable thead .sorting:before,
.dataTable thead .sorting:after,
.dataTable thead .sorting_asc:before,
.dataTable thead .sorting_asc:after,
.dataTable thead .sorting_desc:before,
.dataTable thead .sorting_desc:after {
    position: absolute;
    display: inline-block;
    content: "\f0d8";
    color: #7f8fa4;
    font-family: "FontAwesome"; }

.dataTable thead .sorting:before,
.dataTable thead .sorting_asc:before,
.dataTable thead .sorting_desc:before {
    right: 10px;
    top: 25%; }

.dataTable thead .sorting:after,
.dataTable thead .sorting_asc:after,
.dataTable thead .sorting_desc:after {
    right: 10px;
    bottom: 25%;
    content: "\f0d7"; }

.dataTable thead.c-table__head--slim .sorting:before,
.dataTable thead.c-table__head--slim .sorting_asc:before,
.dataTable thead.c-table__head--slim .sorting_desc:before {
    right: 10px;
    top: 6px; }

.dataTable thead.c-table__head--slim .sorting:after,
.dataTable thead.c-table__head--slim .sorting_asc:after,
.dataTable thead.c-table__head--slim .sorting_desc:after {
    right: 10px;
    bottom: 6px;
    content: "\f0d7"; }

.dataTable thead .sorting:last-child:after,
.dataTable thead .sorting_asc:last-child:after,
.dataTable thead .sorting_desc:last-child:after,
.dataTable thead .sorting:last-child:before,
.dataTable thead .sorting_asc:last-child:before,
.dataTable thead .sorting_desc:last-child:before {
    right: 20px; }

.dataTable thead .sorting_asc:after {
    opacity: 0.5; }

.dataTable thead .sorting_desc:before {
    opacity: 0.5; }

.dataTables_info {
    font-size: 0.875rem;
    margin-top: 0.9375rem;
    float: left;
    color: #7f8fa4; }

.dataTables_empty {
    padding: 1.875rem;
    color: #354052; }

.dataTables_paginate {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 0.9375rem;
    float: right; }
.dataTables_paginate span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
.dataTables_paginate .paginate_button {
    display: block;
    height: 2.1875rem;
    /* [1] */
    padding: 0 16px;
    border: 1px solid #e6eaee;
    background-color: #fff;
    color: rgba(53, 64, 82, 0.5);
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 2.1875rem;
    /* [1] */
    text-align: center; }
.dataTables_paginate .paginate_button:hover {
    color: #354052;
    text-decoration: none; }
.dataTables_paginate .paginate_button.current {
    color: #354052; }
.dataTables_paginate .paginate_button + .paginate_button {
    border-left: 0; }
.dataTables_paginate .paginate_button.previous {
    border-radius: 4px;
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.dataTables_paginate .paginate_button.next {
    border-radius: 4px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }

/*!
 * Datepicker v0.6.4
 * https://github.com/fengyuanchen/datepicker
 *
 * Copyright (c) 2014-2017 Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2017-11-24T14:38:19.628Z
 */
.datepicker-container {
    background-color: #fff;
    direction: ltr;
    font-size: 12px;
    left: 0;
    line-height: 30px;
    position: fixed;
    top: 0;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 210px;
    z-index: -1;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none; }

.datepicker-container::before,
.datepicker-container::after {
    border: 5px solid transparent;
    content: " ";
    display: block;
    height: 0;
    position: absolute;
    width: 0; }

.datepicker-dropdown {
    border: 1px solid #ccc;
    -webkit-box-shadow: 0 3px 6px #ccc;
    box-shadow: 0 3px 6px #ccc;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    position: absolute;
    z-index: 1; }

.datepicker-inline {
    position: static; }

.datepicker-top-left,
.datepicker-top-right {
    border-top-color: #39f; }

.datepicker-top-left::before,
.datepicker-top-left::after,
.datepicker-top-right::before,
.datepicker-top-right::after {
    border-top: 0;
    left: 10px;
    top: -5px; }

.datepicker-top-left::before,
.datepicker-top-right::before {
    border-bottom-color: #39f; }

.datepicker-top-left::after,
.datepicker-top-right::after {
    border-bottom-color: #fff;
    top: -4px; }

.datepicker-bottom-left,
.datepicker-bottom-right {
    border-bottom-color: #39f; }

.datepicker-bottom-left::before,
.datepicker-bottom-left::after,
.datepicker-bottom-right::before,
.datepicker-bottom-right::after {
    border-bottom: 0;
    bottom: -5px;
    left: 10px; }

.datepicker-bottom-left::before,
.datepicker-bottom-right::before {
    border-top-color: #39f; }

.datepicker-bottom-left::after,
.datepicker-bottom-right::after {
    border-top-color: #fff;
    bottom: -4px; }

.datepicker-top-right::before,
.datepicker-top-right::after,
.datepicker-bottom-right::before,
.datepicker-bottom-right::after {
    left: auto;
    right: 10px; }

.datepicker-panel > ul {
    margin: 0;
    padding: 0;
    width: 102%; }

.datepicker-panel > ul::before,
.datepicker-panel > ul::after {
    content: " ";
    display: table; }

.datepicker-panel > ul::after {
    clear: both; }

.datepicker-panel > ul > li {
    background-color: #fff;
    cursor: pointer;
    float: left;
    height: 30px;
    list-style: none;
    margin: 0;
    padding: 0;
    text-align: center;
    width: 30px; }

.datepicker-panel > ul > li:hover {
    background-color: #e5f2ff; }

.datepicker-panel > ul > li.muted,
.datepicker-panel > ul > li.muted:hover {
    color: #999; }

.datepicker-panel > ul > li.highlighted {
    background-color: #e5f2ff; }

.datepicker-panel > ul > li.highlighted:hover {
    background-color: #cce5ff; }

.datepicker-panel > ul > li.picked,
.datepicker-panel > ul > li.picked:hover {
    color: #39f; }

.datepicker-panel > ul > li.disabled,
.datepicker-panel > ul > li.disabled:hover {
    background-color: #fff;
    color: #ccc;
    cursor: default; }

.datepicker-panel > ul > li.disabled.highlighted,
.datepicker-panel > ul > li.disabled:hover.highlighted {
    background-color: #e5f2ff; }

.datepicker-panel > ul > li[data-view="years prev"],
.datepicker-panel > ul > li[data-view="year prev"],
.datepicker-panel > ul > li[data-view="month prev"],
.datepicker-panel > ul > li[data-view="years next"],
.datepicker-panel > ul > li[data-view="year next"],
.datepicker-panel > ul > li[data-view="month next"],
.datepicker-panel > ul > li[data-view="next"] {
    font-size: 18px; }

.datepicker-panel > ul > li[data-view="years current"],
.datepicker-panel > ul > li[data-view="year current"],
.datepicker-panel > ul > li[data-view="month current"] {
    width: 150px; }

.datepicker-panel > ul[data-view="years"] > li,
.datepicker-panel > ul[data-view="months"] > li {
    height: 52.5px;
    line-height: 52.5px;
    width: 52.5px; }

.datepicker-panel > ul[data-view="week"] > li,
.datepicker-panel > ul[data-view="week"] > li:hover {
    background-color: #fff;
    cursor: default; }

.datepicker-hide {
    display: none; }

/* ==========================================================================
   #DATEPICKER CUSTOM STYLES
   ========================================================================== */
/**
 * Instead of modifying `_datepicker.scss` directly, we add Dashboard's custom
 * styles for datepicker plugin here.
 *
 * This file  isn't for asscoited with `fullcalendar` plugin, `fullcalender` has 
 * its own custom styles in `vendor` folder.
 *
 * SCSS variables used in this file can be found in: settings/_settings.calendar.scss
 */
.datepicker-container {
    width: 224px;
    padding: 0.625rem; }

.datepicker-panel > ul > li[data-view="month current"],
.datepicker-panel > ul > li[data-view="year current"],
.datepicker-panel > ul > li[data-view="years current"] {
    width: 160px;
    border-radius: 0; }

.datepicker-panel > ul[data-view=week] > li,
.datepicker-panel > ul[data-view=week] > li:hover {
    color: rgba(53, 64, 82, 0.5); }

.datepicker-panel > ul > li.picked,
.datepicker-panel > ul > li.picked:hover {
    color: #1a91eb; }

.datepicker-dropdown {
    background: #fff;
    border: 1px solid #dfe3e9;
    -webkit-box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.08);
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    overflow: hidden; }

.datepicker-panel > ul > li {
    border-radius: 100%;
    font-size: 0.875rem;
    width: 32px;
    height: 32px; }

.datepicker-panel > ul > li:hover {
    background-color: #f5f7f8; }

.datepicker-panel > ul > li.highlighted {
    background-color: #1a91eb;
    color: #fff;
    border-radius: 100%; }

.datepicker-panel > ul > li[data-view="month"] {
    font-size: 0.875rem; }

.jqvmap-label {
    position: absolute;
    display: none;
    border-radius: 3px;
    background: #292929;
    color: white;
    font-family: sans-serif, Verdana;
    font-size: smaller;
    padding: 3px;
    pointer-events: none; }

.jqvmap-pin {
    pointer-events: none; }

.jqvmap-zoomin, .jqvmap-zoomout {
    position: absolute;
    left: 10px;
    border-radius: 3px;
    background: #000000;
    padding: 3px;
    color: white;
    width: 10px;
    height: 10px;
    cursor: pointer;
    line-height: 10px;
    text-align: center; }

.jqvmap-zoomin {
    top: 10px; }

.jqvmap-zoomout {
    top: 30px; }

.jqvmap-region {
    cursor: pointer; }

.jqvmap-ajax_response {
    width: 100%;
    height: 500px; }

/* ==========================================================================
   #JQV MAP CUSTOM
   ========================================================================== */
/*
 * A quick fix for jqvmap pins (not positioned properly)
 */
.jqvmap-pin {
    -webkit-transform: translate(-3200%, -1050%);
    transform: translate(-3200%, -1050%); }
@media (max-width: 992px) {
    .jqvmap-pin {
        -webkit-transform: translate(-300%, -1050%);
        transform: translate(-300%, -1050%); } }
@media (max-width: 768px) {
    .jqvmap-pin {
        -webkit-transform: translate(-500%, -1500%);
        transform: translate(-500%, -1500%); } }

/* ==========================================================================
   #PAGE OBJECT
   ========================================================================== */
/**
 * Use `o-page` to strucutre your page layout into sidebar, main content.
 */
.o-page {
    position: relative;
    height: 100%; }

/**
 * Used with `o-page__card` for better alignment.
 */
.o-page--center {
    padding-top: 6.25rem; }

/**
 * 1. Control the width of sidebar.
 * 2. Sidebar always on the top.
 */
.o-page__sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    width: 250px;
    /* [1] */
    -webkit-transition: -webkit-transform .2s;
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s;
    z-index: 300;
    /* [2] */
    /**
           * Hide sidebar once viewport is lower than $breakpoint-desktop (992px)
           *
           * 1. Increase the width of sidebar on small screens.
           */ }
@media (max-width: 992px) {
    .o-page__sidebar {
        width: 300px;
        /* [1] */
        -webkit-transform: translateX(-300px);
        transform: translateX(-300px); } }
.o-page__sidebar.is-visible {
    -webkit-transform: translateX(0);
    transform: translateX(0); }

/**
 * Page content is pushed to the right to provide space for the sidebar.
 *
 * 1. Must equal the width of the `o-sidebar`.
 */
.o-page__content {
    margin-left: 250px;
    /* [1] */
    overflow: hidden; }
@media (max-width: 992px) {
    .o-page__content {
        margin-left: 0; } }
.o-page__content.is-pushed {
    margin-left: 250px; }

/** 
 * Page Card
 *
 * A centered page card is used when you want the card to be centered and 
 * provide a little space at the top & bottom e.g. login, 404 pages.
 */
.o-page__card {
    width: 450px;
    margin: 0 auto 3.125rem; }
@media (max-width: 768px) {
    .o-page__card {
        width: 95%; } }

/** 
 * Horizontal Page Card
 *
 * Used with login-2 page.
 */
.o-page__card--horizontal {
    width: 800px;
    margin: 0 auto 3.125rem; }
@media (max-width: 992px) {
    .o-page__card--horizontal {
        width: 95%; } }

/* ==========================================================================
   #MEDIA OBJECT
   ========================================================================== */
.o-media {
    display: block; }
.o-media::after {
    display: table !important;
    clear: both !important;
    content: "" !important; }

/**
 * 1. Spacing between the image and the text should be declared through
 *    the component, usually using a `spacing` utility class. 
 */
.o-media__img {
    /* [1] */
    float: left; }
.o-media__img > img {
    display: block; }

.o-media__body {
    display: block;
    overflow: hidden; }
.o-media__body,
.o-media__body > :last-child {
    margin-bottom: 0; }

/* ==========================================================================
   #LINE OBJECT
   ========================================================================== */
/**
 * The line object is used to align elemnts on one line, obviously.
 */
.o-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between; }

/* ==========================================================================
   #RATIO OBJECT
   ========================================================================== */
/**
 * Ratio object is used to keep media (e.g. images, videos) in
 * their correct aspect ratios.
 *
 * USAGE: 
 * <div class="o-ratio o-ratio--16:9">
 *     <iframe src"/"></iframe>
 * </div>
 */
/**
 * 1. Default cropping is a 1:1 ratio (i.e. a perfect square)
 */
.o-ratio {
    display: block;
    position: relative; }
.o-ratio::before {
    display: block;
    width: 100%;
    padding-bottom: 100%;
    /* [1] */
    content: ""; }

.o-ratio__content,
.o-ratio > iframe,
.o-ratio > embed,
.o-ratio > object {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%; }

.o-ratio--16\:9::before {
    padding-bottom: 56.25%; }

.o-ratio--4\:3::before {
    padding-bottom: 75%; }

.o-ratio--2\:1::before {
    padding-bottom: 50%; }

/* ==========================================================================
   #ALERTS
   ========================================================================== */
.c-alert {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    width: 100%;
    margin: 0 0 0.9375rem 0;
    padding: 0.75rem 2rem 0.75rem 0.75rem;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    border-radius: 4px;
    color: #fff; }
.c-alert .c-close {
    position: absolute;
    top: 0.75rem;
    right: 1rem; }
.c-alert.fade {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    opacity: 0; }
.c-alert.fade.show {
    opacity: 1; }

/**
 * 1. Align icon with text properly.
 */
.c-alert__icon {
    position: relative;
    bottom: -4px;
    /* [1] */
    margin-right: 0.625rem;
    font-size: 1.125rem;
    opacity: 0.7; }

/* Style variants
   ========================================================================== */
.c-alert--success {
    background-color: #1bb934; }

.c-alert--info {
    background-color: #2ea1f8; }

.c-alert--warning {
    background-color: #fd9a18; }

.c-alert--danger {
    background-color: #ed1c24; }

/* ==========================================================================
   #AVATARS
   ========================================================================== */
/**
 * 1. Enable avatars to act like a dropdown toggle.
 * 2. Small arrow (icon) used in avatars with dropdowns.
 * 3. Remove default link decoration when avatar is used as a dropdown toggle.
 */
.c-avatar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0;
    padding: 0;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
.c-avatar.has-dropdown {
    /* [1] */
    cursor: pointer;
    overflow: visible; }
.c-avatar.has-dropdown::after {
    /* [2] */
    display: block;
    padding-left: 10px;
    color: #7f8fa4;
    font-family: "FontAwesome";
    content: "\f107"; }
.c-avatar.has-dropdown:hover {
    text-decoration: none;
    /* [3] */ }
.c-avatar .c-avatar__img {
    width: 70px;
    height: 70px; }

.c-avatar__img {
    border-radius: 100%;
    overflow: hidden; }

/* Size variants
   ========================================================================== */
.c-avatar--xsmall .c-avatar__img {
    width: 36px;
    height: 36px; }

.c-avatar--small .c-avatar__img {
    width: 50px;
    height: 50px; }

.c-avatar--medium .c-avatar__img {
    width: 60px;
    height: 60px; }

.c-avatar--large .c-avatar__img {
    width: 90px;
    height: 90px; }

.c-avatar--xlarge .c-avatar__img {
    width: 110px;
    height: 110px; }

.c-avatar--super .c-avatar__img {
    width: 130px;
    height: 130px; }

/* ==========================================================================
   #BADGES
   ========================================================================== */
.c-badge {
    display: inline-block;
    margin: 0;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    color: #fff;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    /**
           * Badges with icons
           */ }
.c-badge > i {
    margin-right: 0.3125rem; }

.c-badge--small {
    padding: 0.25rem 0.625rem;
    font-size: 10px; }

.c-badge--xsmall {
    padding: 0.125rem 0.3125rem;
    font-size: 10px; }

/* Style variants
   ========================================================================== */
.c-badge--success {
    background-color: #1bb934; }

.c-badge--info {
    background-color: #2ea1f8; }

.c-badge--warning {
    background-color: #fd9a18; }

.c-badge--danger {
    background-color: #ed1c24; }

.c-badge--primary {
    background-color: #475364; }

.c-badge--secondary {
    background-color: #b7c0cd;
    color: #fff; }

/* ==========================================================================
   #BREADCRUMB
   ========================================================================== */
.c-breadcrumb {
    display: block;
    margin: 0;
    padding: 0; }

.c-breadcrumb__item {
    display: inline-block;
    color: #848C98; }
.c-breadcrumb__item > a {
    color: inherit; }
.c-breadcrumb__item:not(:last-child)::after {
    margin: 0 0.625rem;
    color: #a1a7aF;
    content: "/"; }
.c-breadcrumb__item.is-active {
    color: #354052; }

/* ==========================================================================
   #BUTTON GROUP
   ========================================================================== */
/**
 * A series of buttons grouped together on a single line.
 */
.c-btn-group {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; }
.c-btn-group > .c-btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.c-btn-group > .c-btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
.c-btn-group > .c-btn:not(:first-child):not(:last-child) {
    border-radius: 0; }
.c-btn-group > .c-btn + .c-btn {
    border-left: 0; }

/**
 * Force button group to take full width.
 */
.c-btn-group--fullwidth {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%; }
.c-btn-group--fullwidth > .c-btn {
    width: 100%; }

/* ==========================================================================
   #BUTTONS
   ========================================================================== */
/**
 * 1. Apply (c-btn--info) background if `c-btn--{style}` class isn't specified.
 * 2. Align icon (if placed inside button) with text properly.
 * 3. Add dropdown icon/arrow to button with `has-dropdown` class, it depends 
 *    on (FontAwesome) icon font so make sure to modify it if you changed your 
 *    base icon font.
 * 4. Use `disabled` property on <button> elements, and `is-disabled` class on
 *    <a> elements as they don't support `disabled` property.
 */
.c-btn {
    display: inline-block;
    position: relative;
    max-width: 100%;
    margin: 0;
    padding: 0.5rem 1.25rem;
    -webkit-transition: all 0.15s ease-in-out;
    transition: all 0.15s ease-in-out;
    border: 1px solid;
    border-radius: 4px;
    border-color: #1a91eb;
    /* [1] */
    color: #fff;
    /* [1] */
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    overflow: hidden;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea);
    /* [1] */ }
.c-btn:hover, .c-btn:focus {
    outline: 0;
    color: #fff;
    text-decoration: none; }
.c-btn i {
    position: relative;
    bottom: -1px;
    /* [2] */
    font-size: 16px; }
.c-btn.has-dropdown {
    position: relative;
    padding-right: 2.5rem; }
.c-btn.has-dropdown::after {
    /* [3] */
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    margin-left: 1.25rem;
    padding: 0.5rem 8px;
    border-left: 1px solid;
    border-color: inherit;
    color: inherit;
    font-family: "FontAwesome";
    content: "\f107"; }
.c-btn.is-disabled, .c-btn:disabled {
    /* [4] */
    border-color: #e6eaee;
    background: #f5f8fa;
    color: rgba(53, 64, 82, 0.5); }
.c-btn.is-disabled:hover, .c-btn.is-disabled:focus, .c-btn:disabled:hover, .c-btn:disabled:focus {
    border-color: #e6eaee;
    background: #f5f8fa;
    color: rgba(53, 64, 82, 0.5);
    cursor: not-allowed; }

.c-btn--fullwidth {
    width: 100%; }

/**
 * 1. Reduce spacing between the icon and text in small buttons.
 */
.c-btn--small {
    padding: 0.25rem 1.125rem;
    font-size: 0.75rem; }
.c-btn--small i {
    margin-right: 5px;
    /* [1] */ }

.c-btn--large {
    padding: 0.5rem 1.875rem;
    font-size: 1rem; }

/* Style variants
   ========================================================================== */
/**
 * Buttons have a simple, nice gradient on the background, we use 
 * sass's `darken()` method to generate a dark shade of the requested color, 
 * then we use our `linear-gradient` mixin to generate the gradient background.
 */
.c-btn--primary {
    background: #475364;
    background: -webkit-gradient(linear, left top, left bottom, from(#475364), to(#273142));
    background: linear-gradient(to bottom, #475364, #273142);
    border-color: #354052; }
.c-btn--primary:hover {
    background: #3f4958;
    background: -webkit-gradient(linear, left top, left bottom, from(#3f4958), to(#151b24));
    background: linear-gradient(to bottom, #3f4958, #151b24); }
.c-btn--primary:focus {
    background: #3a4452;
    background: -webkit-gradient(linear, left top, left bottom, from(#3a4452), to(#12161e));
    background: linear-gradient(to bottom, #3a4452, #12161e); }

.c-btn--secondary {
    background: #fff;
    background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#f2f4f7));
    background: linear-gradient(to bottom, #fff, #f2f4f7);
    border-color: #dfe3e9;
    color: #354052; }
.c-btn--secondary:hover {
    background: #fcfcfc;
    background: -webkit-gradient(linear, left top, left bottom, from(#fcfcfc), to(#eceff3));
    background: linear-gradient(to bottom, #fcfcfc, #eceff3);
    color: #354052; }
.c-btn--secondary:focus {
    background: #fafafa;
    background: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#e9ecf1));
    background: linear-gradient(to bottom, #fafafa, #e9ecf1);
    color: #354052; }

.c-btn--info {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea);
    border-color: #1a91eb; }
.c-btn--info:hover {
    background: #1a98f7;
    background: -webkit-gradient(linear, left top, left bottom, from(#1a98f7), to(#1485db));
    background: linear-gradient(to bottom, #1a98f7, #1485db); }
.c-btn--info:focus {
    background: #1094f7;
    background: -webkit-gradient(linear, left top, left bottom, from(#1094f7), to(#137fd1));
    background: linear-gradient(to bottom, #1094f7, #137fd1); }

.c-btn--success {
    background: #39b54a;
    background: -webkit-gradient(linear, left top, left bottom, from(#39b54a), to(#34aa44));
    background: linear-gradient(to bottom, #39b54a, #34aa44);
    border-color: #249533; }
.c-btn--success:hover {
    background: #34a544;
    background: -webkit-gradient(linear, left top, left bottom, from(#34a544), to(#2f9a3e));
    background: linear-gradient(to bottom, #34a544, #2f9a3e); }
.c-btn--success:focus {
    background: #329e40;
    background: -webkit-gradient(linear, left top, left bottom, from(#329e40), to(#2d933b));
    background: linear-gradient(to bottom, #329e40, #2d933b); }

.c-btn--fancy {
    background: #886ce6;
    background: -webkit-gradient(linear, left top, left bottom, from(#886ce6), to(#7d5be2));
    background: linear-gradient(to bottom, #886ce6, #7d5be2);
    border-color: #7d5be2; }
.c-btn--fancy:hover {
    background: #7a5be3;
    background: -webkit-gradient(linear, left top, left bottom, from(#7a5be3), to(#6f4adf));
    background: linear-gradient(to bottom, #7a5be3, #6f4adf); }
.c-btn--fancy:focus {
    background: #7352e2;
    background: -webkit-gradient(linear, left top, left bottom, from(#7352e2), to(#6841dd));
    background: linear-gradient(to bottom, #7352e2, #6841dd); }

.c-btn--warning {
    background: #fd9a18;
    background: -webkit-gradient(linear, left top, left bottom, from(#fd9a18), to(#f16911));
    background: linear-gradient(to bottom, #fd9a18, #f16911);
    border-color: #f16911; }
.c-btn--warning:hover {
    background: #fd9104;
    background: -webkit-gradient(linear, left top, left bottom, from(#fd9104), to(#e0600d));
    background: linear-gradient(to bottom, #fd9104, #e0600d); }
.c-btn--warning:focus {
    background: #f48c02;
    background: -webkit-gradient(linear, left top, left bottom, from(#f48c02), to(#d75c0d));
    background: linear-gradient(to bottom, #f48c02, #d75c0d); }

.c-btn--danger {
    background: #f95359;
    background: -webkit-gradient(linear, left top, left bottom, from(#f95359), to(#d35847));
    background: linear-gradient(to bottom, #f95359, #d35847);
    border-color: #d35847; }
.c-btn--danger:hover {
    background: #f83f46;
    background: -webkit-gradient(linear, left top, left bottom, from(#f83f46), to(#cf4937));
    background: linear-gradient(to bottom, #f83f46, #cf4937); }
.c-btn--danger:focus {
    background: #f8353c;
    background: -webkit-gradient(linear, left top, left bottom, from(#f8353c), to(#cb4331));
    background: linear-gradient(to bottom, #f8353c, #cb4331); }

/* ==========================================================================
   #CARD
   ========================================================================== */
/**
 * A simple component to apply card-like style, used with genearal widgets that 
 * have sub-components in it.
 */
.c-card {
    position: relative;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

/**
 * 1. Display a scrollbar if the content of the card is larger than the 
 *    card's width.
 */
.c-card--responsive {
    overflow: auto;
    /* [1] */ }

/**
 * 1. Align header's content vertically in the center
 */
.c-card__header {
    padding: 1.5625rem 1.875rem;
    border-bottom: 1px solid #e6eaee;
    background-color: #fafbfc; }

/**
 * Remove default background-color of card's header
 */
.c-card__header--transparent {
    background-color: transparent; }

.c-card__title {
    margin: 0;
    font-size: 1.125rem; }

.c-card__meta {
    color: #7f8fa4;
    font-size: 0.75rem; }

.c-card__icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    top: -35px;
    right: 0;
    left: 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    margin: 0 auto;
    border-radius: 100%;
    color: #fff;
    font-size: 1.75rem;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }
.c-card__icon:hover i {
    color: inherit;
    text-decoration: none; }

.c-card__body {
    padding: 1.875rem; }

/* ==========================================================================
   #CHOICES (Checkboxes & Radios)
   ========================================================================== */
/**
 * Checkboxes & Radios are controlled with a simple class called `c-choice`
 * because they are similar in styling, this class is used to share same 
 * styles between them.
 *
 * EXAMPLE: 
 *
 * <div class="c-choice c-choice--checkbox">
 *     <input class="c-choice__input" id="custom-checkbox" type="checkbox">
 *     <label class="c-choice__label" for="custom-checkbox">Checkbox 1</label>
 * </div>
 */
/**
 * 1. Get rid of white space cause by using `inline-flex`.
 * 
 * Read More: https://goo.gl/ajXW52
 */
.c-choice {
    margin: 0 0 0.9375rem 0;
    color: #354052;
    font-size: 0;
    /* [1] */
    font-weight: 400;
    /**
           * Disabled state
           *
           * NOTE: `disabled` attribute must be added to <input>
           */ }
.c-choice.is-disabled .c-choice__label {
    color: rgba(53, 64, 82, 0.5);
    cursor: default; }
.c-choice.is-disabled .c-choice__label::before {
    border-color: #dfe3e9;
    background-color: #e6eaee; }

/**
 * 1. Hide default input and create a customized input.
 * 2. Align the customized input with label.
 */
.c-choice__input {
    /* [1] */
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    white-space: nowrap;
    overflow: hidden;
    clip: rect(0 0 0 0);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%); }
.c-choice__input + .c-choice__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #354052;
    font-size: 0.875rem;
    cursor: pointer; }
.c-choice__input + .c-choice__label::before {
    display: inline-block;
    position: relative;
    top: -1px;
    /* [2] */
    width: 16px;
    height: 16px;
    margin: 0 15px 0 0;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #bbc5d5;
    background-color: #fff;
    color: #fff;
    line-height: 15px;
    text-align: center;
    content: "";
    visibility: visible;
    /* [2] */ }

/**
 * Checkboxes
 */
.c-choice--checkbox .c-choice__input + .c-choice__label::before {
    border-radius: 4px; }

.c-choice--checkbox .c-choice__input:checked + .c-choice__label::before {
    border-color: #34aa44;
    background-color: #39b54a;
    font-family: "FontAwesome";
    font-size: 10px;
    content: "\f00c"; }

/**
 * Radio buttons
 */
.c-choice--radio .c-choice__input + .c-choice__label::before {
    border-radius: 100%; }

.c-choice--radio .c-choice__input:checked + .c-choice__label::before {
    border: 4px solid #39b54a;
    background-color: #fff;
    -webkit-box-shadow: 0 0 0 1px #34aa44;
    box-shadow: 0 0 0 1px #34aa44; }

/* ==========================================================================
   #CLOSE
   ========================================================================== */
/**
 * A small button for closing components, used with closable components 
 * like alerts, modals. 
 */
.c-close {
    padding: 0;
    border: 0;
    background-color: transparent;
    color: #fff;
    font-size: 1.625rem;
    line-height: 1;
    opacity: 0.7;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none; }
.c-close:hover, .c-close:focus {
    opacity: 1; }
.c-close:focus {
    outline: 0; }

/* ==========================================================================
   #DROPDOWN
   ========================================================================== */
/**
 * A wrapper for the dropdown
 */
.c-dropdown {
    display: inline-block;
    position: relative; }

/**
 * Dropdown Menu
 * 
 * 1. No matter what size is the dropdown toggle `dropdown-toggle` (a class from
 *    bootstrap), dropdown menu should be positioned properly.
 */
.c-dropdown__menu {
    position: absolute;
    top: 100%;
    /* [1] */
    right: 0;
    width: 9.375rem;
    margin: 5px 0 0;
    -webkit-transition: visibility 0.3s ease, opacity 0.3s ease;
    transition: visibility 0.3s ease, opacity 0.3s ease;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    background-color: #fff;
    text-align: left;
    -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    z-index: 400; }
.c-dropdown .c-dropdown__menu,
.dropdown .c-dropdown__menu {
    opacity: 0;
    visibility: hidden; }
.c-dropdown.show .c-dropdown__menu,
.dropdown.show .c-dropdown__menu {
    opacity: 1;
    visibility: visible; }

/**
 * A larger dropdown menu for dropdowns used in navbar.
 */
.c-dropdown__menu--large {
    width: 18.75rem; }
.c-dropdown__menu--large .c-dropdown__item {
    padding: 0.625rem 0.9375rem; }

/**
 * 1. Remove border from last item to prevent it from collapsing with dropdown
 *    menu's last border.
 */
.c-dropdown__item {
    display: block;
    padding: 0.5rem 0.9375rem;
    border-bottom: 1px solid #dfe3e9;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 400; }
.c-dropdown__item:hover {
    background-color: #f2f4f7;
    color: #2ea1f8;
    text-decoration: none; }
.c-dropdown__item:last-child {
    border-bottom: 0;
    /* [1] */ }

/* ==========================================================================
   #FIELD
   ========================================================================== */
/**
 * Form field `c-field` component is used as a wrapper for more complex inputs
 * that contain an icon, addon, form feedback and form message.
 *
 * 1. Take up (full width - width of field addon).
 */
.c-field {
    position: relative;
    width: 100%; }
.c-field.has-addon-left, .c-field.has-addon-right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap; }
.c-field.has-addon-left > .c-input, .c-field.has-addon-right > .c-input {
    -ms-flex-preferred-size: calc(100% - $field-addon-width);
    flex-basis: calc(100% - $field-addon-width);
    /* [1] */ }
.c-field.has-addon-left > .c-field__addon {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.c-field.has-addon-left > .c-input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
.c-field.has-addon-right > .c-field__addon {
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
.c-field.has-addon-right > .c-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.c-field.has-icon-left > .c-field__icon, .c-field.has-icon-right > .c-field__icon {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: #CED0DA;
    font-size: 1.125rem; }
.c-field.has-icon-left > .c-input {
    padding-left: 40px; }
.c-field.has-icon-left > .c-field__icon {
    left: 15px; }
.c-field.has-icon-right > .c-input {
    padding-right: 40px; }
.c-field.has-icon-right > .c-field__icon {
    right: 15px; }

/**
 * Inline fields don't take up whole width 
 *
 * Used with search input in the navbar & toolbar.
 */
.c-field--inline {
    width: 270px; }
@media (max-width: 768px) {
    .c-field--inline {
        width: 100%; } }

/**
 * Labels are used with inputs, always use labels with inputs for better 
 * accesibility. Hide labels using `u-hidden-visually` to display it for 
 * screen readers.
 *
 * USAGE: <label class="c-field__label u-hidden-visually"></label>
 */
.c-field__label {
    display: block;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    margin: 0 0 0.3125rem 0;
    color: #7f8fa4;
    font-size: 0.875rem;
    font-weight: 400; }

/**
 * Addon is an icon placed in box next to input, used as a visual indicator
 */
.c-field__addon {
    background: #FFF;
    background: -webkit-gradient(linear, left top, left bottom, from(#FFF), to(#F2F4F7));
    background: linear-gradient(to bottom, #FFF, #F2F4F7);
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-preferred-size: 2.625rem;
    flex-basis: 2.625rem;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    color: #CED0DA;
    font-size: 1.125rem;
    text-align: center; }

/**
 * Field message used as a helper note or feedback.
 */
.c-field__message {
    display: inline-block;
    margin: 0 0.3125rem 0 0;
    padding: 0;
    color: rgba(53, 64, 82, 0.5);
    font-size: 0.75rem; }
.c-field__message > i {
    margin-right: 0.3125rem; }

/* ==========================================================================
   #ICONS
   ========================================================================== */
/**
 * Add rounded/circular box background to any icon.
 *
 * USAGE: 
 *      
 * <div class="c-icon c-icon--large c-icon--success">
 *     <i class="fa fa-facebook"></i>
 * </div>
 */
.c-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 4px;
    background: #2ea1f8;
    color: #fff;
    font-size: 1.25rem; }
.c-icon:hover {
    color: #fff;
    text-decoration: none; }

.c-icon--circle {
    border-radius: 100%; }

/* Size variants
   ========================================================================== */
.c-icon--large {
    width: 60px;
    height: 60px;
    font-size: 1.5rem; }

.c-icon--small {
    width: 40px;
    height: 40px;
    font-size: 1rem; }

/* Style variants
   ========================================================================== */
.c-icon--success {
    background: #39b54a;
    background: -webkit-gradient(linear, left top, left bottom, from(#39b54a), to(#34aa44));
    background: linear-gradient(to bottom, #39b54a, #34aa44); }

.c-icon--info {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }

.c-icon--warning {
    background: #fd9a18;
    background: -webkit-gradient(linear, left top, left bottom, from(#fd9a18), to(#f16911));
    background: linear-gradient(to bottom, #fd9a18, #f16911); }

.c-icon--danger {
    background: #f95359;
    background: -webkit-gradient(linear, left top, left bottom, from(#f95359), to(#d35847));
    background: linear-gradient(to bottom, #f95359, #d35847); }

.c-icon--fancy {
    background: #886ce6;
    background: -webkit-gradient(linear, left top, left bottom, from(#886ce6), to(#7d5be2));
    background: linear-gradient(to bottom, #886ce6, #7d5be2); }

/* ==========================================================================
   #INPUTS
   ========================================================================== */
.c-input {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0.59375rem 0.9375rem;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    background-color: #fff;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 400;
    resize: none; }
.c-input::-webkit-input-placeholder {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    color: rgba(53, 64, 82, 0.5);
    font-weight: 400;
    opacity: 0.5; }
.c-input:-ms-input-placeholder {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    color: rgba(53, 64, 82, 0.5);
    font-weight: 400;
    opacity: 0.5; }
.c-input::-ms-input-placeholder {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    color: rgba(53, 64, 82, 0.5);
    font-weight: 400;
    opacity: 0.5; }
.c-input::placeholder {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    color: rgba(53, 64, 82, 0.5);
    font-weight: 400;
    opacity: 0.5; }
.c-input:focus {
    border-color: #2ea1f8;
    outline: none; }
.c-input:focus::-webkit-input-placeholder {
    opacity: 0.2; }
.c-input:focus:-ms-input-placeholder {
    opacity: 0.2; }
.c-input:focus::-ms-input-placeholder {
    opacity: 0.2; }
.c-input:focus::placeholder {
    opacity: 0.2; }
.c-input.is-disabled, .c-input:disabled {
    border-color: #dfe3e9;
    background-color: #e6eaee;
    color: rgba(53, 64, 82, 0.5);
    cursor: not-allowed; }

/**
 * Textarea
 */
textarea.c-input {
    min-height: 90px;
    font-size: 0.875rem;
    font-weight: 400; }

/* State variants
   ========================================================================== */
.c-input--success {
    border-color: #1bb934; }
.c-input--success:focus {
    border-color: #1bb934; }

.c-input--warning {
    border-color: #fd9a18; }
.c-input--warning:focus {
    border-color: #fd9a18; }

.c-input--danger {
    border-color: #ed1c24;
    color: #ed1c24; }
.c-input--danger:focus {
    border-color: #ed1c24;
    color: #ed1c24; }

/* ==========================================================================
   #MODAL 
   ========================================================================== */
.modal-open {
    overflow: hidden; }

.c-modal {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0 0.9375rem;
    outline: 0;
    z-index: 500; }
.modal-open .c-modal {
    overflow-x: hidden;
    overflow-y: auto; }

/**
 * 1. Allow scrolling when content's height is larger than viewport's height.
 * 2. Set the default width of the modal to be medium.
 */
.c-modal__dialog {
    position: relative;
    max-width: 550px;
    margin: 1.875rem auto; }
.c-modal.fade .c-modal__dialog {
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both; }
.c-modal.show .c-modal__dialog {
    -webkit-animation-name: move;
    animation-name: move; }
.c-modal .c-modal__dialog {
    max-width: 550px; }
.c-modal--xsmall .c-modal__dialog {
    max-width: 350px; }
.c-modal--small .c-modal__dialog {
    max-width: 450px; }
.c-modal--medium .c-modal__dialog {
    max-width: 550px; }
.c-modal--large .c-modal__dialog {
    max-width: 650px; }
.c-modal--xlarge .c-modal__dialog {
    max-width: 750px; }
.c-modal--huge .c-modal__dialog {
    max-width: 950px; }

.c-modal__content {
    position: relative;
    width: 100%;
    border-radius: 4px;
    outline: 0;
    overflow: hidden; }

.c-modal__header {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 20px 1.875rem; }

.c-modal__title {
    margin: 0;
    color: #fff;
    font-size: 1.125rem; }

.c-modal__close {
    color: #fff;
    font-size: 1.125rem;
    cursor: pointer;
    opacity: 0.8; }
.c-modal__close:hover {
    color: #fff; }

/**
 * Use this clas to quicklu position the close icon relative 
 * to its container, `c-modal__content`.
 */
.c-modal__close--absolute {
    position: absolute;
    top: 1.875rem;
    right: 1.875rem;
    z-index: 500; }
@media (max-width: 768px) {
    .c-modal__close--absolute {
        top: 0.9375rem; } }

.c-modal__subheader {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 15px 30px;
    background-color: #222C3C;
    color: #fff; }
.c-modal__subheader p {
    color: inherit; }

/**
 * Tab-style navigation inside modal subheader. When used, make sure to remove 
 * default vertical padding from `c-modal__subheader`.
 */
.c-modal__subheader-tab {
    color: #7f8fa4; }
.c-modal__subheader-tab:hover {
    color: #fff;
    text-decoration: none; }
.c-modal__subheader-tab.is-active {
    padding: 0.9375rem 0;
    border-bottom: 3px solid #2ea1f8;
    color: #fff; }

.c-modal__body {
    padding: 1.875rem;
    background-color: #fff;
    overflow: hidden; }

/**
 * Apply max height to force the body of the modal to be scrolled. This class
 * is used with (achievements) modal.
 */
.c-modal__body--maximized {
    max-height: 450px;
    overflow: auto; }

.c-modal__footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.9375rem 1.875rem;
    border-top: 1px solid #e6eaee;
    background-color: #EFF3F6; }

/** 
 * Add a dark background to make modal dialog standout.
 */
.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(29, 37, 49, 0.9);
    z-index: 400; }
.modal-backdrop.fade {
    opacity: 0; }
.modal-backdrop.show {
    opacity: 0.9; }

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll; }

/**
 * Modal move animation
 */
@-webkit-keyframes move {
    from {
    -webkit-transform: translateY(30px);
    transform: translateY(30px);
    opacity: 0; }
50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1; } }

@keyframes move {
    from {
    -webkit-transform: translateY(30px);
    transform: translateY(30px);
    opacity: 0; }
to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1; } }

/* ==========================================================================
   #NAV
   ========================================================================== */
/**
 * Nav sits within the main navbar area. It’s fully responsive as it holds 
 * navigation items/links that will be collapsed, toggled in small 
 * screens via `c-nav-toggle` component.
 */
.c-nav {
    display: block;
    /**
           * Responsive Nav
           *
           * 1. Remove extra margin when `u-mr-auto` utility is used. 
           */ }
@media (max-width: 768px) {
    .c-nav {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1 0 100%;
        flex: 1 0 100%;
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2;
        margin-top: 0.9375rem;
        margin-right: -0.9375rem !important;
        /* [1] */
        margin-left: -0.9375rem;
        border-top: 1px solid #e6eaee;
        /**
                   * Bootstrap classes used to show and hide nav.
                   * This behaviour depends on collapse.js plugin.
                   */ }
    .c-nav.collapse {
        display: none; }
    .c-nav.show {
        display: block; }
    .c-nav.collapsing {
        position: relative;
        height: 0;
        -webkit-transition: height 0.6s;
        transition: height 0.6s;
        overflow: hidden; } }

.c-nav__list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }

/** 
 * 1. Allow positioning dropdown menus inside nav items.
 */
.c-nav__item {
    position: relative;
    /* [1] */
    margin: 0 40px 0 0; }
@media (max-width: 768px) {
    .c-nav__item {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
        margin-right: 0;
        padding: 15px;
        border-bottom: 1px solid #e6eaee; } }
.c-nav__item:last-child {
    margin-right: 0; }
@media (max-width: 768px) {
    .c-nav__item:last-child {
        padding-bottom: 5px;
        border-bottom: 0; } }

.c-nav__link {
    color: #9FA9BA;
    font-size: 0.875rem;
    font-weight: 400; }
.c-nav__link:hover {
    text-decoration: none; }
.c-nav__link.is-active {
    color: #2ea1f8; }
@media (max-width: 768px) {
    .c-nav__link {
        display: block;
        color: #354052;
        font-size: 1rem; } }

/* Nav Toggle
   ========================================================================== */
/**
 * Nav toggle is the popular hamburger-like icon that would show/hide `c-nav` 
 * via javascript through `js-nav-toggle` to toggle `is-open` class.
 * 
 * 1. Remove default button styles.
 * 2. Show only on small screens, hide on large screen.
 */
.c-nav-toggle {
    display: none;
    position: relative;
    width: 30px;
    height: 30px;
    margin: 0 0 0 0.9375rem;
    /* [1] */
    padding: 0;
    /* [1] */
    border: 0;
    /* [1] */
    outline: none;
    background-color: transparent;
    /* [1] */
    cursor: pointer; }
@media (max-width: 768px) {
    .c-nav-toggle {
        display: block;
        /* [2] */ } }

/**
 * The hamburger icon itself, 3 bars stacked on top of each other. 
 */
.c-nav-toggle__bar {
    display: block;
    position: relative;
    width: 30px;
    height: 4px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border-radius: 4px;
    background-color: #354052; }
.c-nav-toggle__bar:first-child {
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px); }
.c-nav-toggle__bar:last-child {
    -webkit-transform: translateY(6px);
    transform: translateY(6px); }

/* ==========================================================================
   #NAVBAR
   ========================================================================== */
/**
 * The site's main page header, used as a wrapper for its sub-components.
 */
.c-navbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    min-width: 100%;
    min-height: 70px;
    padding: 13px 30px;
    border-bottom: 1px solid #E6EAEE;
    background-color: #fff; }
@media (max-width: 768px) {
    .c-navbar {
        padding: 0.625rem 0.9375rem; } }

/**
 * Set a fixed width for search input when placed in navbar.
 */
.c-navbar__search {
    width: 270px !important; }

/**
 * 1. Custom width, height is specififed for proper alignment, adding your own 
 *    brand/logo image will require custom styles for proper alignment.
 * 2. On small screens, seperate logo/brand from other navbar components as 
 *    navbar links would be collapsed.
 */
.c-navbar__brand {
    display: inline-block;
    width: 44px;
    /* [1] */
    height: 44px;
    /* [1] */
    margin: 0 20px 0 0; }
@media (max-width: 768px) {
    .c-navbar__brand {
        margin-right: auto;
        /* [2] */ } }

.c-navbar__title {
    margin: 0;
    padding: 0;
    font-size: 1.125rem; }

/* Style variants
   ========================================================================== */
/**
 * Use `c-navbar--inverse` for dark background colors.
 *
 */
.c-navbar--inverse {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }
.c-navbar--inverse .c-nav,
.c-navbar--inverse .c-nav__item {
    border-color: #53b2f9; }
.c-navbar--inverse .c-nav__link {
    color: #fff; }
.c-navbar--inverse .c-avatar.has-dropdown::after {
    color: #fff; }
.c-navbar--inverse .c-nav-toggle__icon,
.c-navbar--inverse .c-nav-toggle__icon::before,
.c-navbar--inverse .c-nav-toggle__icon::after {
    background-color: #fff;
    opacity: 0.95; }

/* ==========================================================================
   #NOTIFICATIONS
   ========================================================================== */
/**
 * 1. Allow notification to have a dropdown menu.
 * 2. Fix additional space added by positioning number absolutely.
 */
.c-notification {
    display: inline-block;
    position: relative;
    /* [1] */
    margin-right: 0.625rem;
    /* [2] */ }

.c-notification__icon {
    color: #7f8fa4;
    font-size: 20px; }

/**
 * 1. Align number with notifcation icon properly.
 */
.c-notification__number {
    display: inline-block;
    position: absolute;
    top: -3px;
    /* [1] */
    right: -0.625rem;
    /* [1] */
    min-width: 20px;
    height: 20px;
    padding: 0 5px;
    border-radius: 20px;
    background-color: #FF7800;
    color: #fff;
    font-size: 0.75rem;
    line-height: 20px;
    text-align: center; }

/* ==========================================================================
   #PAGINATION
   ========================================================================== */
/**
 * 1. Use flex utilitites with `c-pagination` for proper alignement of its
 *    children.
 */
.c-pagination {
    /* [1] */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 15px;
    text-align: center; }

/**
 * 1. Remove wierd space between list items.
 */
.c-pagination__list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 0;
    /* [1] */ }

.c-pagination__item {
    display: inline-block;
    margin-left: -1px;
    /* [1] */ }
.c-pagination__item:first-child .c-pagination__control {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.c-pagination__item:last-child .c-pagination__control {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }

/**
 * 1. Line height of pagination control should equal its height for proper
 *    vertical alignment.
 */
.c-pagination__control {
    display: block;
    height: 2.1875rem;
    /* [1] */
    padding: 0 16px;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff;
    color: #CED0DA;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 2.1875rem;
    /* [1] */
    text-align: center;
    /**
           * 1. You may resize this icon if you used another icon font.
           */ }
.c-pagination__control i {
    position: relative;
    bottom: -1px;
    -webkit-transition: color .15s;
    transition: color .15s;
    color: #CED0DA;
    font-size: 16px;
    /* [1] */ }
.c-pagination__control:hover {
    color: #354052;
    text-decoration: none; }
.c-pagination__control:hover i {
    color: #354052; }

.c-pagination__link {
    display: block;
    height: 2.1875rem;
    padding: 0 16px;
    border: 1px solid #e6eaee;
    background-color: #fff;
    color: rgba(53, 64, 82, 0.5);
    font-size: 14px;
    line-height: 2.1875rem;
    text-align: center; }
.c-pagination__link.is-active {
    color: #354052; }
.c-pagination__link:hover {
    color: #354052;
    text-decoration: none; }

.c-pagination__counter {
    display: inline-block;
    margin: 0;
    padding: 8px 15px;
    color: #7f8fa4;
    vertical-align: middle; }

/* ==========================================================================
   #POPOVERS
   ========================================================================== */
.c-popover {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 18.75rem;
    padding: 0.9375rem 20px;
    border-radius: 4px;
    background-clip: padding-box;
    background-color: #1d2531;
    color: #fff;
    text-align: center;
    z-index: 400;
    word-wrap: break-word;
    /**
           * 1. (bs-popover-*) classes are inserted by bootstrap's popover plugin, we 
           *    customize these classes for better compatibility with Dashboard UI Kit.
           */ }
.c-popover .arrow {
    display: none; }
.c-popover::before {
    position: absolute;
    border: 6px solid transparent;
    content: " "; }
.c-popover.fade {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    opacity: 0; }
.c-popover.fade.show {
    opacity: 1; }
.c-popover.bs-popover-top {
    /* [1] */
    margin-bottom: 0.9375rem; }
.c-popover.bs-popover-top::before {
    top: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    border-top-color: #1d2531; }
.c-popover.bs-popover-bottom {
    /* [1] */
    margin-top: 0.9375rem; }
.c-popover.bs-popover-bottom::before {
    bottom: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    border-bottom-color: #1d2531; }
.c-popover.bs-popover-right {
    /* [1] */
    margin-left: 0.9375rem; }
.c-popover.bs-popover-right::before {
    top: 50%;
    right: 100%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-right-color: #1d2531; }
.c-popover.bs-popover-left {
    /* [1] */
    margin-right: 0.9375rem; }
.c-popover.bs-popover-left::before {
    top: 50%;
    left: 100%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-left-color: #1d2531; }

/* ==========================================================================
   #PROGRESS
   ========================================================================== */
.c-progress {
    display: block;
    width: 100%;
    height: 20px;
    margin: 0 0 15px 0;
    border-radius: 20px;
    background-color: #E2E7EE; }

/**
 * 1. Default (info) background-color would be applied, if class 
 *    `c-progress--[color]` isn't specified.
 */
.c-progress__bar {
    position: relative;
    height: 100%;
    border-radius: 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    background: #2DA1F8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2DA1F8), to(#1991EB));
    background: linear-gradient(to bottom, #2DA1F8, #1991EB);
    /* [1] */ }

/* Size variants
   ========================================================================== */
.c-progress--xsmall {
    height: 2px; }

.c-progress--small {
    height: 4px; }

.c-progress--medium {
    height: 8px; }

/* Style variants
   ========================================================================== */
.c-progress--danger .c-progress__bar {
    background: #f95359;
    background: -webkit-gradient(linear, left top, left bottom, from(#f95359), to(#d35847));
    background: linear-gradient(to bottom, #f95359, #d35847); }

.c-progress--info .c-progress__bar {
    background: #2DA1F8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2DA1F8), to(#1991EB));
    background: linear-gradient(to bottom, #2DA1F8, #1991EB); }

.c-progress--primary .c-progress__bar {
    background: #475364;
    background: -webkit-gradient(linear, left top, left bottom, from(#475364), to(#273142));
    background: linear-gradient(to bottom, #475364, #273142); }

.c-progress--success .c-progress__bar {
    background: #5AD946;
    background: -webkit-gradient(linear, left top, left bottom, from(#5AD946), to(#2BB415));
    background: linear-gradient(to bottom, #5AD946, #2BB415); }

.c-progress--warning .c-progress__bar {
    background: #F8CF5D;
    background: -webkit-gradient(linear, left top, left bottom, from(#F8CF5D), to(#FDC018));
    background: linear-gradient(to bottom, #F8CF5D, #FDC018); }

.c-progress--fancy .c-progress__bar {
    background: #9D90E4;
    background: -webkit-gradient(linear, left top, left bottom, from(#9D90E4), to(#8261E6));
    background: linear-gradient(to bottom, #9D90E4, #8261E6); }

/* Multi-level progress bars
   ========================================================================== */
.c-progress__bar .c-progress__bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: red;
    z-index: 200; }

/* ==========================================================================
   #RANGES
   ========================================================================== */
.c-range {
    width: 100%;
    outline: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none; }

.c-range--inline {
    width: 96px; }

.c-range::-webkit-slider-thumb {
    position: relative;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 100%;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: #2DA1F8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2DA1F8), to(#1991EB));
    background: linear-gradient(to bottom, #2DA1F8, #1991EB); }

.c-range::-webkit-slider-runnable-track {
    height: 2px;
    border-radius: 1px;
    background-color: #CED0DA; }

/* ==========================================================================
   #SWITCH
   ========================================================================== */
/**
 * 1. Radius of `toggle` should be equal to radius of `c-switch`.
 * 2. The small circle that move to the right and left.
 * 3. Calculation: (width of switch bar) - (toggle's width + 2px )
 * 4. Don't allow active state on a disabled switch.
 */
.c-switch {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer; }
.c-switch::before {
    display: inline-block;
    position: relative;
    width: 48px;
    height: 26px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #e6eaee;
    border-radius: 83px;
    background-color: #dfe3e9;
    content: " "; }
.c-switch::after {
    /* [2] */
    position: absolute;
    top: 3px;
    left: 3px;
    width: 20px;
    height: 20px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border-radius: 83px;
    /* [1] */
    background-color: #fff;
    content: " "; }
.c-switch.is-active::before {
    background-color: #34aa44; }
.c-switch.is-active::after {
    left: 25px;
    /* [3] */ }
.c-switch.is-disabled {
    cursor: not-allowed; }
.c-switch.is-disabled::after {
    opacity: 0.5; }
.c-switch.is-disabled.is-active {
    /* [4] */ }
.c-switch.is-disabled.is-active::before {
    background-color: #dfe3e9; }
.c-switch.is-disabled.is-active::after {
    right: 2px; }

/**
 * 1. Hide input to replace it with switch-style input
 */
.c-switch__input {
    display: none;
    /* [1] */ }

.c-switch__label {
    margin: 0 0 0 0.625rem;
    color: #354052;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }

/* ==========================================================================
   #SWITCH
   ========================================================================== */
/**
 * 1. Radius of `toggle` should be equal to radius of `c-switch`.
 * 2. The small circle that move to the right and left.
 * 3. Calculation: (width of switch bar) - (toggle's width + 2px )
 * 4. Don't allow active state on a disabled switch.
 */
.c-switch {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer; }
.c-switch::before {
    display: inline-block;
    position: relative;
    width: 48px;
    height: 26px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #e6eaee;
    border-radius: 83px;
    background-color: #dfe3e9;
    content: " "; }
.c-switch::after {
    /* [2] */
    position: absolute;
    top: 3px;
    left: 3px;
    width: 20px;
    height: 20px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border-radius: 83px;
    /* [1] */
    background-color: #fff;
    content: " "; }
.c-switch.is-active::before {
    background-color: #34aa44; }
.c-switch.is-active::after {
    left: 25px;
    /* [3] */ }
.c-switch.is-disabled {
    cursor: not-allowed; }
.c-switch.is-disabled::after {
    opacity: 0.5; }
.c-switch.is-disabled.is-active {
    /* [4] */ }
.c-switch.is-disabled.is-active::before {
    background-color: #dfe3e9; }
.c-switch.is-disabled.is-active::after {
    right: 2px; }

/**
 * 1. Hide input to replace it with switch-style input
 */
.c-switch__input {
    display: none;
    /* [1] */ }

.c-switch__label {
    margin: 0 0 0 0.625rem;
    color: #354052;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }

/* ==========================================================================
   #TABLES
   ========================================================================== */
/**
 * 1. The border-collapse CSS property specifies that the table's borders are 
 *    collapsed and it means that adjacent cells share borders.
 */
.c-table {
    display: table;
    width: 100%;
    max-width: 100%;
    border: 1px solid #E6EAEE;
    border-radius: 4px;
    background-color: #fff;
    border-collapse: collapse;
    /* [1] */ }

/**
 * Responsive Tables
 *
 * Wrap any table with one of these classes to make it responsive at certain
 * breakpoint.
 *
 * 1. Wrap your table with this class `c-table-responsive` if the table's width 
 * is larger than 1200px.
 */
.c-table-responsive {
    /* [1] */
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; }
.c-table-responsive .c-table {
    display: block;
    overflow-y: hidden; }

@media (max-width: 576px) {
    .c-table-responsive\@mobile {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; }
    .c-table-responsive\@mobile .c-table {
        display: block;
        overflow-y: hidden; } }

@media (max-width: 768px) {
    .c-table-responsive\@tablet {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; }
    .c-table-responsive\@tablet .c-table {
        display: block;
        overflow-y: hidden; } }

@media (max-width: 992px) {
    .c-table-responsive\@desktop {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; }
    .c-table-responsive\@desktop .c-table {
        display: block;
        overflow-y: hidden; } }

@media (max-width: 1200px) {
    .c-table-responsive\@wide {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; }
    .c-table-responsive\@wide .c-table {
        display: block;
        overflow-y: hidden; } }

/**
 * Use `c-table--zebra` to add zebra-styling to table rows inside the table.
 */
.c-table--zebra tbody tr:nth-of-type(odd) {
    background-color: rgba(230, 234, 238, 0.3); }

/**
 * Highlight table row on hover.
 */
.c-table--highlight tbody tr:hover {
    background-color: rgba(230, 234, 238, 0.3); }

.c-table--fixed {
    table-layout: fixed; }

/**
 * Table title/caption
 * <caption> 
 */
.c-table__title {
    position: relative;
    padding: 25px 30px;
    border: 1px solid #e6eaee;
    border-bottom: 0;
    border-radius: 4px 4px 0 0;
    background-color: #fff;
    color: #354052;
    font-size: 24px;
    text-align: left; }
.c-table__title small {
    display: inline-block;
    margin-left: 10px;
    color: #7f8fa4;
    font-size: 14px; }
.c-table__title small::before {
    display: inline-block;
    position: relative;
    bottom: -2px;
    width: 1px;
    height: 14px;
    margin-right: 12px;
    background-color: #DFE3E9;
    content: ""; }

/**
 * A small icon in table title used to perform general action on table like
 * downloading table content as a report.
 */
.c-table__title-action {
    position: absolute;
    top: 30px;
    right: 30px;
    color: #AFB4BB;
    font-size: 22px; }

/**
 * Table Head
 *
 * Slimmer version of table's head with less sapcing and darker background
 */
.c-table__head--slim {
    padding: 0;
    background-color: #F5F8FA; }
.c-table__head--slim .c-table__cell {
    padding: 10px 0 10px 30px; }

/**
 * Table Row
 */
.c-table__row {
    border-top: 1px solid #E6EAEE;
    /* [1] */
    border-bottom: 1px solid #E6EAEE;
    /* [1] */ }
.c-table__row:last-child {
    border-bottom: 0; }

.c-table__row--success,
.c-table__row--danger,
.c-table__row--info,
.c-table__row--warning {
    border-left: 3px solid; }

.c-table__row--success {
    border-left-color: #1bb934; }

.c-table__row--danger {
    border-left-color: #ed1c24; }

.c-table__row--info {
    border-left-color: #2ea1f8; }

.c-table__row--warning {
    border-left-color: #fd9a18; }

/**
 * Table Cell
 */
.c-table__cell {
    padding: 20px 0 20px 30px;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    vertical-align: middle; }
.c-table__cell:last-child {
    padding-right: 1.875rem; }

.c-table__cell--head {
    color: #7f8fa4;
    font-size: 0.875rem;
    font-weight: 500; }

/**
 * Table cell with image
 *
 * 2. Use flexbox to make sure all table-cell content is vertically centered.
 */
.c-table__cell--img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    /* [2] */
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }

/* ==========================================================================
   #TABS
   ========================================================================== */
/**
 * A wrapper for tabs navigation and content.
 */
/**
 * 1. Works as a wrapper for tabs navigation links.
 */
.c-tabs__list {
    /* [1] */
    padding: 20px 30px;
    border: 1px solid #dfe3e9;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff; }
.c-tabs__list li {
    display: inline-block; }

.c-tabs__link {
    margin: 0 30px 0 0;
    padding: 0 0 21px 0;
    color: #7f8fa4;
    font-size: 1rem; }
.c-tabs__link:hover {
    color: #354052;
    text-decoration: none; }
.c-tabs__link.is-active, .c-tabs__link.active {
    border-bottom: 2px solid #2ea1f8;
    color: #2d3848;
    font-weight: 600; }

/**
 * Splitted-style of tabs navigation list
 */
.c-tabs__list--splitted {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0;
    border: 0;
    background-color: transparent; }
.c-tabs__list--splitted .c-tabs__item {
    -ms-flex-item-align: end;
    align-self: flex-end;
    margin: 0; }
.c-tabs__list--splitted .c-tabs__link {
    display: inline-block;
    -ms-flex-item-align: end;
    align-self: flex-end;
    margin-right: 4px;
    padding: 15px 30px;
    border: 1px solid #dfe3e9;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-size: 14px;
    background: #fff;
    background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#F4F7FA));
    background: linear-gradient(to bottom, #fff, #F4F7FA); }
.c-tabs__list--splitted .c-tabs__link.is-active, .c-tabs__list--splitted .c-tabs__link.active {
    padding: 20px 35px;
    border-bottom-color: #fff;
    background: #fff;
    font-size: 18px; }

.c-tabs__pane {
    display: none;
    margin: 0;
    padding: 20px 30px;
    border: 1px solid #dfe3e9;
    border-top: 0;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: #fff;
    color: #354052; }
.c-tabs__pane.is-active, .c-tabs__pane.active {
    display: block; }

/* ==========================================================================
   #TOGGLE
   ========================================================================== */
/**
 * Toggles are a pair of buttons useful for (Yes/No)-like questions.
 *
 * 1. Remove `is-active` styles when `c-toggle` is disabled
 */
.c-toggle {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    height: 45px;
    cursor: pointer; }
.c-toggle.is-disabled {
    cursor: not-allowed;
    opacity: 0.5; }
.c-toggle.is-disabled .c-toggle__btn.is-active {
    /* [1] */
    border-color: #E7E9ED;
    background-color: #FAFBFC;
    color: #565F6D; }

.c-toggle__btn {
    width: 50%;
    height: 45px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #E7E9ED;
    border-radius: 4px;
    background-color: #FAFBFC;
    color: #565F6D;
    font-size: 14px;
    text-align: center;
    text-transform: uppercase;
    cursor: inherit; }
.c-toggle__btn:nth-child(1) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
.c-toggle__btn:nth-child(2) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
.c-toggle__btn.is-active {
    border-color: #249533;
    background-color: #39B54A;
    color: #fff; }

.c-toggle__label {
    width: 100%;
    height: 45px;
    line-height: 45px;
    cursor: inherit; }

/**
 * 1. Hide default radio button
 */
.c-toggle__input {
    display: none;
    /* [1] */
    width: 100%;
    height: 100%; }

/* ==========================================================================
   #TOOLBAR
   ========================================================================== */
.c-toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 20px 30px;
    background-color: #fff;
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.07);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.07); }
@media (max-width: 768px) {
    .c-toolbar {
        padding: 20px 15px; } }

.c-toolbar__title {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0;
    color: #354052;
    font-size: 18px; }
.c-toolbar__title.has-divider::after {
    display: block;
    width: 1px;
    height: 15px;
    margin: 0 13px;
    background-color: #DFE3E9;
    content: " "; }

.c-toolbar__meta {
    margin: 0;
    color: #7F8FA4;
    font-size: 14px; }

.c-toolbar__icon {
    display: block;
    color: #CED0DA;
    font-size: 18px; }
.c-toolbar__icon.is-active {
    color: #2ea1f8; }
.c-toolbar__icon.has-divider::after {
    display: inline-block;
    position: relative;
    top: 0;
    width: 1px;
    height: 15px;
    margin: 0 13px;
    background-color: #DFE3E9;
    content: " "; }

.c-toolbar__link {
    color: #7F8FA4;
    font-size: 14px; }
.c-toolbar__link.has-dropdown::after {
    display: inline-block;
    margin-left: 10px;
    color: inherit;
    font-family: "FontAwesome";
    font-size: 12px;
    content: "\f3d0"; }

/**
 * Toolbar (tab-like) Navigation
 */
@media only screen and (max-width: 576px) {
    .c-toolbar__nav {
        margin-right: auto;
        margin-left: auto; } }

.c-toolbar__nav-item {
    margin-right: 25px;
    padding: 0 0 27px;
    color: #7f8fa4;
    font-size: 1rem; }
.c-toolbar__nav-item:last-child {
    margin-right: 0; }
.c-toolbar__nav-item:hover {
    color: #354052;
    text-decoration: none; }
.c-toolbar__nav-item.is-active {
    border-bottom: 2px solid #2ea1f8;
    color: #2d3848;
    font-weight: 600; }
@media only screen and (max-width: 576px) {
    .c-toolbar__nav-item {
        padding-bottom: 20px; } }

/**
 * Toolbar stats
 *
 * Numbers & states placed in the toolbar, used in performance.html page
 */
.c-toolbar__state {
    border-right: 1px solid #e6eaee;
    text-align: center; }
@media (max-width: 768px) {
    .c-toolbar__state {
        margin-bottom: 1.875rem;
        border-right: 0; }
    .c-toolbar__state:nth-child(3), .c-toolbar__state:nth-child(4) {
        margin-bottom: 0; } }
.c-toolbar__state:last-child {
    border-right: 0; }

.c-toolbar__state-number {
    margin-bottom: 0;
    font-size: 1.75rem; }

.c-toolbar__state-title {
    color: #7f8fa4;
    font-size: 0.875rem;
    text-transform: uppercase; }

/* ==========================================================================
   #TOOLTIPS
   ========================================================================== */
/**
 * Simple, css-only based tooltips.
 *
 * 1. Prevent other components from overrideing tooltip's line-height.
 * 2. Get tooltip text from `aria-label` and place it using `content` property.
 */
.c-tooltip {
    position: relative; }
.c-tooltip::after {
    position: absolute;
    max-height: 5rem;
    padding: 0.625rem 0.9375rem;
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    border-radius: 4px;
    background-color: #1d2531;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    /* [1] */
    text-align: center;
    text-transform: none;
    white-space: nowrap;
    content: attr(aria-label);
    /* [2] */
    opacity: 0;
    overflow: hidden;
    visibility: hidden;
    z-index: 200; }
.c-tooltip::before {
    position: absolute;
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    border: 6px solid transparent;
    content: " ";
    opacity: 0;
    visibility: hidden; }
.c-tooltip:hover::before, .c-tooltip:hover::after {
    opacity: 1;
    visibility: visible; }

/* Direction variants
   ========================================================================== */
.c-tooltip--top::after {
    bottom: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, -12px);
    transform: translate(-50%, -12px); }

.c-tooltip--top::before {
    bottom: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    border-top-color: #1d2531; }

.c-tooltip--bottom::after {
    top: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 12px);
    transform: translate(-50%, 12px); }

.c-tooltip--bottom::before {
    top: 100%;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    border-bottom-color: #1d2531; }

.c-tooltip--right::after {
    top: 50%;
    left: 100%;
    -webkit-transform: translate(12px, -50%);
    transform: translate(12px, -50%); }

.c-tooltip--right::before {
    top: 50%;
    left: 100%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-right-color: #1d2531; }

.c-tooltip--left::after {
    top: 50%;
    right: 100%;
    -webkit-transform: translate(-12px, -50%);
    transform: translate(-12px, -50%); }

.c-tooltip--left::before {
    top: 50%;
    right: 100%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-left-color: #1d2531; }

/* ==========================================================================
   #PROFILE
   ========================================================================== */
.c-profile-card {
    margin: 0 0 0.9375rem;
    padding: 0.625rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-profile-card__cover {
    height: 100px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    overflow: hidden; }
.c-profile-card__cover > img {
    display: block; }

/**
 * Profile card main content.
 */
.c-profile-card__user {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    top: -20px; }

.c-profile-card__avatar {
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 75px;
    height: 75px;
    margin: 0 0.9375rem 0 0;
    border: 2px solid #fff;
    border-radius: 100%;
    overflow: hidden; }
.c-profile-card__avatar > img {
    display: block; }

.c-profile-card__name {
    -ms-flex-item-align: end;
    align-self: flex-end;
    margin: 0.9375rem 0 0;
    font-size: 1rem;
    line-height: 1.25;
    /* 20px / 16px */ }

.c-profile-card__username {
    display: block;
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-profile-card__stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
.c-profile-card__stats:last-child {
    margin-right: 0; }

/**
 * Profile Card stats/numbers like: followers, following..
 */
.c-profile-card__state {
    margin-right: 30px;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.375;
    text-transform: uppercase; }
.c-profile-card__state:last-child {
    margin-right: 0; }

.c-profile-card__state-number {
    display: block;
    margin: 0;
    color: #354052;
    font-size: 1.25rem;
    font-weight: 600; }

.c-profile-card__social {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 0.625rem 12px;
    border-bottom: 1px solid #e6eaee; }

/**
 * Use brand color utilities to set the background-color of every icon.
 *
 * Example: 
 * 		<a class="c-profile__social-icon u-bg-dribbble" href="#">
 *     		<i class="fa fa-dribbble"></i>
 * 		</a>
 */
.c-profile-card__social-icon {
    display: block;
    width: 38px;
    height: 38px;
    border-radius: 100%;
    color: #fff;
    line-height: 38px;
    text-align: center; }
.c-profile-card__social-icon:hover {
    color: #fff; }

/**
 * A list of meta of information about the profile.
 */
.c-profile-card__meta {
    padding: 0.9375rem 0.625rem 0; }
.c-profile-card__meta:last-child {
    margin-bottom: 0; }

.c-profile-card__meta-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0 0 0.625rem;
    color: #354052;
    font-size: 0.875rem; }
.c-profile-card__meta-item > i {
    margin-right: 0.75rem;
    color: #7f8fa4;
    font-size: 1rem; }

/* ==========================================================================
   #EVENT
   ========================================================================== */
.c-event {
    margin-bottom: 1.875rem;
    padding: 0.625rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background: #fff; }

.c-event__img {
    position: relative;
    margin-bottom: 0.9375rem;
    border-radius: 3px;
    overflow: hidden; }
.c-event__img > img {
    display: block; }

.c-event__status {
    position: absolute;
    top: 0.625rem;
    right: 0.625rem;
    padding: 5px 12px;
    border-radius: 4px;
    background: rgba(49, 53, 63, 0.25);
    color: #fff;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase; }

/** 
 * Meta information about the event
 */
.c-event__meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 0 10px 0 20px; }

.c-event__title {
    margin: 0;
    font-size: 1rem;
    font-weight: 500; }

.c-event__place {
    display: block;
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem; }

/**
 * Align event button with the event title
 */
.c-event__btn {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }

/* ==========================================================================
   #SIDEBAR
   ========================================================================== */
/** 
 * NOTE: The layout, width of the sidebar is controlled by `o-page__sidebar` in
 *       `o-page` object, found in `objects/_objects.page.scss`.
 */
.c-sidebar {
    position: relative;
    height: 100%;
    margin: 0;
    padding-bottom: 1.875rem;
    background-color: #222C3C;
    overflow: auto;
    z-index: 200; }
.c-sidebar::-webkit-scrollbar {
    /* [2] */
    width: 6px;
    background-color: transparent; }
.c-sidebar::-webkit-scrollbar-thumb {
    /* [2] */
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.5); }

/**
 * 1. Height of sidebar brand should equal the height of the navbar for perfect 
 *    alignment.
 * 2. You can change the height of brand/logo image as you want, but its height
 *    shouldn't be more than navbar's height.
 */
.c-sidebar__brand {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 70px;
    /* [1] */
    padding-left: 1.875rem;
    border-bottom: 1px solid #2a3547;
    color: #e6eaee;
    font-size: 16px;
    vertical-align: middle; }
.c-sidebar__brand:hover {
    color: #e6eaee;
    text-decoration: none; }

.c-sidebar__brand-img {
    display: inline-block;
    height: 44px;
    /* [2] */
    margin-right: 0.625rem; }

.c-sidebar__title {
    margin-top: 25px;
    margin-bottom: 15px;
    padding-left: 30px;
    color: #7f8fa4;
    font-size: 12px;
    text-transform: uppercase; }

.c-sidebar__divider {
    display: block;
    width: 100%;
    height: 1px;
    background-color: #2A3547; }

/**
 * Sidebar naviagtion menu
 */
.c-sidebar__item {
    position: relative;
    -webkit-transition: background-color 0.2s;
    transition: background-color 0.2s;
    /**
           * 1. Add small arrow to `c-menu__link` when `c-menu__item` has sub-menu.
           */ }
.c-sidebar__item.has-submenu > .c-sidebar__link::after {
    /* [1] */
    display: inline-block;
    position: absolute;
    top: 12px;
    right: 30px;
    color: #B7C0CD;
    font-family: "FontAwesome";
    content: "\f0d7"; }
.c-sidebar__item.is-open {
    background-color: #1D2531;
    -webkit-box-shadow: inset 3px 0 0 0 #00A8FF;
    box-shadow: inset 3px 0 0 0 #00A8FF; }
.c-sidebar__item.is-open > .c-sidebar__link::after {
    content: "\f0d8"; }

.c-sidebar__link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 19px 0 19px 30px;
    -webkit-transition: background-color .2s;
    transition: background-color .2s;
    color: #B7C0CD;
    font-size: .875rem; }
.c-sidebar__link.is-active {
    background-color: #1D2531;
    -webkit-box-shadow: inset 3px 0 0 0 #00A8FF;
    box-shadow: inset 3px 0 0 0 #00A8FF; }
.c-sidebar__link:hover {
    color: #fff;
    text-decoration: none; }

/**
 * Sub Menu
 */
.c-sidebar__submenu {
    /**
           * Bootstrap classes used to show and hide sidebar submenu.
           * This behaviour depends on collapse.js plugin.
           */ }
.c-sidebar__submenu.collapse {
    display: none; }
.c-sidebar__submenu.show {
    display: block; }
.c-sidebar__submenu.collapsing {
    position: relative;
    height: 0;
    -webkit-transition: height 0.3s;
    transition: height 0.3s;
    overflow: hidden; }

/* Style Variation - Light Sidebar
   ========================================================================== */
.c-sidebar--light {
    border-right: 1px solid #e6eaee;
    background-color: #fff;
    /**
           * 1. Add a slight shadow to the sidebar to make it popup a little bit
           *    on small screens.
           */ }
.c-sidebar--light .c-sidebar__divider {
    background-color: #e6eaee; }
.c-sidebar--light .c-sidebar__brand {
    border-color: #e6eaee;
    color: #354052; }
.c-sidebar--light .c-sidebar__item.has-submenu > .c-sidebar__link::after {
    /* [1] */
    color: #B7C0CD; }
.c-sidebar--light .c-sidebar__item.is-open {
    background-color: #FAFBFC; }
.c-sidebar--light .c-sidebar__link {
    color: #7f8fa4; }
.c-sidebar--light .c-sidebar__link.is-active {
    background-color: transparent;
    color: #354052; }
.c-sidebar--light .c-sidebar__link.is-active i {
    color: #2ea1f8; }
.c-sidebar--light .c-sidebar__link.is-active:hover {
    color: #2ea1f8; }
.c-sidebar--light .c-sidebar__link:hover {
    color: #354052; }
@media (max-width: 992px) {
    .c-sidebar--light {
        -webkit-box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1);
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1);
        /* [1] */ } }

/* Sidebar toggle
   ========================================================================== */
/**
 * An icon the is used to toggle (show/hide) sidebar on small screen, you will 
 * find this component placed in the main navbar.
 */
.c-sidebar-toggle {
    display: none;
    position: relative;
    width: 30px;
    height: 30px;
    margin: 0 0.9375rem 0 0;
    /* [1] */
    padding: 0;
    /* [1] */
    border: 0;
    /* [1] */
    outline: none;
    background-color: transparent;
    /* [1] */
    cursor: pointer; }
@media (max-width: 992px) {
    .c-sidebar-toggle {
        display: block;
        /* [2] */ } }

/**
 * The hamburger icon itself, 3 bars stacked on top of each other. 
 */
.c-sidebar-toggle__bar {
    display: block;
    position: relative;
    width: 30px;
    height: 4px;
    -webkit-transition: all .3s;
    transition: all .3s;
    border-radius: 4px;
    background-color: #354052; }
.c-sidebar-toggle__bar:first-child {
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px); }
.c-sidebar-toggle__bar:last-child {
    -webkit-transform: translateY(6px);
    transform: translateY(6px); }

/* ==========================================================================
   #PROJECT
   ========================================================================== */
/**
 * Used in projects-board.html page.
 */
.c-project {
    margin-bottom: 1.875rem;
    padding: 0.625rem 0.625rem 20px;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background: #fff; }
.c-project:hover .c-project__profile:nth-child(n) {
    -webkit-transform: translateX(0);
    transform: translateX(0); }

.c-project__img {
    position: relative;
    margin-bottom: 0.75rem;
    border-radius: 4px;
    overflow: hidden; }
.c-project__img > img {
    display: block; }

.c-project__title {
    margin: 0;
    padding-left: 0.625rem;
    font-size: 1rem;
    font-weight: 500; }

.c-project__status {
    display: block;
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem; }

.c-project__team {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 1.25rem;
    padding-left: 0.625rem; }

/**
 * On hover, expand project profiles.
 * Maximum number of profiles is 4, more profiles should be hidden.
 *
 * 1. Hover animation will only work on large viewports.
 */
.c-project__profile {
    display: block;
    position: relative;
    width: 32px;
    height: 32px;
    margin-right: 0.3125rem;
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
    text-align: center; }
.c-project__profile:nth-child(2) {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px); }
.c-project__profile:nth-child(3) {
    -webkit-transform: translateX(calc(-20px * 2));
    transform: translateX(calc(-20px * 2)); }
.c-project__profile:nth-child(4) {
    -webkit-transform: translateX(calc(-20px * 3));
    transform: translateX(calc(-20px * 3)); }
.c-project__profile:nth-child(5) {
    -webkit-transform: translateX(calc(-20px * 4));
    transform: translateX(calc(-20px * 4)); }
@media (max-width: 1200px) {
    .c-project__profile:nth-child(n) {
        -webkit-transform: translateX(0);
        transform: translateX(0);
        /* [1] */ } }
.c-project__profile img {
    border-radius: 100%; }

.c-project__profile--btn {
    border-radius: 100%;
    background-color: #e6eaee;
    color: #7f8fa4;
    font-size: 0.875rem;
    line-height: 32px; }
.c-project__profile--btn:hover {
    background-color: #1bb934;
    color: #fff; }

/* ==========================================================================
   #POST
   ========================================================================== */
/**
 * Used in: news-feed.html
 */
.c-post {
    width: 100%;
    min-width: 100%;
    margin: 0 0 0.9375rem;
    border-radius: 4px; }

.c-post__content {
    width: 100%;
    min-width: 100%;
    min-height: 120px;
    padding: 0.625rem 1.875rem 0.625rem 20px;
    border: 1px solid #e6eaee;
    background-color: #fff;
    color: #354052;
    font-size: 0.875rem;
    overflow-y: auto;
    resize: none; }

.c-post__toolbar {
    padding: 12px;
    border: 1px solid #e6eaee;
    border-top: 0;
    background-color: #fafbfc;
    overflow: hidden; }

/**
 * Customize submit button
 */
.c-post__submit {
    margin-right: 12px;
    padding-right: 0.8rem;
    padding-left: 0.8rem; }

.c-post__time {
    position: relative;
    padding: 0 0 0 12px;
    /**
           * Vertical divier to seperate (submit button) from (post time).
           */ }
.c-post__time::before {
    display: inline-block;
    position: absolute;
    top: -24px;
    left: 0;
    width: 1px;
    height: 70px;
    background-color: #e6eaee;
    content: ""; }
.c-post__time i {
    margin-right: 5px; }
@media (max-width: 576px) {
    .c-post__time {
        display: none; } }

/* ==========================================================================
   #STREAM
   ========================================================================== */
/**
 * Used in news-feed.html page
 */
.c-stream {
    margin: 0 0 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-stream-item {
    padding: 0.9375rem 20px 0.9375rem 12px;
    border-bottom: 1px solid #e6eaee; }

/**
 * Stream item header
 */
.c-stream-item__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 0.3125rem; }

.c-stream-item__name {
    color: #354052;
    font-weight: 400; }

.c-stream-item__username {
    margin: 0 0 0 0.3125rem;
    color: #7f8fa4;
    font-size: 0.875rem;
    font-weight: 400;
    /**
           * 1. Remove text-decoration from username when the link is hovered.
           */ }
.c-stream-item__name:hover .c-stream-item__username {
    /* [1] */
    display: inline-block;
    text-decoration: none; }

.c-stream-item__time {
    color: #7f8fa4;
    font-size: 0.875rem; }

/**
 * Stream item gallery
 */
.c-stream-item__gallery {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 0.9375rem; }
.c-stream-item__gallery > img {
    width: 49%;
    border-radius: 4px; }

/**
 * A list of icons to perform certain actions like: (love, comment, reply).
 */
.c-stream-item__actionlist {
    display: inline-block; }

.c-stream-item__action {
    display: inline-block;
    margin: 0 1.875rem 0 0;
    color: #7f8fa4;
    vertical-align: middle; }
.c-stream-item__action:hover {
    text-decoration: none; }
.c-stream-item__action > i {
    margin-right: 0.3125rem;
    font-size: 0.875rem; }

.c-strem-item__actiontoggle {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    bottom: -0.1875rem;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #7f8fa4; }

/* ==========================================================================
   #GALLERY CARD
   ========================================================================== */
.c-gallery-card {
    margin: 0 0 1.875rem;
    padding: 10px;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background: #fff; }

.c-gallery-card__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin: 0 0 0.9375rem; }

.c-gallery-card__title {
    display: inline-block;
    margin: 0;
    font-size: 1rem; }

.c-gallery-card__items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between; }

/**
 * 1. Set width of gallery image to be half of its container minus (5px) as 
 *    a spacing between images.
 */
.c-gallery-card__img {
    width: calc(50% - 5px);
    /* [1] */
    margin: 0 0 0.625rem;
    border-radius: 4px; }

/* ==========================================================================
   #STATE
   ========================================================================== */
.c-state {
    position: relative;
    margin-bottom: 1.875rem;
    padding: 0.9375rem;
    border-radius: 4px; }

.c-state__title {
    margin: 0.625rem 0 0 0.9375rem;
    color: #fff;
    font-size: 0.75rem;
    text-transform: uppercase;
    opacity: 0.7; }

.c-state__number {
    display: block;
    margin: 0 0 20px 0.9375rem;
    color: #fff;
    font-size: 2.25rem;
    font-weight: 400; }

.c-state__status {
    margin: 0;
    padding: 0.625rem 0 0 0.9375rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 0.75rem;
    font-weight: 400;
    text-transform: uppercase; }

/**
 * 1. Equals padding of `.c-state`.
 */
.c-state__indicator {
    display: inline-block;
    position: absolute;
    top: 0.9375rem;
    /* [1] */
    right: 0.9375rem;
    /* [1] */
    color: #fff;
    font-size: 38px;
    opacity: 0.2; }
.c-state__indicator > i {
    display: block; }

/* Style variants
   ========================================================================== */
/**
 * 1. If `c-state--{color}` isn't specified, (info) background color will 
 *    be applied.
 */
.c-state,
.c-state--info {
    /* [1] */
    background-color: #3B517C; }

.c-state--success {
    background-color: #66B92E; }

.c-state--warning {
    background-color: #DA932C; }

.c-state--danger {
    background-color: #D65B4A; }

/* ==========================================================================
   #TODO
   ========================================================================== */
/**
 * 1. Get rid of white space cause by using `inline-flex`.
 * 
 * Read More: https://goo.gl/ajXW52
 */
.c-todo {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1.25rem 1.875rem;
    border-top: 1px solid #e6eaee;
    font-size: 0;
    /* [1] */
    font-weight: 400;
    /**
           * Disabled state
           *
           * NOTE: `disabled` attribute must be added to <input>
           */ }
.c-todo.is-disabled .c-todo__label {
    color: rgba(53, 64, 82, 0.5);
    cursor: default; }
.c-todo.is-disabled .c-todo__label::before {
    border-color: #dfe3e9;
    background-color: #e6eaee;
    text-decoration: none; }

/**
 * 1. Hide default input and create a customized input.
 * 2. Align the customized input with label.
 */
.c-todo__input {
    /* [1] */
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    white-space: nowrap;
    overflow: hidden;
    clip: rect(0 0 0 0);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%); }
.c-todo__input + .c-todo__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 1.5625rem;
    color: #354052;
    font-size: 0.875rem;
    cursor: pointer; }
.c-todo.is-completed .c-todo__input + .c-todo__label {
    text-decoration: line-through;
    opacity: .5; }
.c-todo__input + .c-todo__label::before {
    display: inline-block;
    position: absolute;
    top: 2px;
    /* [2] */
    left: 0;
    width: 16px;
    height: 16px;
    margin: 0 15px 0 0;
    -webkit-transition: all .3s;
    transition: all .3s;
    border: 1px solid #b7c0cd;
    border-radius: 100%;
    background-color: #fff;
    color: #fff;
    font-family: "FontAwesome";
    font-size: 10px;
    line-height: 15px;
    text-align: center;
    text-decoration: none;
    content: "";
    visibility: visible;
    /* [2] */ }
.c-todo__input:checked + .c-todo__label::before {
    border-color: #b7c0cd;
    background-color: #b7c0cd;
    color: #fff;
    content: "\f00c"; }

/* Todo Stream
   ========================================================================== */
/**
 * Todo task displayed as a stream. Used in active-projects.html page.
 */
.c-todo-stream {
    display: block;
    position: relative; }
.c-todo-stream::before {
    position: absolute;
    top: 7.5%;
    left: 7px;
    width: 2px;
    height: 85%;
    background-color: #b7c0cd;
    content: "";
    opacity: 0.2; }
.c-todo-stream .c-todo {
    margin-bottom: 15px;
    padding: 0;
    border-top: 0; }

/* ==========================================================================
   #MESSANGER
   ========================================================================== */
/**
 * Components used to build messanger page.
 *
 * Table of content:
 *
 * 1. Messages
 * 2. Chat 
 */
/* 1. Messages
   ========================================================================== */
/**
 * A container for the messanger app.
 */
.c-messanger {
    background-color: #fff; }

/**
 * A container for inbox messages.
 */
.c-messages {
    height: calc(100vh - (69px + 70px));
    border-right: 1px solid #e6eaee;
    overflow-y: auto; }
.c-messages::-webkit-scrollbar {
    /* [2] */
    width: 6px;
    background-color: transparent; }
.c-messages::-webkit-scrollbar-thumb {
    /* [2] */
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.5); }

.c-message {
    display: block;
    position: relative;
    padding: 15px 20px 15px 30px;
    border-bottom: 1px solid #e6eaee;
    color: #354052; }
.c-message:hover {
    text-decoration: none; }
.c-message.is-active {
    border-left: 3px solid #53b2f9; }

.c-message__title {
    margin: 0;
    color: #354052;
    font-size: 16px; }

.c-message__title-meta {
    display: block;
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-message__time {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #7f8fa4;
    font-size: 14px; }

/**
 * Make room for messages counter
 */
.c-message__snippet {
    margin: 10px 40px 0 0;
    /* [1] */ }

.c-message__counter {
    display: inline-block;
    position: absolute;
    right: 20px;
    bottom: 25px;
    width: 24px;
    height: 24px;
    border-radius: 100%;
    background-color: #53b2f9;
    color: #fff;
    font-size: 14px;
    line-height: 24px;
    text-align: center; }

/* 2. Chat
   ========================================================================== */
/**
 * A container for chat.
 */
.c-chat {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    overflow-y: auto;
    height: calc(100vh - (69px + 70px)); }

.c-chat__body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow-y: auto; }
.c-chat__body::-webkit-scrollbar {
    /* [2] */
    width: 6px;
    background-color: transparent; }
.c-chat__body::-webkit-scrollbar-thumb {
    /* [2] */
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.5); }

.c-chat__post {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 60px;
    flex: 0 0 60px; }

.c-chat__message {
    padding: 20px 80px 20px 30px; }

.c-chat__message-author {
    display: inline-block;
    margin-bottom: 10px;
    color: #354052;
    font-size: 1rem;
    font-weight: 500; }

.c-chat__message-time {
    margin-left: 8px;
    color: #7f8fa4;
    font-size: 14px; }

.c-chat__message-content {
    margin: 0;
    color: #354052; }

/**
 * Chat Divider
 */
.c-chat__divider {
    display: block;
    position: relative;
    color: #7f8fa4;
    font-size: 0.875rem;
    text-align: center; }
.c-chat__divider::before, .c-chat__divider::after {
    position: absolute;
    top: 50%;
    width: 42%;
    height: 1px;
    background-color: #e6eaee;
    content: ""; }
.c-chat__divider::before {
    left: 0; }
.c-chat__divider::after {
    right: 0; }
.c-chat__divider.is-active {
    color: #1A91EB; }
.c-chat__divider.is-active::before, .c-chat__divider.is-active::after {
    background-color: #1A91EB; }

/**
  * Chat Message Composer
  *
  * a form to compose a new message.
  */
.c-chat__composer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: calc(100% - 60px);
    height: 60px;
    margin: 0 auto 20px;
    padding: 0;
    background-color: #fff; }
.c-chat__composer .c-field__addon {
    padding: 5px 25px; }

/* ==========================================================================
   #CREDIT CARD
   ========================================================================== */
/**
 * Used in account-settings.html page
 */
.c-credit-card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    min-height: 196px; }

.c-credit-card__card {
    position: absolute;
    width: 50%;
    margin-right: 1.875rem;
    margin-left: 1.875rem;
    padding: 1.5625rem 1.875rem;
    border: 2px solid #2ea1f8;
    border-radius: 8px;
    background-color: #fff; }

.c-credit-card__logo {
    margin-bottom: 0.9375rem; }

.c-credit-card__number {
    margin-bottom: 0.3125rem;
    color: #354052;
    font-size: 1.25rem;
    font-weight: 600; }

.c-credit-card__status {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-credit-card__user {
    -ms-flex-item-align: end;
    align-self: flex-end;
    width: 100%;
    max-height: 195px;
    padding: 30px 0;
    padding-left: calc(50% + 60px);
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-credit-card__user-title {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600; }

.c-credit-card__user-meta {
    margin: 0 0 5px;
    font-size: 0.875rem; }

/* ==========================================================================
   #SEARCH RESULT
   ========================================================================== */
.c-search-result {
    position: relative;
    margin-bottom: 1.875rem;
    padding: 1.25rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-search-result__avatar {
    margin-right: 1.25rem; }

.c-search-result__title {
    margin-bottom: 0;
    font-size: 1rem; }

.c-search-result__meta {
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-search-result__actionlist {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem; }

.c-search-result__action {
    display: inline-block;
    margin-left: 0.625rem;
    float: left; }
.c-search-result__action > a {
    color: #b7c0cd; }

.c-search-result__gallery {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-top: 20px; }

.c-search-result__gallery-item {
    -ms-flex-preferred-size: 30%;
    flex-basis: 30%;
    border-radius: 4px;
    overflow: hidden; }
.c-search-result__gallery-item > a {
    display: block;
    line-height: 0; }

/* ==========================================================================
   #SEARCH FORM
   ========================================================================== */
/**
 * Used in browse.html page.
 */
.c-search-form {
    margin-bottom: 1.875rem;
    padding: 1.25rem;
    border-radius: 4px;
    background-color: #fff; }

.c-search-form__label {
    margin-bottom: 0.625rem;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase; }

.c-search-form__section {
    margin-bottom: 0.9375rem; }

/* ==========================================================================
   #GRAPH CARD
   ========================================================================== */
.c-graph-card {
    margin: 0 0 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden; }

.c-graph-card__content {
    padding: 1.875rem 1.875rem 0; }

.c-graph-card__title {
    margin: 0;
    font-size: 1.125rem; }

.c-graph-card__date {
    margin: 0 0 0.625rem;
    color: #7f8fa4;
    font-size: 0.75rem; }

.c-graph-card__number {
    margin: 0;
    color: #354052;
    font-size: 2.5rem;
    font-weight: 300; }

.c-graph-card__status {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-graph-card__chart {
    position: relative;
    margin: 0;
    padding: 1.875rem; }

.c-graph-card__footer {
    height: 100%;
    padding: 0.9375rem 1.875rem;
    border-top: #e6eaee solid 1px;
    background-color: #fafbfc; }

/* ==========================================================================
   #DIVIDER
   ========================================================================== */
.c-divider {
    display: block;
    position: relative;
    height: 1px;
    background-color: #dfe3e9;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 400;
    text-align: center;
    text-transform: uppercase;
    /**
           * When divider has a text in its center.
           */ }
.c-divider.has-text {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    background-color: transparent; }
.c-divider.has-text::before, .c-divider.has-text::after {
    width: 42%;
    height: 1px;
    background-color: #dfe3e9;
    content: ""; }
@media (max-width: 768px) {
    .c-divider::before, .c-divider::after {
        width: 30%; } }

.c-divider--small {
    font-size: 0.875rem;
    text-transform: none; }
.c-divider--small.has-text::before, .c-divider--small.has-text::after {
    width: 25%;
    height: 1px;
    background-color: #dfe3e9; }

/* ==========================================================================
   #PROGRESS CARD
   ========================================================================== */
.c-progress-card {
    margin: 0 0 1.875rem;
    padding: 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-progress-card__title {
    margin: 0;
    font-size: 1.125rem; }

.c-progress-card__date {
    margin: 0 0 1.875rem;
    color: #7f8fa4;
    font-size: 0.75rem; }

.c-progress-card__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin: 0 0 0.9375rem; }
.c-progress-card__item:last-child {
    margin-bottom: 0; }

.c-progress-card__label {
    -ms-flex-preferred-size: 20%;
    flex-basis: 20%;
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-progress-card__progress {
    -ms-flex-preferred-size: 80%;
    flex-basis: 80%;
    margin: 0; }

.c-progress-card__legends {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between; }

.c-progress-card__legend {
    display: inline-block;
    width: 10px;
    height: 2px;
    margin: 0 0.625rem 0 0;
    vertical-align: middle; }

/* ==========================================================================
   #PANEL
   ========================================================================== */
/**
 * Panels acts like a secondry sidebar, usually placed on the opposite side of
 * sidebar, in the case of Dashboad's design it's placed in the right.
 *
 * 1. Remove top border from panel to avoid collapsing with navbar's border.
 */
.c-panel {
    height: 100%;
    margin: 0;
    padding: 1.875rem;
    border: 1px solid #e6eaee;
    border-top: 0;
    /* [1] */
    background-color: #fff; }

.c-panel__title {
    margin-bottom: 1.875rem;
    font-size: 1rem; }

.c-panel__widget::after {
    display: block;
    height: 1px;
    margin: 1.875rem 0;
    background-color: #e6eaee;
    content: " ";
    opacity: 0.5; }

.c-panel__widget:last-child::after {
    content: none; }

/* ==========================================================================
   #STATE CARD
   ========================================================================== */
.c-state-card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-bottom: 1.875rem;
    padding: 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-state-card__icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    margin-right: 1.25rem;
    border-radius: 100%;
    background-color: #b7c0cd;
    color: #fff;
    font-size: 1.25rem;
    text-align: center;
    /**
           * Icon's inside border
           */ }
.c-state-card__icon::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 52px;
    height: 52px;
    margin: auto;
    border: 2px solid #fff;
    border-radius: 100%;
    content: "";
    opacity: 0.2; }

/**
 * Icon style variations
 */
.c-state-card__icon--info {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }

.c-state-card__icon--fancy {
    background: #886ce6;
    background: -webkit-gradient(linear, left top, left bottom, from(#886ce6), to(#7d5be2));
    background: linear-gradient(to bottom, #886ce6, #7d5be2); }

.c-state-card__icon--warning {
    background: #fd9a18;
    background: -webkit-gradient(linear, left top, left bottom, from(#fd9a18), to(#f16911));
    background: linear-gradient(to bottom, #fd9a18, #f16911); }

.c-state-card__icon--success {
    background: #39b54a;
    background: -webkit-gradient(linear, left top, left bottom, from(#39b54a), to(#34aa44));
    background: linear-gradient(to bottom, #39b54a, #34aa44); }

.c-state-card__number {
    display: inline-block;
    position: relative;
    margin: 0;
    font-size: 30px;
    font-weight: 400; }

.c-state-card__status {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    top: 15px;
    right: -20px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    border-radius: 100%;
    background-color: #bbc5d5;
    color: #fff;
    font-size: 0.625rem;
    text-align: center; }

.c-state-card__meta {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase; }

/* ==========================================================================
   #SUMMARY
   ========================================================================== */
/**
 * Used in invoices.html page
 */
.c-summary {
    position: relative;
    padding: 0 0 1.875rem 1.875rem; }
.c-summary.has-divider::after {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    content: "";
    background: #e6eaee;
    background: -webkit-gradient(linear, left bottom, left top, from(#e6eaee), to(#fff));
    background: linear-gradient(to top, #e6eaee, #fff); }
@media (max-width: 768px) {
    .c-summary.has-divider::after {
        top: initial;
        bottom: 15px;
        width: 100%;
        height: 1px; } }

.c-summary__title {
    margin-bottom: 0.9375rem;
    color: #7f8fa4;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase; }

.c-summary__number {
    display: inline-block;
    position: relative;
    margin: 0;
    font-size: 1.875rem;
    font-weight: 400; }

.c-summary__status {
    display: block;
    position: absolute;
    top: 0.9375rem;
    right: -1.25rem;
    width: 14px;
    height: 14px;
    border-radius: 100%;
    background-color: #bbc5d5;
    color: #fff;
    font-size: 0.625rem;
    line-height: 14px;
    text-align: center; }

.c-summary__meta {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 400;
    text-transform: uppercase; }

/* ==========================================================================
   #CHART
   ========================================================================== */
/**
 * This works as a wrapper for charts.
 */
.c-chart-container {
    position: relative;
    height: 95px; }

.c-chart__title {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem; }

/* ==========================================================================
   #PROJECT CARD
   ========================================================================== */
/**
 * used in `home-overview.html`
 */
.c-project-card {
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden; }

.c-project-card__img {
    display: block; }

.c-project-card__head {
    padding: 1.25rem 1.875rem;
    border-bottom: 1px solid #e6eaee; }

.c-project-card__title {
    margin: 0;
    font-size: 1rem; }

.c-project-card__info {
    margin: 0;
    color: #7f8fa4;
    font-size: 0.75rem; }

.c-project-card__meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1.25rem 1.875rem; }

/* ==========================================================================
   #MENU
   ========================================================================== */
/**
 * A list of horizontal or vertical links, and unlike `c-menu` component, this 
 * component shouldn't be used inside a sidebar or any other component.
 */
.c-menu__title {
    margin: 0 0 0.625rem 20px;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 400;
    text-transform: uppercase; }

.c-menu__link {
    display: inline-block;
    width: 100%;
    padding: 5px 0 5px 20px;
    border-radius: 4px;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 400; }
.c-menu__link:hover {
    text-decoration: none; }
.c-menu__link.is-active {
    background-color: #2ea1f8;
    color: #fff; }

.c-menu__icon {
    margin-right: 0.625rem;
    color: #7f8fa4; }
.c-menu__link:hover .c-menu__icon {
    color: #2ea1f8; }

/* ==========================================================================
   #BOARDS
   ========================================================================== */
/**
 * Boards consist of 2 main components:
 *
 * 1. Board 
 * 2. Task
 *
 * NOTE: We didn't seperate these components into their own files as it's 
 * intended to be used togther.
 */
/* 1. Board
   ========================================================================== */
.c-board {
    position: relative;
    margin-bottom: 3.125rem; }

.c-board__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 0.625rem; }

.c-board__title {
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 600; }

.c-board__actions {
    display: block;
    position: relative; }
.c-board__actions > a {
    color: #bbc5d5; }

/**
 * A wrapper for board's tasks.
 */
.c-board__content {
    position: relative;
    padding: 10px;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fafbfc;
    overflow-y: auto; }

.c-board__btn {
    display: block;
    position: absolute;
    bottom: -16px;
    left: 16px;
    width: 32px;
    height: 32px;
    border-radius: 100%;
    color: #fff;
    font-size: 0.875rem;
    line-height: 32px;
    text-align: center;
    z-index: 200;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }
.c-board__btn:hover {
    color: #fff; }

/**
 * Add a board button
 */
.c-add-board {
    display: block;
    width: 100%;
    margin-top: 2.125rem;
    margin-bottom: 1.875rem;
    padding: 0.9375rem 1.25rem;
    border-radius: 4px;
    background-color: #b7c0cd;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 600;
    opacity: .8; }
.c-add-board > i {
    margin-right: 0.625rem; }
.c-add-board:hover {
    color: #fff;
    text-decoration: none;
    opacity: 1; }

/**
 * Board style variations
 *
 * Add a border with a specific color to indicate the status of the board.
 */
.c-board--info .c-board__content,
.c-board--success .c-board__content,
.c-board--warning .c-board__content,
.c-board--danger .c-board__content {
    border-top: 3px solid #2ea1f8;
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.c-board--info .c-board__content {
    border-top-color: #2ea1f8; }

.c-board--success .c-board__content {
    border-top-color: #1bb934; }

.c-board--warning .c-board__content {
    border-top-color: #fd9a18; }

.c-board--danger .c-board__content {
    border-top-color: #ed1c24; }

/* 2. Task
   ========================================================================== */
.c-task {
    margin-bottom: 0.625rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff;
    cursor: -webkit-grabbing;
    cursor: grabbing;
    overflow: hidden; }
.c-task:last-of-type {
    margin-bottom: 0; }

.c-task__img {
    display: block; }

.c-task__content {
    margin: 0;
    padding: 0.9375rem 0.625rem 0.625rem;
    color: #354052;
    font-size: 0.875rem;
    font-weight: 600; }

.c-task__indicators {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 0 0.625rem 0.625rem; }

.c-task__indicator {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-right: 0.9375rem;
    color: #7f8fa4;
    font-size: 0.75rem;
    font-weight: 600; }
.c-task__indicator > i {
    display: inline-block;
    margin-right: 0.3125rem;
    color: #bbc5d5;
    font-size: 1rem; }

/**
 * Task style variations
 *
 * Add a border with a specific color to indicate the status of the task.
 */
.c-task--info,
.c-task--success,
.c-task--warning,
.c-task--danger {
    border-left: 3px solid #2ea1f8;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }

.c-task--info {
    border-left-color: #2ea1f8; }

.c-task--success {
    border-left-color: #1bb934; }

.c-task--warning {
    border-left-color: #fd9a18; }

.c-task--danger {
    border-left-color: #ed1c24; }

/* ==========================================================================
   #PLANS 
   ========================================================================== */
.c-plan {
    margin: 0 5px 30px;
    padding: 40px 20px 25px;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: transparent;
    text-align: left; }

.c-plan__img {
    position: absolute;
    top: -20px; }

.c-plan__title {
    margin: 0;
    color: #7f8fa4;
    font-size: 12px;
    text-transform: uppercase; }

.c-plan__price {
    margin: 0;
    font-size: 28px; }

.c-plan__note {
    margin: 0;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase; }

.c-plan__divider {
    display: block;
    width: 100%;
    height: 1px;
    margin: 15px 0;
    background-color: #e6eaee; }

.c-plan__feature {
    margin-bottom: 5px;
    color: #7f8fa4;
    font-size: 0.875rem; }

/* ==========================================================================
   #FILEITEM
   ========================================================================== */
.c-fileitem {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.9375rem 0;
    border-bottom: 1px solid #e6eaee; }
.c-fileitem:first-child {
    padding-top: 0; }
.c-fileitem:last-child {
    padding-bottom: 0;
    border-bottom: 0; }

.c-fileitem__content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }

.c-fileitem__img {
    max-width: 60px;
    margin-right: 20px;
    border-radius: 4px;
    overflow: hidden; }
.c-fileitem__img > img {
    display: block; }

.c-fileitem__name {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #354052;
    font-weight: 600; }
.c-fileitem__name > img {
    margin-right: 8px; }

.c-fileitem__date {
    color: #7f8fa4;
    font-size: 0.875rem; }
.c-fileitem__date i {
    margin: 0 5px; }

/* ==========================================================================
   #OVERVIEW CARD
   ========================================================================== */
/**
 * Not really a reusable component, but it fixes responsive issues with 
 * `Dashboard Overview` section in index.html page.
 */
@media (max-width: 768px) {
    .c-overview-card .u-border-right,
    .c-overview-card .u-border-left {
        border: 0 !important; } }

@media (max-width: 768px) {
    .c-overview-card__section {
        margin-bottom: 30px; }
    .c-overview-card__section h3 {
        font-size: 1.25rem; } }

/* ==========================================================================
   #INVOICE
   ========================================================================== */
.c-invoice {
    position: relative;
    padding: 6.25rem 6.25rem 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff;
    color: #354052;
    -webkit-box-shadow: 0 0 10px 1px rgba(53, 64, 82, 0.1);
    box-shadow: 0 0 10px 1px rgba(53, 64, 82, 0.1); }
@media (max-width: 768px) {
    .c-invoice {
        padding: 1.875rem; } }

/**
 * Invoice Header
 *
 * Contains:
 * - compnay's name & logo 
 * - invoice's title & date
 */
.c-invoice__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 3.125rem; }
@media (max-width: 768px) {
    .c-invoice__header {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-flow: column;
        flex-flow: column; } }

.c-invoice__brand {
    margin-bottom: 1.875rem; }
@media (max-width: 768px) {
    .c-invoice__brand {
        margin: 0; } }

.c-invoice__brand-img {
    max-width: 40px;
    margin-bottom: 0.9375rem; }

.c-invoice__brand-name {
    font-size: 1.5rem; }

.c-invoice__title {
    margin-top: 1.875rem; }
.c-invoice__title h4 {
    margin: 0; }

.c-invoice__date {
    color: #7f8fa4; }

/**
 * Invoice Details
 *
 */
.c-invoice__details {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 6.25rem; }

.c-invoice__company-name {
    margin-bottom: 0.9375rem;
    font-size: 1.25rem; }

.c-invoice__company-address {
    margin-bottom: 0.9375rem;
    color: #7f8fa4; }

/**
 * Invoice content
 */
.c-invoice__body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
@media (max-width: 768px) {
    .c-invoice__body {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-flow: column;
        flex-flow: column; } }

.c-invoice__desc {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 250px;
    flex: 0 1 250px;
    font-size: 1.25rem; }
@media (max-width: 768px) {
    .c-invoice__desc {
        -webkit-box-flex: 0;
        -ms-flex: 0;
        flex: 0;
        margin-bottom: 1.875rem; } }

.c-invoice__number {
    color: #7f8fa4;
    font-size: 0.875rem; }

.c-invoice__table {
    width: 100%; }
.c-invoice__table .c-table {
    margin-bottom: 0.625rem; }
@media (max-width: 768px) {
    .c-invoice__table .c-table {
        display: table;
        width: 100%; } }

.c-invoice__terms {
    color: #7f8fa4;
    font-size: 0.875rem; }

/**
 * Footer
 */
.c-invoice__footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-top: 6.25rem;
    padding-top: 1.875rem;
    border-top: 1px solid #e6eaee; }
@media (max-width: 576px) {
    .c-invoice__footer {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-flow: column;
        flex-flow: column;
        text-align: center; } }

.c-invoice__footer-brand {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }
@media (max-width: 576px) {
    .c-invoice__footer-brand {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-flow: column;
        flex-flow: column;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center; } }

.c-invoice__footer-brand img {
    max-width: 30px;
    margin-right: 0.625rem; }
@media (max-width: 576px) {
    .c-invoice__footer-brand img {
        margin-bottom: 0.625rem; } }

/* ==========================================================================
   #FEED
   ========================================================================== */
/**
 * User Activity Feed
 * 
 * This component is used to show a stream of the user's activities and it's 
 * used in user-profile.html page.
 */
.c-feed {
    position: relative;
    margin-bottom: 1.875rem;
    padding-left: 32px;
    overflow: hidden; }
.c-feed::before {
    position: absolute;
    top: 4px;
    left: 6px;
    width: 1px;
    height: 100%;
    background-color: #e6eaee;
    content: ""; }
.c-feed.has-icons {
    padding-left: 47px; }
.c-feed.has-icons::before {
    left: 14px; }

.c-feed__item {
    position: relative;
    margin-bottom: 25px;
    /**
           * The small circle (dot) before every item of the feed
           */ }
.c-feed__item::before {
    position: absolute;
    top: 4px;
    left: -32px;
    width: 12px;
    height: 12px;
    border-radius: 100%;
    background-color: #2ea1f8;
    content: ""; }
.c-feed__item:last-child {
    margin-bottom: 0; }
.c-feed__item:last-child::after {
    position: absolute;
    top: 16px;
    left: -32px;
    width: 12px;
    height: 100%;
    border-radius: 0;
    background-color: #fff;
    content: ""; }
.c-feed__item.has-icon::before {
    display: none; }
.c-feed__item.has-icon .c-feed__item-icon {
    display: block;
    position: absolute;
    top: 4px;
    left: -47px;
    width: 30px;
    height: 30px;
    border-radius: 100%;
    background-color: #2ea1f8;
    color: #fff;
    font-size: 0.875rem;
    line-height: 30px;
    text-align: center; }
.c-feed__item.has-icon:last-child::after {
    top: 34px;
    left: -47px;
    width: 30px; }

.c-feed__comment {
    display: inline-block;
    margin: 0.625rem 0;
    padding: 0.625rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fafbfc; }

.c-feed__gallery {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 0.625rem; }

.c-feed__gallery-item {
    width: 80px;
    height: 80px;
    margin: 0 0.625rem 0.625rem 0;
    border-radius: 4px;
    overflow: hidden; }

.c-feed__meta {
    display: block;
    color: #7f8fa4;
    font-size: 0.875rem; }

/* Style variants
   ========================================================================== */
.c-feed__item--fancy::before {
    background: #886ce6;
    background: -webkit-gradient(linear, left top, left bottom, from(#886ce6), to(#7d5be2));
    background: linear-gradient(to bottom, #886ce6, #7d5be2); }

.c-feed__item--info::before {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }

.c-feed__item--success::before {
    background: #39b54a;
    background: -webkit-gradient(linear, left top, left bottom, from(#39b54a), to(#34aa44));
    background: linear-gradient(to bottom, #39b54a, #34aa44); }

/* ==========================================================================
   #RATING
   ========================================================================== */
/**
 * A list of starts aligned horizontally to show the current rating of an item.
 *
 * EXAMPLE:
 *
 * <div class="c-rating">
 *      <i class="c-rating__icon is-active fa fa-star"></i>
 *      <i class="c-rating__icon is-active fa fa-star"></i>
 *      <i class="c-rating__icon is-active fa fa-star"></i>
 *      <i class="c-rating__icon is-active fa fa-star"></i>
 *      <i class="c-rating__icon fa fa-star"></i>
 *  </div>
 */
.c-rating {
    display: block; }

.c-rating__icon {
    color: #BBBFC5;
    font-size: 0.75rem; }
.c-rating__icon.is-active {
    color: #FDBC0E; }

/* ==========================================================================
   #MAP
   ========================================================================== */
/**
 * Map is powered by jqvmap plugin, these labels are added manually.
 */
.c-map__visual {
    margin-bottom: 1.875rem; }

.c-map__labels--left {
    margin-right: 0.9375rem;
    margin-left: 3.125rem; }

.c-map__labels--right {
    margin-right: 3.125rem;
    margin-left: 0.9375rem; }

@media (max-width: 1200px) {
    .c-map__labels {
        margin-right: 0;
        margin-left: 0; } }

.c-map__label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 0.625rem; }
.c-map__label:last-child {
    margin-bottom: 0; }

.c-map__country,
.c-map__number {
    color: #354052; }

/* ==========================================================================
   #CANDIDATE
   ========================================================================== */
/**
 * Used in candidates.html page
 */
.c-candidate {
    margin-bottom: 1.875rem;
    padding: 0.9375rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

/**
 * 1. Remove weird extra space.
 */
.c-candidate__cover {
    max-height: 150px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    font-size: 0;
    /* [1] */
    overflow: hidden; }

.c-candidate__info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    top: -0.9375rem;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    padding-bottom: 0.9375rem;
    border-bottom: 1px solid #e6eaee; }

.c-candidate__avatar {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-right: 0.9375rem;
    margin-left: 0.625rem;
    border: 4px solid #fff;
    border-radius: 100%;
    overflow: hidden; }

.c-candidate__meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%; }

.c-candidate__title {
    margin: 0;
    font-size: 1rem; }

.c-candidate__country {
    display: block;
    color: #7f8fa4; }
.c-candidate__country i {
    margin-right: 0.625rem; }

.c-candidate__actions {
    -ms-flex-item-align: center;
    align-self: center;
    margin-right: 0.625rem; }
.c-candidate__actions a {
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    color: #7f8fa4;
    opacity: .5; }
.c-candidate__actions a:last-child {
    margin-left: 0.3125rem; }
.c-candidate__actions a:hover {
    opacity: 1; }

.c-candidate__footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between; }

.c-candidate__status {
    margin-right: 5%;
    font-size: 0.875rem; }

/* ==========================================================================
   #STAGE
   ========================================================================== */
/**
 * Stage component is used to describe the project you are working on based on
 * multiple stages. This component is used in project-overview.html page.
 */
.c-stage {
    margin-bottom: 1.875rem;
    border: 1px solid #e6eaee;
    border-radius: 4px;
    background-color: #fff; }

.c-stage__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 0.9375rem 1.875rem;
    border-bottom: 1px solid #e6eaee;
    cursor: pointer; }
.c-stage__header:hover {
    text-decoration: none; }

.c-stage__header-img {
    max-width: 60px;
    margin-right: 20px;
    border-radius: 4px;
    overflow: hidden; }

.c-stage__icon {
    width: 30px;
    height: 30px;
    margin-right: 0.625rem;
    border: #e6eaee solid 1px;
    border-radius: 100%;
    color: #7f8fa4;
    font-size: 14px;
    line-height: 30px;
    text-align: center; }

.c-stage__panel {
    border-bottom: 1px solid #e6eaee;
    /**
           * Bootstrap classes used to show and hide sidebar submenu.
           * This behaviour depends on collapse.js plugin.
           */ }
.c-stage__panel.collapse {
    display: none; }
.c-stage__panel.collapse.show {
    display: block; }
.c-stage__panel.collapsing {
    position: relative;
    height: 0;
    -webkit-transition: height 0.3s;
    transition: height 0.3s;
    overflow: hidden; }
.c-stage__panel--mute {
    background-color: #fafbfc; }

.c-stage__label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0.9375rem 1.875rem;
    background-color: #1bb934;
    color: #fff; }

.c-stage__label-icon {
    margin-right: 0.625rem;
    font-size: 1.75rem; }

.c-stage__label-title {
    color: #fff; }

/* ==========================================================================
   #COUNTER NAV
   ========================================================================== */
/**
 * Counter nav is a different style of navigation, every navigation link has 
 * an icon (that contains current number) before it. 
 *
 * This component is used in project-overview.html page.
 */
.c-counter-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }

.c-counter-nav__title {
    margin-right: 0.625rem;
    color: #7f8fa4; }

.c-counter-nav__item {
    margin-right: 1.25rem; }

.c-counter-nav__link {
    color: #7f8fa4;
    font-size: 14px; }
.c-counter-nav__link:hover {
    text-decoration: none; }
.c-counter-nav__link.is-active {
    color: #354052; }

.c-counter-nav__counter {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    margin-right: 10px;
    border: 1px solid #e6eaee;
    border-radius: 100%;
    background-color: #fafbfc;
    color: #7f8fa4;
    text-align: center; }
.c-counter-nav__link.is-active .c-counter-nav__counter {
    background: #2EA1F8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2EA1F8), to(#1990EA));
    background: linear-gradient(to bottom, #2EA1F8, #1990EA);
    border-color: #2EA1F8;
    color: #fff; }

/**
 * Inverse 
 *
 * Use `.c-counter-nav--inverse` when using this component on a dark background.
 */
.c-counter-nav--inverse .c-counter-nav__counter {
    border-radius: 100%;
    border-color: #313C4D;
    background-color: #313C4D;
    color: #828A96;
    text-align: center; }
.c-counter-nav__link.is-active .c-counter-nav--inverse .c-counter-nav__counter {
    color: #fff; }

.c-counter-nav--inverse .c-counter-nav__link.is-active {
    color: #fff; }

/* ==========================================================================
   #CHAT DIALOGUE
   ========================================================================== */
/**
 * A pop-up dialogue for different purposes like chat and customer support.
 * This component is used in chat-dialogue.html page.
 */
.c-chat-dialogue {
    position: fixed;
    right: 1.875rem;
    bottom: 1.875rem; }

.c-chat-dialogue__btn {
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 100%;
    border-color: #1a91eb;
    color: #fff;
    font-size: 1.5rem;
    -webkit-box-shadow: 0 2px 7px 0 rgba(34, 44, 60, 0.4);
    box-shadow: 0 2px 7px 0 rgba(34, 44, 60, 0.4);
    cursor: pointer; }
.c-chat-dialogue__btn .c-chat-dialogue__btn-close {
    display: none; }
.c-chat-dialogue__btn.is-open .c-chat-dialogue__btn-open {
    display: none; }
.c-chat-dialogue__btn.is-open .c-chat-dialogue__btn-close {
    display: block; }

.c-chat-dialogue__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    padding: 1.875rem;
    border-bottom: 1px solid #e6eaee; }

.c-chat-dialogue__team {
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 35%; }
.c-chat-dialogue__team .c-avatar {
    position: absolute;
    top: 0;
    left: 0; }
.c-chat-dialogue__team .c-avatar:nth-child(2) {
    -webkit-transform: translateX(20px);
    transform: translateX(20px); }
.c-chat-dialogue__team .c-avatar:nth-child(3) {
    -webkit-transform: translateX(40px);
    transform: translateX(40px); }

.c-chat-dialogue__body {
    position: absolute;
    right: 0;
    bottom: 70px;
    width: 350px;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    border-radius: 4px;
    background-color: #fff;
    -webkit-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.35);
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.35);
    opacity: 0;
    overflow: hidden;
    visibility: hidden; }
.c-chat-dialogue__body.is-active {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    visibility: visible; }

.c-chat-dialogue__messages {
    height: 400px;
    padding: 0 1.25rem;
    background-color: #fafbfc;
    overflow-y: auto; }

.c-chat-dialogue__message {
    padding: 10px 20px; }

.c-chat-dialogue__message-content {
    position: relative;
    max-width: 80%;
    padding: 15px 20px;
    border-radius: 8px;
    color: #fff;
    font-size: 0.875rem;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea);
    word-wrap: break-word; }
.c-chat-dialogue__message-content::before {
    position: absolute;
    top: 30%;
    right: 100%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border: 6px solid transparent;
    border-right-color: #2ea1f8;
    content: " "; }

.c-chat-dialogue__message--self {
    text-align: right; }
.c-chat-dialogue__message--self .c-chat-dialogue__message-content {
    margin-left: auto;
    border: 1px solid #e6eaee;
    background: #fff;
    color: #354052; }
.c-chat-dialogue__message--self .c-chat-dialogue__message-content::before {
    left: 100%;
    border: 6px solid transparent;
    border-left-color: #fff; }

.c-chat-dialogue__footer {
    padding: 1.25rem;
    border-top: 1px solid #e6eaee;
    background-color: #fff; }

/* ==========================================================================
   #LOGIN HORIZONTAL
   ========================================================================== */
.c-login-horizontal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%; }
.c-login-horizontal .c-login__content-wrapper,
.c-login-horizontal .c-login__content-image {
    width: 50%; }
@media (max-width: 768px) {
    .c-login-horizontal .c-login__content-wrapper,
    .c-login-horizontal .c-login__content-image {
        width: 100%; } }
.c-login-horizontal .c-login__header {
    position: relative;
    padding: 1.875rem 0 0;
    padding-left: 1.875rem;
    border: 0;
    background-color: transparent; }
@media (max-width: 768px) {
    .c-login-horizontal .c-login__header {
        padding-top: 3.125rem; } }
.c-login-horizontal .c-login__icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    top: -35px;
    right: 0;
    left: 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    margin: 0 auto;
    border-radius: 100%;
    color: #fff;
    font-size: 1.25rem;
    background: #2ea1f8;
    background: -webkit-gradient(linear, left top, left bottom, from(#2ea1f8), to(#1990ea));
    background: linear-gradient(to bottom, #2ea1f8, #1990ea); }
.c-login-horizontal .c-login__icon--left {
    right: initial;
    left: 30px; }
.c-login-horizontal .c-login__icon--rounded {
    top: -25px;
    width: 45px;
    height: 45px;
    border-radius: 4px; }
.c-login-horizontal .c-login__title {
    margin: 0;
    font-size: 24px;
    text-align: left; }
.c-login-horizontal .c-login__content {
    padding: 0.9375rem 1.875rem 1.875rem 1.875rem; }
.c-login-horizontal .c-login__content-image {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 1.875rem;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: #49515D;
    color: #fff;
    overflow: hidden; }
.c-login-horizontal .c-login__content-image img {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: .2; }
.c-login-horizontal .c-login__content-image h3,
.c-login-horizontal .c-login__content-image p {
    color: #fff; }
.c-login-horizontal .c-login__content-image p {
    font-size: 1rem;
    opacity: .9; }
@media (max-width: 768px) {
    .c-login-horizontal .c-login__content-image {
        display: none; } }

/* ==========================================================================
   #SIDEBAR MINIMUZED
   ========================================================================== */
/**
 * This file contains all customization for minimized-style sidebar.
 */
/**
 * This sidebar object
 */
@media (max-width: 992px) {
    .c-sidebar.is-minimized .c-sidebar__icon {
        margin-right: 15px; } }

@media (min-width: 992px) {
    .o-page__sidebar.is-minimized {
        width: 70px; }
    .o-page__sidebar.is-minimized + .o-page__content {
        margin-left: 70px;
        -webkit-transition: margin .3s;
        transition: margin .3s; }
    /**
           * On Hover
           */
    .o-page__sidebar.is-minimized:hover {
        width: 250px; }
    .o-page__sidebar.is-minimized:hover + .o-page__content {
        margin-left: 250px; }
    /**
           * This sidebar component
           */
    .c-sidebar.is-minimized {
        width: 70px;
        overflow-x: hidden;
        -webkit-transition: width .3s;
        transition: width .3s; }
    .c-sidebar.is-minimized .c-sidebar__brand {
        padding: 10px; }
    .c-sidebar.is-minimized .c-sidebar__brand-img {
        margin-right: 15px; }
    .c-sidebar.is-minimized .c-sidebar__item {
        border-bottom: 1px solid #2a3547; }
    .c-sidebar.is-minimized .c-sidebar__link {
        padding: 0; }
    .c-sidebar.is-minimized .c-sidebar__icon {
        display: block;
        min-width: 70px;
        padding: 15px 0;
        font-size: 20px;
        text-align: center; }
    .c-sidebar.is-minimized .c-sidebar__item.has-submenu > .c-sidebar__link::after {
        display: none;
        top: 20px; }
    /**
           * On Hover
           */
    .c-sidebar.is-minimized:hover {
        width: 250px; }
    .c-sidebar.is-minimized:hover .c-sidebar__brand-text {
        display: block; }
    .c-sidebar.is-minimized:hover .c-sidebar__brand-img {
        margin-right: 15px; }
    .c-sidebar.is-minimized:hover .c-sidebar__title {
        display: block; }
    .c-sidebar.is-minimized:hover .c-sidebar__item.has-submenu > .c-sidebar__link::after {
        display: block; } }

/* ==========================================================================
   #TYPOGRAPHY UTILITIES
   ========================================================================== */
/**
 * Headings
 *
 * Redefine all of our basic heading styles against utility classes so as to
 * allow for double stranded heading hierarchy, e.g. we semantically need an H2,
 * but we want it to be sized like an H1:
 *
 *   <h2 class="u-h1"></h2>
 *
 */
.u-h1 {
    font-size: 2.25rem; }

.u-h2 {
    font-size: 1.75rem; }

.u-h3 {
    font-size: 1.5rem; }

.u-h4 {
    font-size: 1.25rem; }

.u-h5 {
    font-size: 1.125rem; }

.u-h6 {
    font-size: 1rem; }

/**
 * Text Utilitites
 */
/**
 * Text Colors
 */
.u-text-mute {
    color: #7f8fa4 !important; }

.u-text-danger {
    color: #ed1c24 !important; }

.u-text-success {
    color: #45B854 !important; }

.u-text-dark {
    color: #354052; }

.u-text-white {
    color: #fff; }

/**
  * Text Sizes
  */
.u-text-tiny {
    font-size: 0.625rem !important; }

.u-text-xsmall {
    font-size: 0.75rem !important; }

.u-text-small {
    font-size: 0.875rem !important; }

.u-text-large {
    font-size: 1rem !important; }

.u-text-big {
    font-size: 3.375rem;
    font-weight: 600;
    line-height: 48px; }

/**
  * Font Weights
  */
.u-text-bold {
    font-weight: 600 !important; }

/**
  * Aligning Text
  */
.u-text-right {
    text-align: right !important; }

.u-text-left {
    text-align: left !important; }

.u-text-center {
    text-align: center !important; }

/**
 * Transforming Text
 */
.u-text-uppercase {
    text-transform: uppercase !important; }

.u-text-lowercase {
    text-transform: lowercase !important; }

.u-text-capitalize {
    text-transform: capitalize !important; }

/* ==========================================================================
   #FLOATS UTILITIES
   ========================================================================== */
.u-float-left {
    float: left !important; }

.u-float-right {
    float: right !important; }

/* ==========================================================================
   #CLEARFIX UTILITIY
   ========================================================================== */
/**
 * Clear floated elements by adding `u-clearfix` to the parent element. 
 */
.u-clearfix::after {
    display: table !important;
    clear: both !important;
    content: "" !important; }

/* ==========================================================================
   #SPACING UTILITIES
   ========================================================================== */
/**
 * Spacing utilities are used to provide spacing between components using 
 * margins or inside the components using paddings. Different sizes allow you 
 * to easily construct consistent layouts of components.
 */
/**
 * Margins
 *
 * m  -> all sides
 * mt -> margin-top
 * mr -> margin-right 
 * mb -> margin-bottom
 * ml -> margin-left
 * mv -> margin-top & margin-bottom (vertically)
 * mh -> margin-right & margin-left (horizontaly)
 *
 */
.u-m-zero {
    margin: 0 !important; }

.u-mt-zero {
    margin-top: 0 !important; }

.u-mr-zero {
    margin-right: 0 !important; }

.u-mb-zero {
    margin-bottom: 0 !important; }

.u-ml-zero {
    margin-left: 0 !important; }

.u-mv-zero {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

.u-mh-zero {
    margin-right: 0 !important;
    margin-left: 0 !important; }

.u-m-auto {
    margin: auto !important; }

.u-mt-auto {
    margin-top: auto !important; }

.u-mr-auto {
    margin-right: auto !important; }

.u-mb-auto {
    margin-bottom: auto !important; }

.u-ml-auto {
    margin-left: auto !important; }

.u-mv-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

.u-mh-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

.u-m-xsmall {
    margin: 0.625rem !important; }

.u-mt-xsmall {
    margin-top: 0.625rem !important; }

.u-mr-xsmall {
    margin-right: 0.625rem !important; }

.u-mb-xsmall {
    margin-bottom: 0.625rem !important; }

.u-ml-xsmall {
    margin-left: 0.625rem !important; }

.u-mv-xsmall {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important; }

.u-mh-xsmall {
    margin-right: 0.625rem !important;
    margin-left: 0.625rem !important; }

.u-m-small {
    margin: 0.9375rem !important; }

.u-mt-small {
    margin-top: 0.9375rem !important; }

.u-mr-small {
    margin-right: 0.9375rem !important; }

.u-mb-small {
    margin-bottom: 0.9375rem !important; }

.u-ml-small {
    margin-left: 0.9375rem !important; }

.u-mv-small {
    margin-top: 0.9375rem !important;
    margin-bottom: 0.9375rem !important; }

.u-mh-small {
    margin-right: 0.9375rem !important;
    margin-left: 0.9375rem !important; }

.u-m-medium {
    margin: 1.875rem !important; }

.u-mt-medium {
    margin-top: 1.875rem !important; }

.u-mr-medium {
    margin-right: 1.875rem !important; }

.u-mb-medium {
    margin-bottom: 1.875rem !important; }

.u-ml-medium {
    margin-left: 1.875rem !important; }

.u-mv-medium {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important; }

.u-mh-medium {
    margin-right: 1.875rem !important;
    margin-left: 1.875rem !important; }

.u-m-large {
    margin: 3.125rem !important; }

.u-mt-large {
    margin-top: 3.125rem !important; }

.u-mr-large {
    margin-right: 3.125rem !important; }

.u-mb-large {
    margin-bottom: 3.125rem !important; }

.u-ml-large {
    margin-left: 3.125rem !important; }

.u-mv-large {
    margin-top: 3.125rem !important;
    margin-bottom: 3.125rem !important; }

.u-mh-large {
    margin-right: 3.125rem !important;
    margin-left: 3.125rem !important; }

.u-m-xlarge {
    margin: 6.25rem !important; }

.u-mt-xlarge {
    margin-top: 6.25rem !important; }

.u-mr-xlarge {
    margin-right: 6.25rem !important; }

.u-mb-xlarge {
    margin-bottom: 6.25rem !important; }

.u-ml-xlarge {
    margin-left: 6.25rem !important; }

.u-mv-xlarge {
    margin-top: 6.25rem !important;
    margin-bottom: 6.25rem !important; }

.u-mh-xlarge {
    margin-right: 6.25rem !important;
    margin-left: 6.25rem !important; }

/**
 * Paddings
 *
 * p  -> all sides
 * pt -> padding-top
 * pr -> padding-right 
 * pb -> padding-bottom
 * pl -> padding-left
 * pv -> padding-top & padding-bottom (vertically)
 * ph -> padding-right & padding-left (horizontaly)
 *
 */
.u-p-zero {
    padding: 0 !important; }

.u-pt-zero {
    padding-top: 0 !important; }

.u-pr-zero {
    padding-right: 0 !important; }

.u-pb-zero {
    padding-bottom: 0 !important; }

.u-pl-zero {
    padding-left: 0 !important; }

.u-pv-zero {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

.u-ph-zero {
    padding-right: 0 !important;
    padding-left: 0 !important; }

.u-p-auto {
    padding: auto !important; }

.u-pt-auto {
    padding-top: auto !important; }

.u-pr-auto {
    padding-right: auto !important; }

.u-pb-auto {
    padding-bottom: auto !important; }

.u-pl-auto {
    padding-left: auto !important; }

.u-pv-auto {
    padding-top: auto !important;
    padding-bottom: auto !important; }

.u-ph-auto {
    padding-right: auto !important;
    padding-left: auto !important; }

.u-p-xsmall {
    padding: 0.625rem !important; }

.u-pt-xsmall {
    padding-top: 0.625rem !important; }

.u-pr-xsmall {
    padding-right: 0.625rem !important; }

.u-pb-xsmall {
    padding-bottom: 0.625rem !important; }

.u-pl-xsmall {
    padding-left: 0.625rem !important; }

.u-pv-xsmall {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important; }

.u-ph-xsmall {
    padding-right: 0.625rem !important;
    padding-left: 0.625rem !important; }

.u-p-small {
    padding: 0.9375rem !important; }

.u-pt-small {
    padding-top: 0.9375rem !important; }

.u-pr-small {
    padding-right: 0.9375rem !important; }

.u-pb-small {
    padding-bottom: 0.9375rem !important; }

.u-pl-small {
    padding-left: 0.9375rem !important; }

.u-pv-small {
    padding-top: 0.9375rem !important;
    padding-bottom: 0.9375rem !important; }

.u-ph-small {
    padding-right: 0.9375rem !important;
    padding-left: 0.9375rem !important; }

.u-p-medium {
    padding: 1.875rem !important; }

.u-pt-medium {
    padding-top: 1.875rem !important; }

.u-pr-medium {
    padding-right: 1.875rem !important; }

.u-pb-medium {
    padding-bottom: 1.875rem !important; }

.u-pl-medium {
    padding-left: 1.875rem !important; }

.u-pv-medium {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important; }

.u-ph-medium {
    padding-right: 1.875rem !important;
    padding-left: 1.875rem !important; }

.u-p-large {
    padding: 3.125rem !important; }

.u-pt-large {
    padding-top: 3.125rem !important; }

.u-pr-large {
    padding-right: 3.125rem !important; }

.u-pb-large {
    padding-bottom: 3.125rem !important; }

.u-pl-large {
    padding-left: 3.125rem !important; }

.u-pv-large {
    padding-top: 3.125rem !important;
    padding-bottom: 3.125rem !important; }

.u-ph-large {
    padding-right: 3.125rem !important;
    padding-left: 3.125rem !important; }

.u-p-xlarge {
    padding: 6.25rem !important; }

.u-pt-xlarge {
    padding-top: 6.25rem !important; }

.u-pr-xlarge {
    padding-right: 6.25rem !important; }

.u-pb-xlarge {
    padding-bottom: 6.25rem !important; }

.u-pl-xlarge {
    padding-left: 6.25rem !important; }

.u-pv-xlarge {
    padding-top: 6.25rem !important;
    padding-bottom: 6.25rem !important; }

.u-ph-xlarge {
    padding-right: 6.25rem !important;
    padding-left: 6.25rem !important; }

/* ==========================================================================
   #COLORS UTILITIES
   ========================================================================== */
/**
 * Set the text color of elements.
 */
.u-color-success {
    color: #1bb934 !important; }

.u-color-info {
    color: #2ea1f8 !important; }

.u-color-danger {
    color: #ed1c24 !important; }

.u-color-warning {
    color: #fd9a18 !important; }

.u-color-primary {
    color: #354052 !important; }

.u-color-secondary {
    color: #fff !important; }

.u-color-white {
    color: #fff !important; }

/**
  * Set the background color of elements.
  */
.u-bg-success {
    background: #1bb934 !important;
    background-color: #1bb934 !important; }

.u-bg-info {
    background: #1a91eb !important;
    background-color: #1a91eb !important; }

.u-bg-danger {
    background: #ed1c24 !important;
    background-color: #ed1c24 !important; }

.u-bg-fancy {
    background: #a388e3 !important;
    background-color: #a388e3 !important; }

.u-bg-warning {
    background: #fd9a18 !important;
    background-color: #fd9a18 !important; }

.u-bg-primary {
    background: #475364 !important;
    background-color: #475364 !important; }

.u-bg-secondary {
    background: #fafbfc !important;
    background-color: #fafbfc !important; }

.u-bg-white {
    background: #fff !important;
    background-color: #fff !important; }

/**
  * Use brand-color utilities to apply text colors and background colors to 
  * brand-related components like social icons.
  */
/**
 * Brand text colors
 */
.u-color-facebook {
    color: #2492E4 !important; }

.u-color-twitter {
    color: #0AF !important; }

.u-color-pinterest {
    color: #E30F27 !important; }

.u-color-behance {
    color: #187AC6 !important; }

.u-color-dribbble {
    color: #ea4c89 !important; }

.u-color-linkedin {
    color: #222 !important; }

/**
 * Brand background colors
 *
 * 1. If a component has border, we change its border-color to match with
 *    the brand background-color.
 */
.u-bg-facebook {
    border-color: #2492E4 !important;
    /* [1] */
    background: #2492E4 !important; }

.u-bg-twitter {
    border-color: #0AF !important;
    background: #0AF !important; }

.u-bg-pinterest {
    border-color: #E30F27 !important;
    background: #E30F27 !important; }

.u-bg-behance {
    border-color: #187AC6 !important;
    background: #187AC6 !important; }

.u-bg-dribbble {
    border-color: #ea4c89 !important;
    background: #ea4c89 !important; }

.u-bg-linkedin {
    border-color: #222;
    background-color: #222 !important; }

/* ==========================================================================
   #DISPLAY UTILITIES
   ========================================================================== */
.u-block {
    display: block !important; }

.u-inline-block {
    display: inline-block !important; }

.u-inline {
    display: inline !important; }

/* ==========================================================================
   #BORDER UTILITIES
   ========================================================================== */
/**
 * Use border utilities to add or remove borders from certain components 
 * to prevent border collapsing.
 */
/**
 * Add borders
 */
.u-border-top {
    border-top: 1px solid #e6eaee !important; }

.u-border-right {
    border-right: 1px solid #e6eaee !important; }

.u-border-bottom {
    border-bottom: 1px solid #e6eaee !important; }

.u-border-left {
    border-left: 1px solid #e6eaee !important; }

/**
 * Remove borders
 */
.u-border-zero {
    border: 0 !important; }

.u-border-top-zero {
    border-top: 0 !important; }

.u-border-right-zero {
    border-right: 0 !important; }

.u-border-bottom-zero {
    border-bottom: 0 !important; }

.u-border-left-zero {
    border-left: 0 !important; }

/**
 * Border radius
 */
.u-border-rounded {
    border-radius: 4px;
    overflow: hidden; }

.u-border-circle {
    border-radius: 100%;
    overflow: hidden; }

/* ==========================================================================
   #FLEX UTILITIES
   ========================================================================== */
.u-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important; }

.u-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important; }

.u-justify-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important; }

.u-justify-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important; }

.u-justify-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important; }

.u-justify-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important; }

.u-justify-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important; }

.u-align-items-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important; }

.u-align-items-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important; }

.u-align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important; }

.u-align-items-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important; }

.u-align-items-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important; }

.u-align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important; }

.u-align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important; }

.u-align-self-center {
    -ms-flex-item-align: center !important;
    align-self: center !important; }

.u-align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important; }

.u-align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important; }

.u-flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important; }

.u-flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important; }

/* ==========================================================================
   #RESPONSIVE UTILITIES
   ========================================================================== */
/**
 * Hide content at all viewports.
 */
.u-hidden {
    display: none !important; }

/**
 * Hide visually but make it accessible to screenreaders.
 */
.u-hidden-visually {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    margin: -1px !important;
    padding: 0 !important;
    border: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    clip: rect(0 0 0 0) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important; }

/**
 * Use `u-hidden-down@{breakpoint}` to hide content from a specific breakpoint 
 * and smaller viewports.
 *
 * 1. Hide on mobile.
 * 2. Hide on mobile and tablet.
 * 3. Hide on mobile, tablet and desktop.
 * 4. Hide on mobile, tablet, desktop and wide. 
 *
 * NOTE: u-hidden@wide : still visible on viewports larger than wide breakpoint.
 */
@media (max-width: 576px) {
    .u-hidden-down\@mobile {
        /* [1] */
        display: none !important; } }

@media (max-width: 768px) {
    .u-hidden-down\@tablet {
        /* [2] */
        display: none !important; } }

@media (max-width: 992px) {
    .u-hidden-down\@desktop {
        /* [3] */
        display: none !important; } }

@media (max-width: 1200px) {
    .u-hidden-down\@wide {
        /* [4] */
        display: none !important; } }

/**
 * Use `u-hidden-up@{breakpoint}` to hide content from a specific breakpoint 
 * and larger viewports.
 *
 * 1. Hide on viewports larger than mobile.
 * 2. Hide on viewports larger than tablet.
 * 3. Hide on viewports larger than desktop.
 * 4. Hide on viewports larger than wide.
 *
 * NOTE: u-hidden@wide : still visible on viewports larger than wide breakpoint.
 */
@media (min-width: 576px) {
    .u-hidden-up\@mobile {
        /* [1] */
        display: none !important; } }

@media (min-width: 768px) {
    .u-hidden-up\@tablet {
        /* [2] */
        display: none !important; } }

@media (min-width: 992px) {
    .u-hidden-up\@desktop {
        /* [3] */
        display: none !important; } }

@media (min-width: 1200px) {
    .u-hidden-up\@wide {
        /* [4] */
        display: none !important; } }

/* ==========================================================================
   #OPACITY UTILITIES
   ========================================================================== */
/**
 * Use opacity utilities to specify the level of transparency of an element
 */
.u-opacity-heavy {
    opacity: 0.75 !important; }

.u-opacity-medium {
    opacity: 0.5 !important; }

.u-opacity-light {
    opacity: 0.25 !important; }

.u-opacity-zero {
    opacity: 0; }

/* ==========================================================================
   #SIZES UTILITIES
   ========================================================================== */
/**
 * Widths
 */
.u-width-25 {
    width: 25% !important; }

.u-width-50 {
    width: 50% !important; }

.u-width-75 {
    width: 75% !important; }

.u-width-100 {
    width: 100% !important; }

/**
 * Heights
 */
.u-height-25 {
    height: 25% !important; }

.u-height-50 {
    height: 50% !important; }

.u-height-75 {
    height: 75% !important; }

.u-height-100 {
    height: 100% !important; }

/* ==========================================================================
   #OVERFLOW UTILITIES
   ========================================================================== */
/**
 * Overflow
 */
.u-overflow-auto {
    overflow: auto; }

.u-overflow-scroll {
    overflow: scroll; }

/**
 * Horizontal Overflow
 */
.u-overflow-x-auto {
    overflow: auto; }

.u-overflow-x-scroll {
    overflow: scroll; }

/**
 * Vericall Overflow
 */
.u-overflow-y-auto {
    overflow: auto; }

.u-overflow-y-scroll {
    overflow: scroll; }

/* ==========================================================================
   #BOOTSTRAP UTILITIES
   ========================================================================== */
/**
 * These classes are used by bootstrap plugins.
 */
.fade {
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    opacity: 0; }
.fade.show {
    opacity: 1; }
/*FOR ERRROR MEAAGE*/
.help-block strong {
    color: red !important;
}
input.has-error,select.has-error,textarea.has-error {
    border-color: red !important;
}
.red{
    background-color: #f44336!important;
}
